# Technical Context

## 1. Core Technologies

### Backend
- **Language**: Go (Golang) 1.21+
- **Web Framework**: Fiber (Fast, Express-inspired web framework)
- **Database**: PostgreSQL 13+
- **ORM**: GORM (Go ORM library)
- **Caching/Messaging**: Redis
- **Authentication**: JWT (JSON Web Tokens)
- **Authorization**: Custom role-based access control (with potential future migration to Casbin)
- **Configuration**: Koanf (for flexible configuration management)
- **Logging**: Zerolog (structured, leveled logging)
- **Dependency Injection**: Manual (for explicit dependency management)
- **Migrations**: Golang-migrate (database schema versioning)
- **API Documentation**: Swag (for OpenAPI/Swagger generation)

### Frontend (Planned/Conceptual)
- **Framework**: Next.js (React framework for production)
- **Language**: TypeScript
- **Styling**: Tailwind CSS (utility-first CSS framework)
- **State Management**: Zustand or React Context API
- **Data Fetching**: React Query or SWR
- **Deployment**: Vercel (for Next.js applications)
- **PWA**: Progressive Web App capabilities

## 2. Development Environment

### Local Setup
- **Docker/Docker Compose**: For local database (PostgreSQL) and Redis instances.
- **Go Modules**: For dependency management.
- **Makefile**: For common development tasks (e.g., `make run`, `make test`, `make migrate-up`).
- **Environment Variables**: For sensitive configurations (e.g., database credentials, JWT secrets).

### Tools
- **Editor**: VS Code (with Go extensions, Docker, GitLens)
- **Linter**: `golangci-lint` (for Go code quality and style)
- **Testing**: Go's built-in testing framework
- **API Client**: cURL, Postman, or Insomnia for API testing.

## 3. Project Structure

```
pantry-pal/
├── cmd/api/           # Main application entry point
├── internal/
│   ├── core/          # Business logic (domain, usecases, services)
│   │   ├── domain/    # Core entities, interfaces, and business rules
│   │   └── usecases/  # Application-specific business logic (interactors)
│   └── infra/         # Infrastructure concerns (persistence, web, auth, config, logger)
│       ├── auth/      # Authentication and authorization implementations
│       ├── config/    # Configuration loading
│       ├── errors/    # Custom error types
│       ├── logger/    # Logging setup
│       ├── persistence/ # Database and other persistence implementations
│       ├── redis/     # Redis client and related services
│       └── web/       # HTTP server, handlers, and middleware
├── docs/              # Old API documentation (moved to memory-bank/features)
├── memory-bank/       # Project documentation, rules, and feature guides
│   ├── features/      # Detailed feature documentation
│   │   ├── EXPIRATION_TRACKING_EXAMPLES.md
│   │   ├── IDEMPOTENCY_MIDDLEWARE_EXAMPLES.md
│   │   ├── RECIPE_CONSUMPTION_EXAMPLES.md
│   │   ├── RECIPE_MANAGEMENT_SYSTEM.md
│   │   ├── SHOPPING_LIST_API.md
│   │   ├── SHOPPING_LIST_EXAMPLES.md
│   │   ├── SHOPPING_LIST_FEATURE.md
│   │   ├── SHOPPING_LIST_IMPLEMENTATION.md
│   │   └── SWAGGER_API_DOCUMENTATION.md
│   ├── activeContext.md
│   ├── productContext.md
│   ├── projectbrief.md
│   ├── progress.md
│   ├── RULES_AND_GUIDES.md
│   └── systemPatterns.md
├── scripts/           # Utility scripts (e.g., init-db.sql, test-api.sh)
├── alembic/           # Python-based database migrations (for initial setup, now using golang-migrate)
├── go.mod             # Go module definition
├── go.sum             # Go module checksums
├── config.yaml        # Application configuration
├── .env.example       # Example environment variables
├── .gitignore         # Git ignore file
├── README.md          # Project README
└── TODO_FEATURES.md   # List of features to be implemented (to be integrated into memory bank)
```

## 4. Continuous Integration/Continuous Deployment (CI/CD)
- **Pipeline (Conceptual)**: GitHub Actions or GitLab CI/CD.
- **Stages**:
    - **Build**: Compile Go application.
    - **Test**: Run unit and integration tests.
    - **Lint**: Run `golangci-lint` checks.
    - **Security Scan**: Static analysis for vulnerabilities.
    - **Docker Build**: Build Docker images for backend.
    - **Deployment**: Deploy to cloud provider (e.g., Kubernetes, AWS ECS).

## 5. Monitoring & Observability
- **Metrics**: Prometheus (for application and system metrics).
- **Tracing**: OpenTelemetry (for distributed tracing).
- **Alerting**: Alertmanager (for critical alerts).
- **Dashboarding**: Grafana (for visualizing metrics and logs).

## 6. Security Considerations
- **JWT**: Secure token generation and validation.
- **HTTPS**: All API communication over TLS.
- **Input Validation**: Comprehensive validation on all API inputs.
- **Rate Limiting**: Protect against abuse and DDoS attacks.
- **Dependency Scanning**: Regularly scan for vulnerable dependencies.
- **Secrets Management**: Environment variables or dedicated secrets management service.

## 7. Performance Considerations
- **Database Indexing**: Proper indexing for frequently queried columns.
- **Caching**: Redis for hot data and idempotency.
- **Optimized Queries**: Efficient SQL queries and ORM usage.
- **Concurrency**: Go's goroutines and channels for concurrent processing.
- **Load Testing**: Regular load testing to identify bottlenecks.

## 8. Future Technical Directions
- **GraphQL API**: Alternative API layer for flexible data fetching.
- **Message Queues**: Kafka or RabbitMQ for asynchronous processing and inter-service communication.
- **Serverless Functions**: For specific event-driven tasks.
- **Machine Learning**: For advanced analytics and smart suggestions (e.g., predictive expiration).