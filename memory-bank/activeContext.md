# Active Context

## Current Project State
Pantry Pal has completed 75% of core features, with 5% in progress and 20% remaining. The project is currently focused on completing high-priority MVP features as outlined in Sprint 1, while strictly adhering to established development standards and documentation practices.

## Recent Changes
- Core feature implementation complete.
- All current tests passing (37/37).
- Fixed refresh token hashing issue:
  - Changed from bcrypt to SHA-256 for refresh tokens
  - Kept bcrypt for password hashing
  - Updated JWTAuthService implementation
- Added seed data functionality:
  - Test user creation script
  - Initial data seeding (pantry, locations, products)
  - Additional test data (more products and inventory items)
  - Make commands for easy seeding (`make seed-all`)
- Purchase History core functionality implemented:
  - Purchase and PurchaseItem domain models
  - Repository layer and migrations
  - HTTP API endpoints with Swagger documentation
- Comprehensive development rules and guidelines established ([RULES_AND_GUIDES.md](memory-bank/RULES_AND_GUIDES.md)), including a **critical rule for Swagger documentation regeneration** and a new section on **Strictly Adhered Patterns**.
- Detailed feature documentation created for all major modules and pending tasks in `memory-bank/features/`.
- Database schema documented ([DATABASE_SCHEMA_POSTGRESQL_PantryPal.md](memory-bank/DATABASE_SCHEMA_POSTGRESQL_PantryPal.md)).
- All memory bank documents updated and cross-referenced for consistency.

## Open Issues
High Priority:
1. Purchase History Management incomplete.
2. Pantry-Specific Settings missing.
3. Password Change functionality needed.
4. Usage Tracking & Analytics required.

## Current Focus
Sprint 1: Core MVP Completion (Following Established Standards)

### Immediate Tasks (1-2 weeks)
1. **Purchase History Management - Store System**
   - Implement Store domain model and interfaces
   - Create Store repository layer
   - Add Store management HTTP endpoints:
     - Create store (POST /stores)
     - List stores (GET /stores)
     - Get store details (GET /stores/{storeId})
     - Update store (PUT /stores/{storeId})
     - Delete store (DELETE /stores/{storeId})
   - Support both global and user-specific store lists
   - Add test coverage
   - [Detailed Documentation](memory-bank/features/PURCHASE_HISTORY_MANAGEMENT.md)

2. **Pantry-Specific Settings**
   - Create settings entity.
   - Implement settings repository.
   - Add configuration use cases.
   - Develop settings API.
   - [Detailed Documentation](memory-bank/features/PANTRY_SPECIFIC_SETTINGS.md)

3. **Password Change Functionality**
   - Implement change password use case.
   - Add security logging.
   - Complete HTTP handler.
   - [Detailed Documentation](memory-bank/features/PASSWORD_CHANGE_FUNCTIONALITY.md)

## Critical Development Rules & Patterns
- **Swagger Documentation Regeneration**: After creating, updating, or deleting any API handler, developers **MUST** regenerate the Swagger documentation using `swag init -g cmd/api/main.go -o docs`. This is crucial for maintaining up-to-date API documentation.
- **Strictly Adhered Patterns**: Developers **MUST** strictly follow the conventions for:
    - **Error Handling**: Consistent `AppError` usage and error wrapping.
    - **HTTP Responses**: Standard `APIResponse` structure, proper pagination, filtering, and error responses.
    - **Goroutines**: Safe concurrency management with `context.Context` and `sync.WaitGroup`, avoiding race conditions.
    - **Logging**: Structured logging with `zerolog` including contextual information.
    - **Domain Events**: Proper definition, raising, dispatching, and handling of domain events.
- **Reference**: For full details and examples, refer to "Section 11. Strictly Adhered Patterns (CRITICAL)" in [`RULES_AND_GUIDES.md`](memory-bank/RULES_AND_GUIDES.md).

## Integration Points
- Purchase → Inventory linking.
- Settings → Inventory thresholds.
- Usage Logs → Analytics integration.

## Active Decisions
- Clean Architecture implementation validated.
- Redis idempotency implementations (`internal/infra/redis/idempotency_repository.go` and `internal/infra/persistence/redis/idempotency_service.go`) confirmed as non-redundant and serving distinct purposes.
- `internal/core/usecases` directory structure verified to align with Clean Architecture principles; no conflicting 'usecase' directories found.
- JWT + Custom auth system working (fixed refresh token hashing issue).
- PostgreSQL + Redis stack effective.
- Feature priorities established.
- Development standards formalized in [RULES_AND_GUIDES.md](memory-bank/RULES_AND_GUIDES.md).
- All project documentation centralized and linked within the memory bank.

## Technical Health
- All tests passing (37/37).
- No linting errors.
- Documentation current and comprehensive.
- Core architecture stable.
- Standards documented and enforced.
- Seed data available for testing.

## Next Sprints
1. Sprint 1: Core MVP Completion (Current Focus)
2. Sprint 2: Enhanced Features
   - Usage Tracking ([Detailed Documentation](memory-bank/features/INVENTORY_TRACKING.md))
   - Inventory Adjustments ([Detailed Documentation](memory-bank/features/INVENTORY_TRACKING.md))
   - Idempotency Middleware ([Detailed Documentation](memory-bank/features/IDEMPOTENCY_MIDDLEWARE_EXAMPLES.md))
3. Sprint 3: Security & Recovery
   - Account Recovery ([Detailed Documentation](memory-bank/features/ACCOUNT_RECOVERY_SYSTEM.md))
   - Advanced Notifications ([Detailed Documentation](memory-bank/features/ADVANCED_NOTIFICATION_CHANNELS.md))
   - Casbin Authorization Migration ([Detailed Documentation](memory-bank/features/CASBIN_AUTHORIZATION_MIGRATION.md))

## Notes
- Focus on completing high-priority features.
- Maintain test coverage for new features.
- Follow established development standards.
- Update memory bank with all changes.
- Consider integration points during implementation.
- Use seed data for comprehensive testing.
