# Product Catalog & Categories

## Overview
This document outlines the requirements for the Product Catalog and Category Management features, enabling users to browse, define, and categorize products and their variants.

## Key Features & Requirements

### Product Catalog
- **Browse Catalog**: As a user, I can browse a shared, growing catalog of generic products (e.g., "Milk," "Olive Oil").
- **Define New Products**: As a user, I can define new generic products if they are not in the existing catalog.
- **Create Product Variants**: As a user, I can create specific product variants for existing generic products (e.g., "Whole Milk 1 Gallon," "Bertolli Extra Virgin Olive Oil 1L") including barcode/GTIN and an image URL.
    - **Constraint**: `Product.name` is unique per `brand` and `category`. `ProductVariant.barcode_gtin` **MUST** be globally unique if provided.

### Category Management
- **Categorize Products**: As a user, I can categorize products (e.g., "Dairy," "Grains," "Produce") to organize them. The system provides a default set of categories.
- **Hierarchical Categories**: As a user, I can create and manage hierarchical categories (e.g., "Dairy" can have sub-categories like "Milk," "Cheese"). Category names **SHOULD** be unique globally or at least within their direct parent to avoid confusion.

## Related Entities
- [`Product`](internal/core/domain/product.go)
- [`ProductVariant`](internal/core/domain/product.go)
- [`Category`](internal/core/domain/category.go)

## API Endpoints (Conceptual)
- `POST /products`
- `GET /products`
- `GET /products/{productId}`
- `PUT /products/{productId}`
- `DELETE /products/{productId}`
- `POST /product-variants`
- `GET /product-variants`
- `GET /product-variants/{variantId}`
- `PUT /product-variants/{variantId}`
- `DELETE /product-variants/{variantId}`
- `POST /categories`
- `GET /categories`
- `GET /categories/{categoryId}`
- `PUT /categories/{categoryId}`
- `DELETE /categories/{categoryId}`

## Implementation Notes
- Global product catalog, but users can add custom products/variants.
- Hierarchical category structure.
- Uniqueness constraints on product names and barcodes.