# Pantry-Specific Settings

## Overview
This document outlines the requirements for Pantry-Specific Settings, allowing users to configure various preferences and thresholds for their individual pantries.

## Key Features & Requirements

### Custom Thresholds
- **Low Stock Thresholds**: As a user, I can configure custom low stock thresholds for specific product variants or categories within my pantry.

### Default Locations
- **Default Storage Location**: As a user, I can set a default storage location for newly added items in my pantry.

### Preferred Units & Currency
- **Preferred Currency**: As a user, I can set a preferred currency for purchases in my pantry.
- **Preferred Units of Measure (TODO)**:
    - **Missing Components**: Add support for preferred units of measure in `PantrySettings` entity and related use cases/APIs.

### Notification Preferences (TODO)
- **Missing Components**: Add support for notification preferences in `PantrySettings` entity and related use cases/APIs.

## Related Entities
- [`PantrySettings`](internal/core/domain/pantry_settings.go) (conceptual)

## API Endpoints (Conceptual)
- `GET /pantries/{pantryId}/settings`
- `PUT /pantries/{pantryId}/settings`
- `PATCH /pantries/{pantryId}/settings/low-stock-thresholds`
- `PATCH /pantries/{pantryId}/settings/default-location`
- `PATCH /pantries/{pantryId}/settings/preferred-currency`

## Implementation Notes
- Settings should be stored as key-value pairs or a structured JSONB field in the database.
- Integration with Inventory Management for applying low stock thresholds and default locations.
- Database migration for `pantry_settings` table is needed.