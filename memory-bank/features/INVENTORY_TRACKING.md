# Inventory Tracking

## Overview
This document details the requirements for the Inventory Tracking feature, enabling users to manage items within their pantries, track quantities, locations, and expiration dates, and record usage and adjustments.

## Key Features & Requirements

### Core Inventory Management
- **Add Inventory Items**: As a user, I can add new inventory items to my pantry specifying the product variant, initial quantity, unit of measure, expiration date, best-before date, and storage location.
- **View Inventory**: As a user, I can view all inventory items in a specific pantry with their current quantity, location, expiration/best-before dates, and associated product details.
- **Update Inventory Items**: As a user, I can update the quantity, location, expiration date, or notes for any inventory item.
- **Filter and Sort**: As a user, I can filter and sort inventory items by product name, category, location, expiration date, status (e.g., low stock, expired), and last used date.

### Usage Tracking
- **Log Consumption**: As a user, I can log the consumption (usage) of an item, specifying the quantity used and the date of usage. This **MUST** automatically reduce the `current_quantity` of the `InventoryItem`.
- **Usage Tracking & Analytics (TODO)**:
    - **Status**: Partial (consumption exists, no detailed logs)
    - **Priority**: High
    - **Estimated Effort**: 2-3 days
    - **Missing Components**:
        - Domain Models: `UsageLog` entity for detailed consumption tracking, Usage analytics aggregation, Consumption pattern analysis.
        - Repository Layer: `UsageLogRepository` implementation, Database migration for `usage_logs` table, Analytics query methods.
        - Use Cases: `LogItemUsage` use case, `GetUsageHistory` use case, `AnalyzeConsumptionPatterns` use case, `PredictUsage` use case.
        - HTTP API: Usage logging endpoints, Usage history retrieval, Analytics endpoints.
    - **Success Criteria**:
        - Detailed consumption logging
        - Usage history retrieval
        - Consumption pattern analysis
        - Integration with shopping list generation

### Inventory Adjustments
- **Record Adjustments**: As a user, I can record inventory adjustments for non-consumption reasons (e.g., spoilage, loss, manual correction of miscount), affecting the `current_quantity`.
- **Inventory Adjustments (TODO)**:
    - **Status**: Not Started
    - **Priority**: Medium
    - **Estimated Effort**: 2 days
    - **Missing Components**:
        - Domain Models: `InventoryAdjustment` entity, Adjustment types (spoilage, loss, manual_correction, transfer), Adjustment validation logic.
        - Repository Layer: `InventoryAdjustmentRepository` implementation, Database migration for `inventory_adjustments` table.
        - Use Cases: `RecordAdjustment` use case, `GetAdjustmentHistory` use case, `RecordSpoilage` use case.
        - HTTP API: Adjustment recording endpoints, Adjustment history endpoints.

### Pantry Locations
- **Custom Locations**: As a user, I can define custom storage locations within each of my pantries (e.g., "Fridge Door," "Pantry Shelf 1," "Freezer Drawer").

## Related Entities
- [`InventoryItem`](internal/core/domain/inventory.go)
- [`PantryLocation`](internal/core/domain/pantry_location.go)
- [`UsageLog`](internal/core/domain/inventory.go) (conceptual)
- [`InventoryAdjustment`](internal/core/domain/inventory_requests.go) (conceptual)

## API Endpoints (Conceptual)
- `POST /pantries/{pantryId}/inventory` (Create item)
- `GET /pantries/{pantryId}/inventory` (List items)
- `GET /pantries/{pantryId}/inventory/{itemId}`
- `PUT /pantries/{pantryId}/inventory/{itemId}` (Update item)
- `DELETE /pantries/{pantryId}/inventory/{itemId}`
- `POST /pantries/{pantryId}/inventory/{itemId}/consume` (Log consumption)
- `POST /pantries/{pantryId}/inventory/{itemId}/adjust` (Record adjustment)
- `POST /pantries/{pantryId}/locations`
- `GET /pantries/{pantryId}/locations`
- `PUT /pantries/{pantryId}/locations/{locationId}`
- `DELETE /pantries/{pantryId}/locations/{locationId}`
- `GET /pantries/{pantryId}/usage-logs`
- `GET /pantries/{pantryId}/analytics/consumption`

## Implementation Notes
- FIFO (First-In, First-Out) logic for consumption where applicable.
- Integration with Unit of Measure Management for quantity handling.
- Expiration tracking is a separate but related feature.