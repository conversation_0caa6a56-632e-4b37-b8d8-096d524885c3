# Shopping List API Documentation

## Overview

This document describes the REST API endpoints for managing shopping lists in the Pantry Pal application. All endpoints require authentication and proper pantry access permissions.

## Base URL

```
/api/v1/pantries/{pantry_id}/shopping-lists
```

## Authentication

All endpoints require a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Shopping List Endpoints

### 1. Create Shopping List

**POST** `/api/v1/pantries/{pantry_id}/shopping-lists`

Creates a new shopping list in the specified pantry.

**Request Body:**
```json
{
  "name": "Weekly Groceries",
  "description": "Groceries for the week of March 15th"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "pantry_id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "Weekly Groceries",
    "description": "Groceries for the week of March 15th",
    "created_by": "123e4567-e89b-12d3-a456-426614174002",
    "status": "active",
    "created_at": "2024-03-15T10:00:00Z",
    "updated_at": "2024-03-15T10:00:00Z",
    "items": []
  }
}
```

### 2. Get Shopping Lists

**GET** `/api/v1/pantries/{pantry_id}/shopping-lists`

Retrieves all shopping lists for a pantry with optional filtering.

**Query Parameters:**
- `status` (optional): Filter by status (`active`, `completed`, `archived`)
- `created_by` (optional): Filter by creator user ID
- `limit` (optional): Number of results per page (default: 20, max: 100)
- `offset` (optional): Number of results to skip (default: 0)
- `sort_by` (optional): Sort field (`created_at`, `updated_at`, `name`)
- `sort_order` (optional): Sort direction (`asc`, `desc`)

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "pantry_id": "123e4567-e89b-12d3-a456-426614174001",
      "name": "Weekly Groceries",
      "description": "Groceries for the week of March 15th",
      "created_by": "123e4567-e89b-12d3-a456-426614174002",
      "status": "active",
      "created_at": "2024-03-15T10:00:00Z",
      "updated_at": "2024-03-15T10:00:00Z",
      "items": [...]
    }
  ],
  "pagination": {
    "total": 1,
    "limit": 20,
    "offset": 0,
    "has_more": false
  }
}
```

### 3. Get Shopping List by ID

**GET** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}`

Retrieves a specific shopping list with all its items.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "pantry_id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "Weekly Groceries",
    "description": "Groceries for the week of March 15th",
    "created_by": "123e4567-e89b-12d3-a456-426614174002",
    "status": "active",
    "created_at": "2024-03-15T10:00:00Z",
    "updated_at": "2024-03-15T10:00:00Z",
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174003",
        "shopping_list_id": "123e4567-e89b-12d3-a456-426614174000",
        "product_variant_id": "123e4567-e89b-12d3-a456-426614174004",
        "free_text_name": null,
        "quantity_desired": 2.5,
        "unit_of_measure_id": "123e4567-e89b-12d3-a456-426614174005",
        "notes": "Organic if available",
        "is_purchased": false,
        "purchased_at": null,
        "created_at": "2024-03-15T10:05:00Z",
        "updated_at": "2024-03-15T10:05:00Z"
      }
    ]
  }
}
```

### 4. Update Shopping List

**PUT** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}`

Updates shopping list details.

**Request Body:**
```json
{
  "name": "Updated Weekly Groceries",
  "description": "Updated description"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Updated Weekly Groceries",
    "description": "Updated description",
    "updated_at": "2024-03-15T11:00:00Z"
  }
}
```

### 5. Change Shopping List Status

**PATCH** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/status`

Changes the status of a shopping list.

**Request Body:**
```json
{
  "status": "completed"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "completed",
    "updated_at": "2024-03-15T12:00:00Z"
  }
}
```

### 6. Delete Shopping List

**DELETE** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}`

Deletes a shopping list and all its items.

**Response (204 No Content)**

## Shopping List Item Endpoints

### 1. Add Item to Shopping List

**POST** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/items`

Adds an item to a shopping list.

**Request Body (Product Variant Item):**
```json
{
  "product_variant_id": "123e4567-e89b-12d3-a456-426614174004",
  "quantity_desired": 2.5,
  "unit_of_measure_id": "123e4567-e89b-12d3-a456-426614174005",
  "notes": "Organic if available"
}
```

**Request Body (Free Text Item):**
```json
{
  "free_text_name": "Special spice blend",
  "quantity_desired": 1.0,
  "notes": "From the local market"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174003",
    "shopping_list_id": "123e4567-e89b-12d3-a456-426614174000",
    "product_variant_id": "123e4567-e89b-12d3-a456-426614174004",
    "free_text_name": null,
    "quantity_desired": 2.5,
    "unit_of_measure_id": "123e4567-e89b-12d3-a456-426614174005",
    "notes": "Organic if available",
    "is_purchased": false,
    "purchased_at": null,
    "created_at": "2024-03-15T10:05:00Z",
    "updated_at": "2024-03-15T10:05:00Z"
  }
}
```

### 2. Update Shopping List Item

**PUT** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/items/{item_id}`

Updates an existing shopping list item.

**Request Body:**
```json
{
  "quantity_desired": 3.0,
  "notes": "Updated notes"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174003",
    "quantity_desired": 3.0,
    "notes": "Updated notes",
    "updated_at": "2024-03-15T11:05:00Z"
  }
}
```

### 3. Mark Item as Purchased

**PATCH** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/items/{item_id}/purchased`

Marks an item as purchased.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174003",
    "is_purchased": true,
    "purchased_at": "2024-03-15T13:00:00Z",
    "updated_at": "2024-03-15T13:00:00Z"
  }
}
```

### 4. Mark Item as Not Purchased

**DELETE** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/items/{item_id}/purchased`

Marks an item as not purchased.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174003",
    "is_purchased": false,
    "purchased_at": null,
    "updated_at": "2024-03-15T13:05:00Z"
  }
}
```

### 5. Remove Item from Shopping List

**DELETE** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/items/{item_id}`

Removes an item from a shopping list.

**Response (204 No Content)**

### 6. Bulk Mark Items as Purchased

**PATCH** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/items/bulk-purchased`

Marks multiple items as purchased in a single operation.

**Request Body:**
```json
{
  "item_ids": [
    "123e4567-e89b-12d3-a456-426614174003",
    "123e4567-e89b-12d3-a456-426614174006",
    "123e4567-e89b-12d3-a456-426614174007"
  ]
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "3 items marked as purchased"
}
```

### 7. Bulk Mark Items as Not Purchased

**DELETE** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/items/bulk-purchased`

Marks multiple items as not purchased in a single operation.

**Request Body:**
```json
{
  "item_ids": [
    "123e4567-e89b-12d3-a456-426614174003",
    "123e4567-e89b-12d3-a456-426614174006"
  ]
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "2 items marked as not purchased"
}
```

## Statistics Endpoints

### 1. Get Shopping List Statistics

**GET** `/api/v1/pantries/{pantry_id}/shopping-lists/{list_id}/stats`

Retrieves statistics for a specific shopping list.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "shopping_list_id": "123e4567-e89b-12d3-a456-426614174000",
    "total_items": 10,
    "purchased_items": 7,
    "remaining_items": 3,
    "completion_percentage": 70.0
  }
}
```

### 2. Get Pantry Shopping List Statistics

**GET** `/api/v1/pantries/{pantry_id}/shopping-lists/stats`

Retrieves aggregated statistics for all shopping lists in a pantry.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "pantry_id": "123e4567-e89b-12d3-a456-426614174001",
    "total_shopping_lists": 5,
    "active_shopping_lists": 3,
    "completed_shopping_lists": 2,
    "archived_shopping_lists": 0,
    "total_items": 45,
    "total_purchased_items": 32,
    "overall_completion_rate": 71.1
  }
}
```

## Error Responses

All endpoints return standardized error responses:

**400 Bad Request:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Invalid input data",
    "details": {
      "name": "Name is required"
    }
  }
}
```

**401 Unauthorized:**
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  }
}
```

**403 Forbidden:**
```json
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "User does not have access to this pantry"
  }
}
```

**404 Not Found:**
```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Shopping list not found"
  }
}
```

**422 Unprocessable Entity:**
```json
{
  "success": false,
  "error": {
    "code": "BUSINESS_RULE_VIOLATION",
    "message": "Either product_variant_id or free_text_name must be provided"
  }
}
```

## Rate Limiting

All endpoints are subject to rate limiting:
- **Authenticated users**: 1000 requests per hour
- **Per endpoint**: 100 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1647345600
```
