# Unit of Measure Management

## Overview
This document specifies the requirements for managing units of measure and their conversions within Pantry Pal, ensuring accurate quantity tracking across various product types.

## Key Features & Requirements

### Standard Units
- **Availability**: As a user, I expect standard units of measure to be available (e.g., "<PERSON>allo<PERSON>," "Liter," "Piece," "Pound," "Gram").

### Unit Conversion
- **Automatic Conversion**: As a user, I expect the system to automatically convert quantities between compatible units (e.g., 3785 ml to 1 gallon, 16 oz to 1 pound) based on predefined conversion factors.
- **System-Level Definition**: As a system-level administrator (via backend tools/scripts), I can define new global units of measure and conversion factors between them.

## Related Entities
- [`UnitOfMeasure`](internal/core/domain/unit_of_measure.go)
- `UnitOfMeasureConversion` (conceptual entity for conversions)

## API Endpoints (Conceptual)
- `GET /units-of-measure`
- `GET /units-of-measure/{unitId}`
- `GET /units-of-measure/conversions` (for system administrators)
- `POST /units-of-measure/conversions` (for system administrators)

## Implementation Notes
- Predefined set of common units.
- Conversion factors stored in the database.
- Conversion logic implemented in a dedicated service or use case.