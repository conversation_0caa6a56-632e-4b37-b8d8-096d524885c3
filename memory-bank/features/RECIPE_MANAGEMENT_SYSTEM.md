# Recipe Management System

## 🎯 **Overview**

The Recipe Management System is a comprehensive solution for creating, managing, and cooking recipes with intelligent inventory integration. It provides rich media support, step-by-step instructions, nutritional information, and smart shopping list generation.

## ✨ **Core Features**

### **📝 Recipe Creation & Management**
- **Rich Recipe Details**: Title, description, cuisine, category, difficulty levels
- **Timing Information**: Prep time, cook time, total time calculation
- **Serving Management**: Configurable servings with automatic scaling
- **Source Tracking**: Recipe source attribution and notes
- **Public/Private Recipes**: Share recipes publicly or keep them private
- **Favorite System**: Mark and filter favorite recipes

### **🥘 Ingredients Management**
- **Product Integration**: Link ingredients to product variants in catalog
- **Flexible Quantities**: Support for various units of measure
- **Preparation Notes**: Specify preparation methods (diced, chopped, etc.)
- **Optional Ingredients**: Mark ingredients as optional or garnish
- **Fallback Support**: Manual ingredient entry when products don't exist

### **📋 Step-by-Step Instructions**
- **Ordered Steps**: Sequential cooking instructions with step numbers
- **Rich Content**: Optional titles, detailed instructions, and tips
- **Timing Integration**: Duration for each step
- **Temperature Tracking**: Cooking temperatures for precision
- **Media Support**: Images and videos for each step

### **📸 Media Management**
- **Multiple Media Types**: Support for images and videos
- **Main Image Selection**: Featured recipe image
- **Step-by-Step Media**: Attach media to specific instructions
- **Metadata Support**: Captions, alt text, and file information
- **Organized Display**: Ordered media with proper categorization

### **🍎 Nutritional Information**
- **Comprehensive Nutrition**: Calories, macronutrients, vitamins, minerals
- **Per-Serving Calculation**: Automatic nutrition scaling
- **Flexible Input**: Optional nutrition data entry
- **Health Tracking**: Support for dietary monitoring

### **🏷️ Tagging & Categorization**
- **System Tags**: Pre-defined tags (vegetarian, vegan, gluten-free, etc.)
- **Custom Tags**: User-defined tags for organization
- **Tag Analytics**: Usage tracking and popular tags
- **Smart Filtering**: Filter recipes by multiple tags

### **⭐ Reviews & Ratings**
- **5-Star Rating System**: Rate recipes from 1-5 stars
- **Review Comments**: Detailed feedback and cooking notes
- **Photo Reviews**: Attach photos of cooked dishes
- **Cooking Tracking**: Track when recipes were cooked
- **Average Ratings**: Calculated average ratings for recipes

### **📚 Recipe Collections**
- **Meal Planning**: Create collections for meal plans
- **Themed Collections**: Group recipes by themes or occasions
- **Public Collections**: Share curated recipe collections
- **Ordered Recipes**: Organize recipes within collections
- **Collection Notes**: Add notes for specific recipes in collections

## 🔗 **Inventory Integration**

### **📦 Ingredient Availability Checking**
- **Real-Time Inventory**: Check ingredient availability in pantries
- **Quantity Matching**: Compare required vs. available quantities
- **Unit Conversion**: Handle different units of measure
- **Optional Ingredient Handling**: Skip optional ingredients in availability checks

### **🛒 Smart Shopping List Generation**
- **Missing Ingredients**: Automatically identify missing ingredients
- **Shopping List Creation**: Generate shopping lists for missing items
- **Recipe Context**: Include recipe information in shopping items
- **Priority Management**: Set priorities for shopping items

### **🍽️ Recipe Cooking Integration**
- **Ingredient Consumption**: Automatically consume ingredients when cooking
- **Inventory Updates**: Real-time inventory updates during cooking
- **Cooking Tracking**: Track cooking frequency and last cooked dates
- **Portion Scaling**: Scale ingredient consumption based on servings

## 🔍 **Advanced Features**

### **🔎 Search & Discovery**
- **Full-Text Search**: Search in titles, descriptions, and ingredients
- **Advanced Filtering**: Filter by cuisine, category, difficulty, time, calories
- **Tag-Based Search**: Find recipes by tags
- **Popular Recipes**: Discover trending and highly-rated recipes
- **Recent Activity**: View recently cooked recipes

### **📏 Recipe Scaling**
- **Dynamic Scaling**: Scale recipes to different serving sizes
- **Ingredient Adjustment**: Automatic quantity recalculation
- **Nutrition Scaling**: Proportional nutrition information
- **Instruction Preservation**: Maintain cooking instructions during scaling

### **📊 Analytics & Insights**
- **Cooking Statistics**: Track cooking frequency and patterns
- **Popular Ingredients**: Identify most-used ingredients
- **Nutrition Tracking**: Monitor nutritional intake from recipes
- **Recipe Performance**: Track ratings and reviews

## 🏗️ **Technical Architecture**

### **Domain Models**
- **Recipe**: Core recipe entity with all metadata
- **RecipeIngredient**: Ingredient specifications with quantities
- **RecipeInstruction**: Step-by-step cooking instructions
- **RecipeMedia**: Images and videos associated with recipes
- **RecipeNutrition**: Nutritional information per recipe
- **RecipeTag**: Categorization and tagging system
- **RecipeReview**: User ratings and reviews
- **RecipeCollection**: Recipe grouping and meal planning

### **Repository Pattern**
- **RecipeRepository**: Core recipe CRUD operations
- **RecipeTagRepository**: Tag management and analytics
- **RecipeReviewRepository**: Review and rating management
- **RecipeCollectionRepository**: Collection and meal planning

### **Business Logic**
- **RecipeUsecase**: Orchestrates recipe operations
- **Inventory Integration**: Checks availability and manages consumption
- **Shopping List Generation**: Creates shopping lists from recipes
- **Scaling Logic**: Handles recipe portion adjustments

### **API Endpoints**
- **CRUD Operations**: Create, read, update, delete recipes
- **Search & Filter**: Advanced recipe discovery
- **Inventory Integration**: Check availability and consume ingredients
- **Scaling**: Dynamic recipe scaling
- **Collections**: Meal planning and organization

## 📱 **API Documentation**

### **Recipe Management Endpoints**

#### **POST /recipes**
Create a new recipe with ingredients, instructions, and media.

#### **GET /recipes**
Retrieve user's recipes with pagination and filtering.

#### **GET /recipes/public**
Browse public recipes from all users.

#### **GET /recipes/{recipeId}**
Get detailed recipe information.

#### **PUT /recipes/{recipeId}**
Update recipe details, ingredients, or instructions.

#### **DELETE /recipes/{recipeId}**
Delete a recipe and all associated data.

#### **POST /recipes/{recipeId}/scale**
Scale recipe to different serving sizes.

#### **POST /recipes/{recipeId}/cook**
Mark recipe as cooked and update statistics.

#### **POST /recipes/{recipeId}/check-inventory**
Check ingredient availability in pantry inventory.

#### **GET /recipes/search**
Search recipes with advanced filtering options.

### **Request/Response Examples**

#### **Create Recipe Request**
```json
{
  "title": "Classic Chocolate Chip Cookies",
  "description": "Soft and chewy chocolate chip cookies",
  "cuisine": "American",
  "category": "Dessert",
  "difficulty": "easy",
  "prep_time": 15,
  "cook_time": 12,
  "servings": 24,
  "is_public": true,
  "tags": ["dessert", "cookies", "chocolate"],
  "ingredients": [
    {
      "product_variant_id": "uuid",
      "name": "All-purpose flour",
      "quantity": 2.25,
      "unit": "cups",
      "preparation": "sifted"
    }
  ],
  "instructions": [
    {
      "title": "Prepare dry ingredients",
      "instruction": "In a medium bowl, whisk together flour, baking soda, and salt.",
      "duration": 3
    }
  ],
  "nutrition": {
    "calories": 150,
    "protein": 2.0,
    "carbohydrates": 22.0,
    "fat": 6.0
  }
}
```

#### **Inventory Check Response**
```json
{
  "available_ingredients": [
    {
      "ingredient_id": "uuid",
      "name": "All-purpose flour",
      "required_quantity": 2.25,
      "required_unit": "cups",
      "available_quantity": 5.0,
      "available_unit": "cups",
      "is_available": true,
      "is_optional": false
    }
  ],
  "missing_ingredients": [],
  "shopping_list_items": [],
  "can_cook": true,
  "missing_count": 0,
  "total_ingredients": 8
}
```

## 🚀 **Getting Started**

### **Prerequisites**
- Go 1.21+
- PostgreSQL 13+
- Redis 6+
- Swagger/OpenAPI tools

### **Setup Instructions**

1. **Database Migration**
   ```bash
   # Run recipe table migrations
   migrate -path internal/infra/persistence/migrations -database "postgres://..." up
   ```

2. **API Documentation**
   ```bash
   # Generate Swagger docs
   swag init -g cmd/api/main.go -o docs
   ```

3. **Start Server**
   ```bash
   # Build and run
   go build -o bin/api cmd/api/main.go
   ./bin/api
   ```

4. **Access Documentation**
   - Swagger UI: `http://localhost:8080/docs/`
   - API Spec: `http://localhost:8080/docs/swagger.json`

## 🎯 **Usage Examples**

### **Creating a Recipe**
```bash
curl -X POST "http://localhost:8080/api/v1/recipes" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d @recipe.json
```

### **Checking Ingredient Availability**
```bash
curl -X POST "http://localhost:8080/api/v1/recipes/{id}/check-inventory" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"pantry_id": "pantry-uuid"}'
```

### **Scaling a Recipe**
```bash
curl -X POST "http://localhost:8080/api/v1/recipes/{id}/scale" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"servings": 12}'
```

## 🔮 **Future Enhancements**

### **Planned Features**
- **Recipe Import**: Import recipes from URLs and other formats
- **Voice Instructions**: Text-to-speech for hands-free cooking
- **Cooking Timers**: Built-in timers for recipe steps
- **Meal Planning**: Advanced meal planning with calendar integration
- **Recipe Sharing**: Social features for recipe sharing
- **Nutritional Analysis**: Advanced nutritional analysis and recommendations
- **Dietary Filters**: Enhanced dietary restriction filtering
- **Recipe Analytics**: Detailed analytics and insights

### **Integration Opportunities**
- **Smart Kitchen Devices**: Integration with IoT cooking devices
- **Grocery Delivery**: Integration with grocery delivery services
- **Nutrition Apps**: Integration with fitness and nutrition tracking apps
- **Social Media**: Recipe sharing on social platforms

## 📊 **Performance & Scalability**

### **Database Optimization**
- **Indexed Queries**: Optimized database indexes for fast searches
- **Pagination**: Efficient pagination for large recipe collections
- **Caching**: Redis caching for frequently accessed recipes

### **API Performance**
- **Lazy Loading**: Efficient loading of recipe relationships
- **Batch Operations**: Bulk operations for better performance
- **Response Optimization**: Optimized JSON responses

## 🔒 **Security & Privacy**

### **Access Control**
- **Recipe Ownership**: Users can only modify their own recipes
- **Public/Private Recipes**: Granular privacy controls
- **Secure Media**: Secure media upload and storage

### **Data Protection**
- **Input Validation**: Comprehensive input validation
- **SQL Injection Protection**: Parameterized queries
- **XSS Prevention**: Output sanitization

## 🎉 **Conclusion**

The Recipe Management System provides a comprehensive solution for recipe creation, management, and cooking with intelligent inventory integration. It combines rich media support, detailed nutritional information, and smart shopping list generation to create a complete cooking experience.

The system is designed for scalability, performance, and ease of use, making it suitable for both individual users and commercial applications. With its extensive API documentation and flexible architecture, it can be easily integrated into existing applications or used as a standalone service.
