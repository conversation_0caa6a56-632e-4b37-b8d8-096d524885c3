# Product Context

## Problems Solved
- Food Waste Reduction: By tracking expiration dates and consumption patterns.
- Improved Organization: Centralized inventory and meal planning.
- Enhanced Collaboration: Multi-user pantry management.
- Efficient Shopping: Smart shopping list generation.

## How it Works
Pantry Pal is a multi-tenant application where users can create and manage multiple pantries. Each pantry can have multiple members with different roles. Users can add products, track inventory, manage recipes, and generate shopping lists.

## User Experience Goals
- Intuitive and easy-to-use interface.
- Fast and responsive performance.
- Secure and reliable data management.
- Collaborative features for shared households.
- Actionable insights to reduce waste.

## Key Features and Modules

### 1. User & Pantry Management
- User registration, login, and profile management.
- Multi-tenant pantry creation and management.
- Pantry membership and role-based access control.
- [Detailed Documentation](memory-bank/features/USER_AND_PANTRY_MANAGEMENT.md)

### 2. Product Catalog & Categories
- Comprehensive product catalog with categories and subcategories.
- Unit of measure management with conversion capabilities.
- Product search and filtering.
- [Detailed Documentation](memory-bank/features/PRODUCT_CATALOG_AND_CATEGORIES.md)

### 3. Unit of Measure Management
- Standard units of measure and automatic quantity conversion.
- System-level definition of new units and conversion factors.
- [Detailed Documentation](memory-bank/features/UNIT_OF_MEASURE_MANAGEMENT.md)

### 4. Inventory Tracking
- Detailed inventory item management (quantity, location, expiration).
- Consumption tracking and history.
- Inventory adjustments (spoilage, loss, manual correction).
- Custom storage locations within each pantry.
- [Detailed Documentation](memory-bank/features/INVENTORY_TRACKING.md)

### 5. Purchase History Management
- Recording purchase transactions and linking to inventory.
- Managing frequently visited stores.
- [Detailed Documentation](memory-bank/features/PURCHASE_HISTORY_MANAGEMENT.md)

### 6. Shopping Lists
- Create and manage multiple shopping lists.
- Add items by product variant or free text.
- Track purchased items and completion status.
- Automated Shopping List Generation based on low stock, empty items, expiring items, and recipe requirements.
- Smart quantity recommendations based on consumption patterns.
- [Shopping List API Documentation](memory-bank/features/SHOPPING_LIST_API.md)
- [Shopping List Feature Documentation](memory-bank/features/SHOPPING_LIST_FEATURE.md)
- [Shopping List Examples](memory-bank/features/SHOPPING_LIST_EXAMPLES.md)
- [Shopping List Implementation](memory-bank/features/SHOPPING_LIST_IMPLEMENTATION.md)

### 7. Recipe Management
- Create, manage, and organize recipes with rich details.
- Ingredient management with product integration and unit conversion.
- Step-by-step instructions with media support.
- Nutritional information tracking.
- Recipe collections for meal planning.
- Recipe Consumption: Automatically consume ingredients from inventory with smart item selection.
- [Recipe Management System Documentation](memory-bank/features/RECIPE_MANAGEMENT_SYSTEM.md)
- [Recipe Consumption Examples](memory-bank/features/RECIPE_CONSUMPTION_EXAMPLES.md)

### 8. Pantry-Specific Settings
- Configurable thresholds for low stock, default locations, preferred units, and currency settings.
- [Detailed Documentation](memory-bank/features/PANTRY_SPECIFIC_SETTINGS.md)

### 9. Notifications & Alerts
- In-app notifications for low stock, expiring/expired items, new pantry invitations.
- **Expiration Tracking & Alerts**: Smart status classification, configurable thresholds, multi-channel notifications.
- [Expiration Tracking Examples](memory-bank/features/EXPIRATION_TRACKING_EXAMPLES.md)
- **Advanced Notification Channels (TODO)**: Email, SMS, Push Notifications.
- [Detailed Documentation](memory-bank/features/ADVANCED_NOTIFICATION_CHANNELS.md)

### 10. Password Change Functionality
- Securely change user password with current password verification, strength validation, and security event logging.
- Automatic refresh token revocation.
- [Detailed Documentation](memory-bank/features/PASSWORD_CHANGE_FUNCTIONALITY.md)

### 11. Account Recovery System
- Password reset flow with email verification and token validation.
- [Detailed Documentation](memory-bank/features/ACCOUNT_RECOVERY_SYSTEM.md)

### 12. Idempotency Middleware
- Protection against duplicate operations for production reliability.
- Redis-based storage, request fingerprinting, and response caching.
- [Idempotency Middleware Examples](memory-bank/features/IDEMPOTENCY_MIDDLEWARE_EXAMPLES.md)

### 13. Casbin Authorization Migration (TODO)
- Migration from custom authorization to Casbin for robust RBAC.
- Dynamic policy updates and role-based policy definitions.
- [Detailed Documentation](memory-bank/features/CASBIN_AUTHORIZATION_MIGRATION.md)

### 14. API Documentation
- Comprehensive OpenAPI/Swagger documentation for all API endpoints.
- Interactive UI for browsing and testing.
- [Swagger/OpenAPI Documentation](memory-bank/features/SWAGGER_API_DOCUMENTATION.md)

## Database Schema
- Detailed PostgreSQL database schema including table definitions, column types, constraints, and relationships.
- [Detailed Schema Document](memory-bank/DATABASE_SCHEMA_POSTGRESQL_PantryPal.md)

## Future Enhancements
- Advanced Analytics (consumption trends, waste reduction insights)
- Mobile Features (barcode scanning, mobile-specific endpoints)
- Recipe Enhancements (URL import, voice instructions, cooking timers, meal planning calendar)