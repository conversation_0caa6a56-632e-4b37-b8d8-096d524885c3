# Development Rules and Guidelines

## 1. Core Principles

### Clean Architecture
- Domain logic MUST reside in `core/domain`
- Use cases MUST reside in `core/usecases`
- Infrastructure concerns MUST be in `infra`
- Dependencies MUST flow inward only
- All interfaces MUST be defined by consumers

### SOLID Principles
- Single Responsibility: One clear purpose per type/package
- Open/Closed: Open for extension, closed for modification
- Liskov Substitution: Subtypes must be substitutable
- Interface Segregation: Small, focused interfaces
- Dependency Inversion: Depend on abstractions

## 2. Coding Standards

### Go Guidelines
- MUST use `go fmt` for formatting
- MUST pass `golangci-lint` checks
- MUST handle errors explicitly
- MUST use meaningful variable names
- MUST document exported items

### Error Handling
- MUST use custom error types from `internal/infra/errors`
- MUST wrap errors with context using `fmt.Errorf("... %w", err)`
- MUST provide clear error messages
- MUST use appropriate error codes

### Testing
- MUST write unit tests for core business logic
- MUST achieve >80% coverage for core logic
- MUST use interfaces for mocking
- MUST organize tests in `_test.go` files

## 3. Database Rules

### Migrations
- MUST use `golang-migrate` for schema changes
- MUST provide both up and down scripts
- MUST NOT use GORM AutoMigrate in production
- MUST follow naming convention: `NNNNNN_description.up/down.sql`

### Data Access
- MUST use repository pattern
- MUST use transactions for multi-table operations
- MUST implement soft deletes with `deleted_at`
- MUST use GORM properly (preload, joins)

## 4. API Guidelines

### REST Principles
- MUST use appropriate HTTP methods
- MUST use consistent URL patterns
- MUST implement proper status codes
- MUST version via URL path (/api/v1/...)

### Response Format
```json
{
    "success": true,
    "data": {},
    "message": "Optional message",
    "meta": {
        "pagination": {}
    }
}
```

### Security
- MUST validate all inputs
- MUST use JWT authentication
- MUST implement role-based access
- MUST use HTTPS in production
- MUST rate limit APIs

## 5. Development Workflow

### Version Control
- MUST use feature branches
- MUST write clear commit messages
- MUST review all code changes
- MUST pass CI checks before merge

### Documentation
- MUST update OpenAPI/Swagger docs
- MUST document all API endpoints
- MUST maintain README files
- MUST update memory bank when adding features

### API Documentation Generation (Swagger)
- **CRITICAL**: After creating, updating, or deleting any API handler, developers **MUST** regenerate the Swagger documentation.
- **Command**: `swag init -g cmd/api/main.go -o docs` (or `make swagger-gen` if a Makefile target exists)
- **Reason**: To ensure API documentation (`swagger.json`, `swagger.yaml`, `docs.go`) remains synchronized with the codebase.

## 6. Project-Specific Patterns

### Multi-tenancy
- MUST scope all data to pantries
- MUST validate pantry access
- MUST enforce role permissions
- MUST isolate tenant data

### Event System
- MUST raise domain events for significant changes
- MUST handle events asynchronously
- MUST log event processing
- MUST maintain event order

### Feature Implementation Checklist
1. Define domain models and interfaces
2. Implement repository layer
3. Create use cases
4. Add HTTP handlers
5. Write tests
6. Update documentation
7. Update memory bank

## 7. Memory Bank Updates

### When to Update
- After implementing new features
- When modifying existing features
- When adding new patterns
- When discovering issues/solutions

### What to Update
- Progress tracking
- Active context
- System patterns
- Technical context
- Documentation references

## 8. Code Organization

### Directory Structure
```
pantry-pal/
├── cmd/api/           # Entry point
├── internal/
│   ├── core/         # Business logic
│   │   ├── domain/   # Entities
│   │   └── usecases/ # Application logic (files should ideally be named `*_usecase.go`)
│   └── infra/        # External concerns
└── memory-bank/      # Project documentation
```

### 8.2. Naming Conventions within `usecases`
- **Rule**: Files within the `internal/core/usecases` directory SHOULD follow the `*_usecase.go` naming convention (e.g., `inventory_usecase.go`, `expiration_usecase.go`).
- **Rationale**: This ensures consistency with the `Usecase` struct names and clearly indicates their role as application-specific business logic, even if the internal type is named `*Service` (e.g., `ShoppingListService` acts as a use case).

## 9. Review Requirements

### Code Review Checklist
- [ ] Follows Clean Architecture
- [ ] Implements SOLID principles
- [ ] Includes proper tests
- [ ] Has necessary documentation
- [ ] Updates memory bank
- [ ] Handles errors properly
- [ ] Validates inputs
- [ ] Maintains security
- [ ] Swagger documentation regenerated

### Performance Considerations
- Query optimization
- Proper indexing
- Caching strategy
- Resource efficiency

## 10. Maintenance

### Regular Tasks
- Update dependencies
- Review security alerts
- Monitor performance
- Update documentation
- Clean up unused code
- Review error logs

### Quality Metrics
- Test coverage >80%
- No linting errors
- Documentation current
- Memory bank updated

## 11. Strictly Adhered Patterns (CRITICAL)

The following patterns are fundamental to the Pantry Pal backend and **MUST** be strictly adhered to by all developers. Deviations require explicit architectural review and approval.

### 11.1. Error Conventions
- **Rule**: All errors MUST use the custom `AppError` type defined in [`internal/infra/errors/errors.go`](internal/infra/errors/errors.go).
- **Rule**: Errors MUST be wrapped with context using `fmt.Errorf("... %w", err)` to preserve the call stack and provide meaningful debugging information.
- **Rule**: HTTP status codes returned to clients MUST be consistent with the `AppError.HTTPStatus` field.
- **Reference**: See "Error Handling" in this document (Section 2.2) and "Error Handling Examples" in [`BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md`](memory-bank/features/BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md#31-error-handling-examples).

### 11.2. HTTP Responses (Pagination, Filtering, Error Responses)
- **Rule**: All API responses MUST conform to the standard JSON `APIResponse` structure.
- **Rule**: List endpoints MUST implement pagination using `page`, `limit`, `sort_by`, `sort_order` query parameters and return `PaginationMetadata`.
- **Rule**: Filtering for list endpoints MUST be implemented consistently, typically via query parameters.
- **Rule**: Error responses MUST use the `APIResponse` structure with the `error` field populated by `ErrorInfo` (containing `code`, `message`, `details`).
- **Rule**: All functions responsible for generating HTTP responses (success, error, paginated) are centralized in [`internal/infra/web/handler/response.go`](internal/infra/web/handler/response.go).
- **Reference**: See "API Guidelines" in this document (Section 4) and "API Design Examples" in [`BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md`](memory-bank/features/BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md#7-api-design-examples).

### 11.3. Goroutines (Safe Concurrency)
- **Rule**: All goroutines MUST be managed safely, either by using `context.Context` for cancellation/timeouts or `sync.WaitGroup` for waiting on completion.
- **Rule**: Race conditions MUST be explicitly avoided using appropriate synchronization primitives (mutexes, channels) or by ensuring data immutability.
- **Rule**: The `go test -race` flag MUST be used during testing to detect race conditions.
- **Reference**: See "Concurrency" in this document (Section 2.4).

### 11.4. Logging
- **Rule**: All logging MUST use the structured `zerolog` library via the centralized logger instance in [`internal/infra/logger/logger.go`](internal/infra/logger/logger.go).
- **Rule**: Logs MUST include contextual information such as `request_id`, `user_id`, and `pantry_id` where relevant.
- **Rule**: Appropriate log levels (Debug, Info, Warn, Error, Fatal) MUST be used.
- **Reference**: See "Structured Logging" in this document (Section 3.6) and "Structured Logging Example" in [`BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md`](memory-bank/features/BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md#36-structured-logging-example-zerolog).

### 11.5. Domain Events
- **Rule**: Significant business occurrences MUST be captured as domain events.
- **Rule**: Domain events MUST be defined in [`internal/core/domain/events.go`](internal/core/domain/events.go).
- **Rule**: Domain entities (aggregate roots) MUST expose methods to add and clear domain events.
- **Rule**: Events MUST be dispatched by use cases after successful persistence of the aggregate root.
- **Rule**: Event handlers MUST be idempotent and handle potential failures gracefully.
- **Reference**: See "Event System" in this document (Section 6.2) and "Domain Events Flow Example" in [`BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md`](memory-bank/features/BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md#8-domain-events-flow-example).