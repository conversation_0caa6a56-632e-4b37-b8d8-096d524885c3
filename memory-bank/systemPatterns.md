# System Architecture and Design Patterns

## 1. Clean Architecture
- **Core Principle**: Separation of concerns, with dependencies flowing inwards.
- **Layers**:
    - **Domain**: Core business entities and rules.
    - **Use Cases**: Application-specific business rules.
    - **Infrastructure**: External concerns (database, web, etc.).
- **Benefits**: Testability, maintainability, flexibility, and independence from external frameworks.

## 2. Multi-Tenancy
- **Pattern**: Shared database, separate schema (or logical separation via `pantry_id`).
- **Implementation**: Every data entity is associated with a `pantry_id`. All queries and operations are scoped by the authenticated user's pantry access.
- **Authorization**: Implemented using a custom authorization service (`internal/core/domain/pantry_authorization.go`) that checks user roles and pantry memberships.

## 3. Repository Pattern
- **Purpose**: Abstract the data layer from the business logic.
- **Implementation**: Interfaces defined in `internal/core/domain/repositories.go`, with concrete implementations in `internal/infra/persistence/postgres`.
- **Benefits**: Decoupling, easier testing, and flexibility to switch data sources.

## 4. Event-Driven Architecture
- **Pattern**: Domain events are raised for significant state changes.
- **Implementation**: Events defined in `internal/core/domain/events.go`, dispatched by `internal/core/usecases/event_dispatcher.go`.
- **Benefits**: Loose coupling, scalability, and extensibility for future features (e.g., notifications, analytics).

## 5. Idempotency Middleware
- **Purpose**: Prevent duplicate processing of requests due to retries or network issues.
- **Implementation**: Redis-based middleware (`internal/infra/redis/idempotency_service.go`) that stores request fingerprints and caches responses.
- **Reference**: [Idempotency Middleware Examples](memory-bank/features/IDEMPOTENCY_MIDDLEWARE_EXAMPLES.md)

## 6. Centralized Error Handling
- **Pattern**: Custom error types and centralized error handling.
- **Implementation**: Custom error types defined in `internal/infra/errors/errors.go`, used consistently across all layers.
- **Benefits**: Consistent error responses, easier debugging, and clear error categorization.

## 7. Configuration Management
- **Tool**: Koanf for flexible configuration loading.
- **Implementation**: `config.yaml` and environment variables are used for application settings.
- **Benefits**: Externalized configuration, easy deployment across environments.

## 8. Structured Logging
- **Tool**: Zerolog for high-performance structured logging.
- **Implementation**: Centralized logger instance (`internal/infra/logger/logger.go`) used throughout the application.
- **Benefits**: Easier log parsing, monitoring, and debugging in production.

## 9. API Documentation (Swagger/OpenAPI)
- **Tool**: Swag for Go.
- **Implementation**: Annotations in handler files generate `swagger.json`, `swagger.yaml`, and `docs.go`.
- **Reference**: [Swagger/OpenAPI Documentation](memory-bank/features/SWAGGER_API_DOCUMENTATION.md)
- **Benefits**: Interactive API documentation, automated client generation, and consistent API contracts.

## 10. Database Migrations
- **Tool**: Golang-migrate.
- **Implementation**: SQL migration scripts in `internal/infra/persistence/migrations`.
- **Benefits**: Version-controlled schema changes, reliable database updates.

## 11. Feature-Specific Patterns

### Expiration Tracking
- **Logic**: Smart status classification (Warning, Alert, Critical, Expired) based on configurable thresholds.
- **Notifications**: Multi-channel notification system (Email, Telegram, Supabase, Webhook).
- **Reference**: [Expiration Tracking Examples](memory-bank/features/EXPIRATION_TRACKING_EXAMPLES.md)

### Recipe Management
- **Features**: Rich recipe details, ingredient management with unit conversion, step-by-step instructions, nutritional info, media, tags, reviews, collections.
- **Integration**: Real-time inventory availability checks and smart shopping list generation.
- **Reference**: [Recipe Management System Documentation](memory-bank/features/RECIPE_MANAGEMENT_SYSTEM.md)
- **Consumption**: [Recipe Ingredient Consumption Examples](memory-bank/features/RECIPE_CONSUMPTION_EXAMPLES.md)

### Shopping List Management
- **Features**: Create/manage lists, add items (product variant or free text), purchase tracking, bulk operations, statistics.
- **Generation**: Automated generation based on low stock, expiring items, recipe needs, and consumption patterns.
- **References**:
    - [Shopping List API Documentation](memory-bank/features/SHOPPING_LIST_API.md)
    - [Shopping List Feature Documentation](memory-bank/features/SHOPPING_LIST_FEATURE.md)
    - [Shopping List Examples](memory-bank/features/SHOPPING_LIST_EXAMPLES.md)
    - [Shopping List Implementation](memory-bank/features/SHOPPING_LIST_IMPLEMENTATION.md)

## 12. Future Architectural Considerations
- **CQRS/Event Sourcing**: For complex domain events and read model optimization.
- **Microservices**: Potential future decomposition for larger scale.
- **Advanced Analytics**: Dedicated service for data processing and insights.