# API Integration Guide for Next.js PWA

## Overview

This guide provides comprehensive information for integrating the Pantry Pal API with a Next.js PWA application. It covers offline-first strategies, service worker integration, and PWA-specific API patterns using modern Next.js features.

## Next.js PWA API Configuration

### Environment Setup

```typescript
// next.config.js
const nextConfig = {
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1',
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  }
};

// .env.local
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your_vapid_key
```

### PWA-Aware API Client Configuration

```typescript
// src/lib/api/config.ts
export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL!,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  // PWA-specific settings
  retryAttempts: 3,
  retryDelay: 1000,
  offlineQueueEnabled: true,
  cacheStrategy: 'network-first',
};
```

## Authentication

### JWT Token Management

The API uses JWT Bearer tokens for authentication with automatic refresh capability.

```typescript
interface TokenPair {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: 'Bearer';
}

interface AuthResponse {
  success: boolean;
  data: {
    user: UserProfile;
    tokens: TokenPair;
  };
  message: string;
  timestamp: string;
}
```

### Authentication Endpoints

#### Register User

```http
POST /auth/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "confirm_password": "securepassword123",
  "first_name": "John",
  "last_name": "Doe"
}
```

#### Login User

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

#### Refresh Token

```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Logout

```http
POST /auth/logout
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Standard Response Format

All API responses follow a consistent structure:

```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ErrorInfo;
  message?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

interface ErrorInfo {
  code: string;
  message: string;
  details?: Record<string, any>;
}

interface PaginatedResponse<T = any> extends APIResponse<T> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}
```

## Core API Endpoints

### User Management

#### Get User Profile

```http
GET /users/profile
Authorization: Bearer {access_token}
```

#### Update User Profile

```http
PUT /users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "profile_picture_url": "https://example.com/avatar.jpg"
}
```

#### Change Password

```http
POST /users/change-password
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "current_password": "oldpassword",
  "new_password": "newpassword123",
  "confirm_password": "newpassword123"
}
```

### Pantry Management

#### Create Pantry

```http
POST /pantries
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "Family Kitchen",
  "description": "Main family pantry and refrigerator"
}
```

#### Get User Pantries

```http
GET /pantries
Authorization: Bearer {access_token}
```

#### Get Pantry Details

```http
GET /pantries/{pantryId}
Authorization: Bearer {access_token}
```

#### Update Pantry

```http
PUT /pantries/{pantryId}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "Updated Pantry Name",
  "description": "Updated description"
}
```

#### Delete Pantry

```http
DELETE /pantries/{pantryId}
Authorization: Bearer {access_token}
```

### Pantry Membership

#### Invite Member

```http
POST /pantries/{pantryId}/members
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "editor"
}
```

#### Get Pantry Members

```http
GET /pantries/{pantryId}/members
Authorization: Bearer {access_token}
```

#### Update Member Role

```http
PUT /pantries/{pantryId}/members/{memberId}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "role": "admin"
}
```

#### Remove Member

```http
DELETE /pantries/{pantryId}/members/{memberId}
Authorization: Bearer {access_token}
```

### Inventory Management

#### Create Inventory Item

```http
POST /pantries/{pantryId}/inventory
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "product_variant_id": "uuid",
  "quantity": 2.5,
  "unit_of_measure_id": "uuid",
  "location_id": "uuid",
  "expiration_date": "2024-12-31T00:00:00Z",
  "purchase_date": "2024-01-15T00:00:00Z",
  "purchase_price": 4.99,
  "notes": "Organic brand"
}
```

#### Get Pantry Inventory

```http
GET /pantries/{pantryId}/inventory?page=1&limit=20&search=milk&location_id=uuid&category_id=uuid&expiring_soon=true
Authorization: Bearer {access_token}
```

#### Update Inventory Item

```http
PUT /pantries/{pantryId}/inventory/{itemId}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "quantity": 1.5,
  "expiration_date": "2024-12-25T00:00:00Z",
  "notes": "Updated notes"
}
```

#### Consume Inventory Item

```http
POST /pantries/{pantryId}/inventory/{itemId}/consume
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "consumed_quantity": 0.5,
  "notes": "Used for breakfast"
}
```

### Product Catalog

#### Get Categories

```http
GET /catalog/categories?page=1&limit=50
Authorization: Bearer {access_token}
```

#### Get Products

```http
GET /catalog/products?page=1&limit=20&search=milk&category_id=uuid
Authorization: Bearer {access_token}
```

#### Get Product Details

```http
GET /catalog/products/{productId}
Authorization: Bearer {access_token}
```

#### Get Product Variants

```http
GET /catalog/products/{productId}/variants
Authorization: Bearer {access_token}
```

### Recipe Management

#### Create Recipe

```http
POST /recipes
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "title": "Chocolate Chip Cookies",
  "description": "Classic homemade cookies",
  "cuisine": "American",
  "category": "Dessert",
  "difficulty": "easy",
  "prep_time": 15,
  "cook_time": 12,
  "servings": 24,
  "is_public": false,
  "ingredients": [
    {
      "product_variant_id": "uuid",
      "quantity": 2.25,
      "unit_of_measure_id": "uuid",
      "notes": "All-purpose flour"
    }
  ],
  "instructions": [
    {
      "step_number": 1,
      "instruction": "Preheat oven to 375°F",
      "duration": 5
    }
  ],
  "nutrition": {
    "calories": 150,
    "protein": 2.0,
    "carbohydrates": 20.0,
    "fat": 7.0
  }
}
```

#### Get User Recipes

```http
GET /recipes?page=1&limit=20&search=cookie&cuisine=American&difficulty=easy&is_favorite=true
Authorization: Bearer {access_token}
```

#### Check Recipe Ingredients

```http
POST /recipes/{recipeId}/check-ingredients
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "pantry_id": "uuid"
}
```

### Shopping Lists

#### Generate Shopping List

```http
POST /pantries/{pantryId}/inventory/shopping-list
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "low_stock_threshold": 1.0,
  "include_expired": true,
  "recipe_ids": ["uuid1", "uuid2"]
}
```

## Error Handling

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_FAILED` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `CONFLICT` | 409 | Resource conflict |
| `BUSINESS_RULE_VIOLATION` | 422 | Business rule violation |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INTERNAL_SERVER_ERROR` | 500 | Server error |

### Error Response Example

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": {
      "fields": {
        "email": "Invalid email format",
        "password": "Password must be at least 8 characters"
      }
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

## Idempotency

For critical operations, include an idempotency key to prevent duplicate operations:

```http
POST /pantries/{pantryId}/inventory
Authorization: Bearer {access_token}
Idempotency-Key: unique-operation-key-123
Content-Type: application/json
```

## Rate Limiting

The API implements rate limiting. Monitor these headers:

* `X-RateLimit-Limit`: Request limit per window
* `X-RateLimit-Remaining`: Remaining requests in current window
* `X-RateLimit-Reset`: Time when the rate limit resets

## Pagination

List endpoints support pagination with these parameters:

* `page`: Page number (default: 1)
* `limit`: Items per page (default: 10, max: 100)
* `sort_by`: Sort field
* `sort_order`: `asc` or `desc`

## Bulk Operations

### Bulk Create Inventory Items

```http
POST /pantries/{pantryId}/inventory/bulk
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "items": [
    {
      "product_variant_id": "uuid1",
      "quantity": 2.0,
      "unit_of_measure_id": "uuid",
      "location_id": "uuid"
    },
    {
      "product_variant_id": "uuid2",
      "quantity": 1.5,
      "unit_of_measure_id": "uuid",
      "location_id": "uuid"
    }
  ]
}
```

### Bulk Update Inventory Items

```http
PUT /inventory/bulk
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "updates": [
    {
      "id": "uuid1",
      "quantity": 1.0
    },
    {
      "id": "uuid2",
      "expiration_date": "2024-12-31T00:00:00Z"
    }
  ]
}
```

### Bulk Consume Items

```http
POST /inventory/bulk/consume
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "consumptions": [
    {
      "id": "uuid1",
      "consumed_quantity": 0.5
    },
    {
      "id": "uuid2",
      "consumed_quantity": 1.0
    }
  ]
}
```

### Consume Recipe Ingredients

```http
POST /pantries/{pantryId}/inventory/recipe/consume
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "recipe_id": "uuid",
  "servings": 4,
  "notes": "Dinner preparation"
}
```

## Expiration Tracking

### Track Expiring Items

```http
POST /pantries/{pantryId}/expiration/track
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "alert_days": 3,
  "critical_days": 1,
  "category_filters": ["dairy", "meat"]
}
```

### Configure Expiration Alerts

```http
POST /pantries/{pantryId}/expiration/alerts
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "enabled": true,
  "alert_days": 3,
  "warning_days": 7,
  "critical_days": 1,
  "min_value": 1.0,
  "channels": [
    {
      "type": "email",
      "enabled": true,
      "config": {
        "email": "<EMAIL>"
      }
    },
    {
      "type": "push",
      "enabled": true
    }
  ],
  "quiet_hours": {
    "enabled": true,
    "start_time": "22:00",
    "end_time": "08:00",
    "timezone": "America/New_York"
  },
  "category_filters": ["dairy", "meat", "produce"]
}
```

## Pantry Locations

### Create Location

```http
POST /pantries/{pantryId}/locations
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "Freezer",
  "description": "Main freezer compartment",
  "location_type": "freezer"
}
```

### Get Pantry Locations

```http
GET /pantries/{pantryId}/locations
Authorization: Bearer {access_token}
```

## Units of Measure

### Get Units

```http
GET /catalog/units?page=1&limit=50
Authorization: Bearer {access_token}
```

### Create Derived Unit

```http
POST /catalog/units/derived
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "tablespoon",
  "symbol": "tbsp",
  "base_unit_id": "uuid",
  "conversion_factor": 0.0625
}
```

## WebSocket Integration (Future)

For real-time updates, the API will support WebSocket connections:

```typescript
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onmessage = (event) => {
  const notification = JSON.parse(event.data);
  // Handle real-time notifications
};
```

## Next.js PWA API Patterns

### Server Components vs Client Components

```typescript
// Server Component (app/pantries/page.tsx)
import { getPantries } from '@/lib/api/pantries';

export default async function PantriesPage() {
  // Fetch data on server for initial render
  const initialPantries = await getPantries();

  return (
    <div>
      <PantriesList initialData={initialPantries} />
    </div>
  );
}

// Client Component with PWA features
'use client';
import { usePantries } from '@/lib/hooks/use-pantries';
import { usePWA } from '@/lib/pwa/pwa-context';

export function PantriesList({ initialData }: { initialData: Pantry[] }) {
  const { isOnline } = usePWA();
  const { data: pantries, isLoading, error } = usePantries(initialData);

  if (!isOnline && !pantries) {
    return <OfflineMessage />;
  }

  return (
    <div>
      {pantries?.map(pantry => (
        <PantryCard key={pantry.id} pantry={pantry} />
      ))}
    </div>
  );
}
```

### Route Handlers for API Proxy

```typescript
// app/api/pantries/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const response = await fetch(`${process.env.API_URL}/pantries`, {
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch pantries' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();

    const response = await fetch(`${process.env.API_URL}/pantries`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create pantry' },
      { status: 500 }
    );
  }
}
```

### Server Actions for Forms

```typescript
// app/actions/pantry-actions.ts
'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { createPantrySchema } from '@/lib/validations/pantry';

export async function createPantryAction(
  prevState: any,
  formData: FormData
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return { error: 'Unauthorized' };
  }

  const validatedFields = createPantrySchema.safeParse({
    name: formData.get('name'),
    description: formData.get('description'),
  });

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
    };
  }

  try {
    const response = await fetch(`${process.env.API_URL}/pantries`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(validatedFields.data),
    });

    if (!response.ok) {
      throw new Error('Failed to create pantry');
    }

    const pantry = await response.json();

    revalidatePath('/pantries');
    redirect(`/pantries/${pantry.data.id}`);
  } catch (error) {
    return { error: 'Failed to create pantry' };
  }
}
```

### PWA-Specific Hooks

```typescript
// src/lib/hooks/use-offline-sync.ts
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { usePWA } from '@/lib/pwa/pwa-context';
import { syncManager } from '@/lib/sync/sync-manager';

export function useOfflineSync() {
  const { isOnline } = usePWA();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (isOnline) {
      // Trigger sync when coming online
      syncManager.startSync().then(() => {
        // Invalidate all queries to refetch fresh data
        queryClient.invalidateQueries();
      });
    }
  }, [isOnline, queryClient]);

  return {
    triggerSync: () => syncManager.startSync(),
    isOnline,
  };
}

// src/lib/hooks/use-pantries.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { usePWA } from '@/lib/pwa/pwa-context';
import { pantryService } from '@/lib/api/pantries';
import { QUERY_KEYS } from '@/lib/constants/query-keys';

export function usePantries(initialData?: Pantry[]) {
  const { isOnline } = usePWA();

  return useQuery({
    queryKey: QUERY_KEYS.pantries,
    queryFn: () => pantryService.getPantries(),
    initialData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: isOnline,
    refetchOnReconnect: true,
    retry: (failureCount, error: any) => {
      // Don't retry if offline
      if (!navigator.onLine) return false;
      return failureCount < 3;
    },
  });
}

export function useCreatePantry() {
  const queryClient = useQueryClient();
  const { isOnline } = usePWA();

  return useMutation({
    mutationFn: pantryService.createPantry,
    onMutate: async (newPantry) => {
      // Optimistic update
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.pantries });

      const previousPantries = queryClient.getQueryData(QUERY_KEYS.pantries);

      queryClient.setQueryData(QUERY_KEYS.pantries, (old: Pantry[] = []) => [
        ...old,
        { ...newPantry, id: `temp_${Date.now()}`, synced: isOnline }
      ]);

      return { previousPantries };
    },
    onError: (err, newPantry, context) => {
      // Rollback on error
      queryClient.setQueryData(QUERY_KEYS.pantries, context?.previousPantries);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.pantries });
    },
  });
}
```

## Next Steps

1. **PWA Setup**: Follow [NEXTJS_PWA_GUIDE.md](./NEXTJS_PWA_GUIDE.md) for complete PWA configuration
2. **Authentication**: Review [AUTHENTICATION_GUIDE.md](./AUTHENTICATION_GUIDE.md) for Next.js auth implementation
3. **Error Handling**: Check [ERROR_HANDLING_GUIDE.md](./ERROR_HANDLING_GUIDE.md) for PWA error patterns
4. **Data Models**: Explore [DATA_MODELS.md](./DATA_MODELS.md) for Next.js PWA TypeScript interfaces
5. **Components**: Use [NEXTJS_PWA_COMPONENTS.md](./NEXTJS_PWA_COMPONENTS.md) for PWA-specific components
