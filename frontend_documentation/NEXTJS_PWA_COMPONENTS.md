# Next.js PWA Components Guide

## Overview

This guide provides reusable components specifically designed for the Pantry Pal PWA, including offline-aware components, PWA-specific UI elements, and mobile-optimized interfaces.

## PWA-Specific Components

### 1. Install App Banner ( `src/components/pwa/install-banner.tsx` )

```typescript
'use client';

import { useState, useEffect } from 'react';
import { usePWA } from '@/lib/pwa/pwa-context';
import { XMarkIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

export function InstallBanner() {
  const { isInstallable, isInstalled, installApp } = usePWA();
  const [dismissed, setDismissed] = useState(false);

  useEffect(() => {
    const wasDismissed = localStorage.getItem('install-banner-dismissed');
    setDismissed(wasDismissed === 'true');
  }, []);

  const handleDismiss = () => {
    setDismissed(true);
    localStorage.setItem('install-banner-dismissed', 'true');
  };

  const handleInstall = async () => {
    await installApp();
    setDismissed(true);
  };

  if (!isInstallable || isInstalled || dismissed) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-emerald-600 text-white p-4 shadow-lg">
      <div className="flex items-center justify-between max-w-md mx-auto">
        <div className="flex items-center space-x-3">
          <ArrowDownTrayIcon className="h-6 w-6" />
          <div>
            <p className="font-medium">Install Pantry Pal</p>
            <p className="text-sm text-emerald-100">
              Get the full app experience
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleInstall}
            className="bg-white text-emerald-600 px-4 py-2 rounded-lg font-medium text-sm hover:bg-emerald-50 transition-colors"
          >
            Install
          </button>
          <button
            onClick={handleDismiss}
            className="p-1 hover:bg-emerald-700 rounded"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
}
```

### 2. Offline Indicator ( `src/components/pwa/offline-indicator.tsx` )

```typescript
'use client';

import { usePWA } from '@/lib/pwa/pwa-context';
import { WifiIcon, WifiSlashIcon } from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

export function OfflineIndicator() {
  const { isOnline } = usePWA();

  return (
    <div
      className={clsx(
        'fixed top-4 right-4 z-50 flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300',
        isOnline
          ? 'bg-emerald-100 text-emerald-800 translate-y-[-100px] opacity-0'
          : 'bg-red-100 text-red-800 translate-y-0 opacity-100'
      )}
    >
      {isOnline ? (
        <WifiIcon className="h-4 w-4" />
      ) : (
        <WifiSlashIcon className="h-4 w-4" />
      )}
      <span>{isOnline ? 'Online' : 'Offline'}</span>
    </div>
  );
}
```

### 3. Update Available Banner ( `src/components/pwa/update-banner.tsx` )

```typescript
'use client';

import { usePWA } from '@/lib/pwa/pwa-context';
import { ArrowPathIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

export function UpdateBanner() {
  const { updateAvailable, updateApp } = usePWA();
  const [dismissed, setDismissed] = useState(false);

  if (!updateAvailable || dismissed) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-blue-600 text-white p-3 shadow-lg">
      <div className="flex items-center justify-between max-w-md mx-auto">
        <div className="flex items-center space-x-3">
          <ArrowPathIcon className="h-5 w-5" />
          <span className="font-medium">Update available</span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={updateApp}
            className="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium hover:bg-blue-50 transition-colors"
          >
            Update
          </button>
          <button
            onClick={() => setDismissed(true)}
            className="p-1 hover:bg-blue-700 rounded"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
```

### 4. PWA Status Bar ( `src/components/pwa/pwa-status.tsx` )

```typescript
'use client';

import { usePWA } from '@/lib/pwa/pwa-context';
import { InstallBanner } from './install-banner';
import { OfflineIndicator } from './offline-indicator';
import { UpdateBanner } from './update-banner';

export function PWAStatus() {
  return (
    <>
      <UpdateBanner />
      <OfflineIndicator />
      <InstallBanner />
    </>
  );
}
```

## Mobile-Optimized Components

### 1. Mobile Navigation ( `src/components/layout/mobile-nav.tsx` )

```typescript
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { clsx } from 'clsx';
import {
  HomeIcon,
  ArchiveBoxIcon,
  BookOpenIcon,
  ShoppingBagIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  ArchiveBoxIcon as ArchiveBoxIconSolid,
  BookOpenIcon as BookOpenIconSolid,
  ShoppingBagIcon as ShoppingBagIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid,
} from '@heroicons/react/24/solid';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, iconSolid: HomeIconSolid },
  { name: 'Inventory', href: '/inventory', icon: ArchiveBoxIcon, iconSolid: ArchiveBoxIconSolid },
  { name: 'Recipes', href: '/recipes', icon: BookOpenIcon, iconSolid: BookOpenIconSolid },
  { name: 'Shopping', href: '/shopping-list', icon: ShoppingBagIcon, iconSolid: ShoppingBagIconSolid },
  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, iconSolid: Cog6ToothIconSolid },
];

export function MobileNav() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  return (
    <>
      {/* Mobile menu button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsOpen(true)}
          className="p-2 rounded-lg bg-white shadow-lg border border-gray-200"
        >
          <Bars3Icon className="h-6 w-6 text-gray-600" />
        </button>
      </div>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div className="md:hidden fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setIsOpen(false)} />
      )}

      {/* Mobile menu */}
      <div
        className={clsx(
          'md:hidden fixed top-0 left-0 h-full w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out z-50',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-xl font-bold text-gray-900">Pantry Pal</h2>
            <button
              onClick={() => setIsOpen(false)}
              className="p-2 rounded-lg hover:bg-gray-100"
            >
              <XMarkIcon className="h-6 w-6 text-gray-600" />
            </button>
          </div>

          <nav className="space-y-2">
            {navigation.map((item) => {
              const isActive = pathname.startsWith(item.href);
              const Icon = isActive ? item.iconSolid : item.icon;

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsOpen(false)}
                  className={clsx(
                    'flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    isActive
                      ? 'bg-emerald-100 text-emerald-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Bottom navigation for mobile */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 safe-area-pb">
        <div className="flex justify-around">
          {navigation.slice(0, 4).map((item) => {
            const isActive = pathname.startsWith(item.href);
            const Icon = isActive ? item.iconSolid : item.icon;

            return (
              <Link
                key={item.name}
                href={item.href}
                className={clsx(
                  'flex flex-col items-center space-y-1 px-2 py-1 rounded-lg transition-colors',
                  isActive
                    ? 'text-emerald-600'
                    : 'text-gray-400 hover:text-gray-600'
                )}
              >
                <Icon className="h-6 w-6" />
                <span className="text-xs font-medium">{item.name}</span>
              </Link>
            );
          })}
        </div>
      </div>
    </>
  );
}
```

### 3. Swipe Actions ( `src/components/ui/swipe-actions.tsx` )

```typescript
'use client';

import { useState, useRef, ReactNode } from 'react';
import { clsx } from 'clsx';

interface SwipeAction {
  id: string;
  label: string;
  icon: ReactNode;
  color: 'red' | 'blue' | 'green' | 'yellow';
  onAction: () => void;
}

interface SwipeActionsProps {
  children: ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  disabled?: boolean;
}

export function SwipeActions({
  children,
  leftActions = [],
  rightActions = [],
  disabled = false,
}: SwipeActionsProps) {
  const [swipeDistance, setSwipeDistance] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const startX = useRef(0);
  const currentX = useRef(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const actionWidth = 80;
  const threshold = 60;

  const colorClasses = {
    red: 'bg-red-500 text-white',
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    yellow: 'bg-yellow-500 text-white',
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled) return;
    startX.current = e.touches[0].clientX;
    setIsAnimating(false);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (disabled || !startX.current) return;

    currentX.current = e.touches[0].clientX;
    const distance = currentX.current - startX.current;

    // Limit swipe distance
    const maxLeft = leftActions.length * actionWidth;
    const maxRight = rightActions.length * actionWidth;
    const limitedDistance = Math.max(-maxRight, Math.min(maxLeft, distance));

    setSwipeDistance(limitedDistance);
  };

  const handleTouchEnd = () => {
    if (disabled) return;

    setIsAnimating(true);

    // Check if any action should be triggered
    if (swipeDistance > threshold && leftActions.length > 0) {
      const actionIndex = Math.min(
        Math.floor(swipeDistance / actionWidth),
        leftActions.length - 1
      );
      leftActions[actionIndex]?.onAction();
    } else if (swipeDistance < -threshold && rightActions.length > 0) {
      const actionIndex = Math.min(
        Math.floor(Math.abs(swipeDistance) / actionWidth),
        rightActions.length - 1
      );
      rightActions[actionIndex]?.onAction();
    }

    // Reset position
    setTimeout(() => {
      setSwipeDistance(0);
      startX.current = 0;
      currentX.current = 0;
    }, 100);
  };

  return (
    <div ref={containerRef} className="relative overflow-hidden">
      {/* Left actions */}
      {leftActions.length > 0 && (
        <div className="absolute left-0 top-0 bottom-0 flex">
          {leftActions.map((action, index) => (
            <div
              key={action.id}
              className={clsx(
                'flex items-center justify-center w-20 transition-transform duration-200',
                colorClasses[action.color]
              )}
              style={{
                transform: `translateX(${Math.max(0, swipeDistance - (index + 1) * actionWidth)}px)`,
              }}
            >
              <div className="flex flex-col items-center space-y-1">
                {action.icon}
                <span className="text-xs font-medium">{action.label}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Right actions */}
      {rightActions.length > 0 && (
        <div className="absolute right-0 top-0 bottom-0 flex">
          {rightActions.map((action, index) => (
            <div
              key={action.id}
              className={clsx(
                'flex items-center justify-center w-20 transition-transform duration-200',
                colorClasses[action.color]
              )}
              style={{
                transform: `translateX(${Math.min(0, swipeDistance + (index + 1) * actionWidth)}px)`,
              }}
            >
              <div className="flex flex-col items-center space-y-1">
                {action.icon}
                <span className="text-xs font-medium">{action.label}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Main content */}
      <div
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        className={clsx(
          'relative bg-white',
          isAnimating && 'transition-transform duration-200 ease-out'
        )}
        style={{
          transform: `translateX(${swipeDistance}px)`,
        }}
      >
        {children}
      </div>
    </div>
  );
}
```

## Offline-Aware Components

### 1. Offline Form ( `src/components/forms/offline-form.tsx` )

```typescript
'use client';

import { useState, useEffect } from 'react';
import { usePWA } from '@/lib/pwa/pwa-context';
import { offlineDB } from '@/lib/db/offline-db';
import { toast } from 'react-hot-toast';
import { CloudArrowUpIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface OfflineFormProps {
  onSubmit: (data: any) => Promise<void>;
  children: React.ReactNode;
  formId: string;
  className?: string;
}

export function OfflineForm({ onSubmit, children, formId, className }: OfflineFormProps) {
  const { isOnline } = usePWA();
  const [pendingSubmissions, setPendingSubmissions] = useState(0);

  useEffect(() => {
    // Check for pending submissions
    const checkPending = async () => {
      const queue = await offlineDB.getSyncQueue();
      const pending = queue.filter(item => item.id.startsWith(formId));
      setPendingSubmissions(pending.length);
    };

    checkPending();
  }, [formId]);

  const handleSubmit = async (data: any) => {
    try {
      if (isOnline) {
        await onSubmit(data);
        toast.success('Saved successfully');
      } else {
        // Save to offline queue
        await offlineDB.addToSyncQueue('create', formId, data);
        setPendingSubmissions(prev => prev + 1);
        toast.success('Saved offline - will sync when online');
      }
    } catch (error) {
      if (!isOnline) {
        // Fallback to offline storage
        await offlineDB.addToSyncQueue('create', formId, data);
        setPendingSubmissions(prev => prev + 1);
        toast.success('Saved offline - will sync when online');
      } else {
        toast.error('Failed to save');
        throw error;
      }
    }
  };

  return (
    <div className={className}>
      {/* Offline status indicator */}
      {!isOnline && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center space-x-2">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
          <span className="text-sm text-yellow-800">
            You're offline. Changes will be saved locally and synced when online.
          </span>
        </div>
      )}

      {/* Pending submissions indicator */}
      {pendingSubmissions > 0 && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-center space-x-2">
          <CloudArrowUpIcon className="h-5 w-5 text-blue-600" />
          <span className="text-sm text-blue-800">
            {pendingSubmissions} item(s) waiting to sync
          </span>
        </div>
      )}

      {/* Form content */}
      <form onSubmit={(e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const data = Object.fromEntries(formData.entries());
        handleSubmit(data);
      }}>
        {children}
      </form>
    </div>
  );
}
```

### 2. Cached Image ( `src/components/ui/cached-image.tsx` )

```typescript
'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { PhotoIcon } from '@heroicons/react/24/outline';

interface CachedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallback?: React.ReactNode;
}

export function CachedImage({
  src,
  alt,
  width = 400,
  height = 300,
  className,
  fallback,
}: CachedImageProps) {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const loadImage = async () => {
      try {
        setIsLoading(true);
        setHasError(false);

        // Try to load from cache first
        if ('caches' in window) {
          const cache = await caches.open('static-image-assets');
          const cachedResponse = await cache.match(src);

          if (cachedResponse) {
            const blob = await cachedResponse.blob();
            const objectUrl = URL.createObjectURL(blob);
            setImageSrc(objectUrl);
            setIsLoading(false);
            return;
          }
        }

        // Fallback to network
        const response = await fetch(src);
        if (response.ok) {
          const blob = await response.blob();
          const objectUrl = URL.createObjectURL(blob);
          setImageSrc(objectUrl);

          // Cache the image
          if ('caches' in window) {
            const cache = await caches.open('static-image-assets');
            await cache.put(src, response.clone());
          }
        } else {
          throw new Error('Failed to load image');
        }
      } catch (error) {
        console.error('Error loading image:', error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();

    return () => {
      if (imageSrc && imageSrc.startsWith('blob:')) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [src]);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`}>
        <div className="animate-pulse">
          <PhotoIcon className="h-8 w-8 text-gray-400" />
        </div>
      </div>
    );
  }

  if (hasError || !imageSrc) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`}>
        {fallback || <PhotoIcon className="h-8 w-8 text-gray-400" />}
      </div>
    );
  }

  return (
    <Image
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onError={() => setHasError(true)}
    />
  );
}
```

### 2. Pull to Refresh ( `src/components/ui/pull-to-refresh.tsx` )

```typescript
'use client';

import { useState, useRef, useCallback, ReactNode } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: ReactNode;
  disabled?: boolean;
}

export function PullToRefresh({ onRefresh, children, disabled = false }: PullToRefreshProps) {
  const [isPulling, setIsPulling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const startY = useRef(0);
  const currentY = useRef(0);

  const threshold = 80;
  const maxPull = 120;

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (disabled || window.scrollY > 0) return;
    startY.current = e.touches[0].clientY;
  }, [disabled]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (disabled || window.scrollY > 0 || !startY.current) return;

    currentY.current = e.touches[0].clientY;
    const distance = currentY.current - startY.current;

    if (distance > 0) {
      e.preventDefault();
      const pullDistance = Math.min(distance * 0.5, maxPull);
      setPullDistance(pullDistance);
      setIsPulling(pullDistance > threshold);
    }
  }, [disabled, threshold, maxPull]);

  const handleTouchEnd = useCallback(async () => {
    if (disabled) return;

    if (isPulling && pullDistance > threshold) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }

    setIsPulling(false);
    setPullDistance(0);
    startY.current = 0;
    currentY.current = 0;
  }, [disabled, isPulling, pullDistance, threshold, onRefresh]);

  return (
    <div
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      className="relative"
    >
      {/* Pull indicator */}
      <div
        className={clsx(
          'absolute top-0 left-0 right-0 flex items-center justify-center transition-all duration-200 ease-out',
          pullDistance > 0 ? 'opacity-100' : 'opacity-0'
        )}
        style={{
          transform: `translateY(${Math.max(0, pullDistance - 40)}px)`,
          height: `${Math.max(0, pullDistance)}px`,
        }}
      >
        <div className="flex flex-col items-center space-y-2 text-gray-500">
          <ArrowPathIcon
            className={clsx(
              'h-6 w-6 transition-transform duration-200',
              isRefreshing && 'animate-spin',
              isPulling && 'rotate-180'
            )}
          />
          <span className="text-sm font-medium">
            {isRefreshing ? 'Refreshing...' : isPulling ? 'Release to refresh' : 'Pull to refresh'}
          </span>
        </div>
      </div>

      {/* Content */}
      <div
        style={{
          transform: `translateY(${pullDistance}px)`,
          transition: pullDistance === 0 ? 'transform 0.2s ease-out' : 'none',
        }}
      >
        {children}
      </div>
    </div>
  );
}
```
