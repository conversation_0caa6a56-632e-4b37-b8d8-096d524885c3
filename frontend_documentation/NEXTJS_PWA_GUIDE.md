# Next.js PWA Development Guide for Pantry Pal

## Overview

This guide provides comprehensive instructions for building a Progressive Web App (PWA) using Next.js for the Pantry Pal application. The PWA will provide native app-like experience with offline capabilities, push notifications, and optimized performance.

## Project Setup

### 1. Initialize Next.js Project

```bash
npx create-next-app@latest pantry-pal-frontend --typescript --tailwind --eslint --app
cd pantry-pal-frontend
```

### 2. Install PWA Dependencies

```bash
npm install next-pwa workbox-webpack-plugin
npm install -D @types/node
```

### 3. Install Additional Dependencies

```bash
# State Management & API
npm install @tanstack/react-query axios zustand

# UI Components & Styling
npm install @headlessui/react @heroicons/react clsx tailwind-merge
npm install react-hot-toast react-hook-form @hookform/resolvers zod

# PWA & Offline Support
npm install idb localforage

# Authentication & Security
npm install jose

# Development Tools
npm install -D @types/react @types/react-dom
```

## PWA Configuration

### 1. Next.js Configuration ( `next.config.js` )

```javascript
const withPWA = require('next-pwa')({
    dest: 'public',
    register: true,
    skipWaiting: true,
    disable: process.env.NODE_ENV === 'development',
    buildExcludes: [/middleware-manifest\.json$/],
    runtimeCaching: [{
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
                cacheName: 'google-fonts',
                expiration: {
                    maxEntries: 4,
                    maxAgeSeconds: 365 * 24 * 60 * 60 // 1 year
                }
            }
        },
        {
            urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
                cacheName: 'google-fonts-static',
                expiration: {
                    maxEntries: 4,
                    maxAgeSeconds: 365 * 24 * 60 * 60 // 1 year
                }
            }
        },
        {
            urlPattern: /\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,
            handler: 'StaleWhileRevalidate',
            options: {
                cacheName: 'static-image-assets',
                expiration: {
                    maxEntries: 64,
                    maxAgeSeconds: 24 * 60 * 60 // 24 hours
                }
            }
        },
        {
            urlPattern: /\/_next\/image\?url=.+$/i,
            handler: 'StaleWhileRevalidate',
            options: {
                cacheName: 'next-image',
                expiration: {
                    maxEntries: 64,
                    maxAgeSeconds: 24 * 60 * 60 // 24 hours
                }
            }
        },
        {
            urlPattern: /\/api\/v1\/.*/i,
            handler: 'NetworkFirst',
            options: {
                cacheName: 'api-cache',
                expiration: {
                    maxEntries: 32,
                    maxAgeSeconds: 5 * 60 // 5 minutes
                },
                networkTimeoutSeconds: 10
            }
        }
    ]
});

/** @type {import('next').NextConfig} */
const nextConfig = {
    experimental: {
        appDir: true,
    },
    images: {
        domains: ['localhost', 'api.pantrypal.com'],
    },
    env: {
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1',
        NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    }
};

module.exports = withPWA(nextConfig);
```

### 2. Web App Manifest ( `public/manifest.json` )

```json
{
  "name": "Pantry Pal",
  "short_name": "PantryPal",
  "description": "Smart pantry management for reducing food waste",
  "theme_color": "#10b981",
  "background_color": "#ffffff",
  "display": "standalone",
  "orientation": "portrait",
  "scope": "/",
  "start_url": "/",
  "icons": [
    {
      "src": "/icons/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable any"
    }
  ],
  "shortcuts": [
    {
      "name": "Add Item",
      "short_name": "Add Item",
      "description": "Quickly add an item to inventory",
      "url": "/inventory/add",
      "icons": [
        {
          "src": "/icons/add-item-96x96.png",
          "sizes": "96x96"
        }
      ]
    },
    {
      "name": "Shopping List",
      "short_name": "Shopping",
      "description": "View shopping list",
      "url": "/shopping-list",
      "icons": [
        {
          "src": "/icons/shopping-96x96.png",
          "sizes": "96x96"
        }
      ]
    },
    {
      "name": "Recipes",
      "short_name": "Recipes",
      "description": "Browse recipes",
      "url": "/recipes",
      "icons": [
        {
          "src": "/icons/recipes-96x96.png",
          "sizes": "96x96"
        }
      ]
    }
  ],
  "categories": ["food", "lifestyle", "productivity"],
  "screenshots": [
    {
      "src": "/screenshots/desktop-1.png",
      "sizes": "1280x720",
      "type": "image/png",
      "form_factor": "wide"
    },
    {
      "src": "/screenshots/mobile-1.png",
      "sizes": "375x812",
      "type": "image/png",
      "form_factor": "narrow"
    }
  ]
}
```

## Project Structure

```
pantry-pal-frontend/
├── src/
│   ├── app/                    # Next.js 13+ App Router
│   │   ├── (auth)/            # Auth route group
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── (dashboard)/       # Protected route group
│   │   │   ├── dashboard/
│   │   │   ├── pantries/
│   │   │   ├── inventory/
│   │   │   ├── recipes/
│   │   │   └── shopping-list/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── loading.tsx
│   │   ├── error.tsx
│   │   ├── not-found.tsx
│   │   └── page.tsx
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Base UI components
│   │   ├── forms/            # Form components
│   │   ├── layout/           # Layout components
│   │   └── features/         # Feature-specific components
│   ├── lib/                  # Utilities and configurations
│   │   ├── api/              # API client and services
│   │   ├── auth/             # Authentication utilities
│   │   ├── db/               # Offline database utilities
│   │   ├── hooks/            # Custom React hooks
│   │   ├── store/            # State management
│   │   ├── utils/            # General utilities
│   │   └── validations/      # Form validation schemas
│   ├── types/                # TypeScript type definitions
│   └── styles/               # Additional styles
├── public/
│   ├── icons/                # PWA icons
│   ├── screenshots/          # App screenshots
│   ├── manifest.json         # PWA manifest
│   └── sw.js                 # Service worker (auto-generated)
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
└── package.json
```

## Core Implementation

### 1. Root Layout ( `src/app/layout.tsx` )

```typescript
import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';
import { Toaster } from 'react-hot-toast';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'Pantry Pal',
    template: '%s | Pantry Pal'
  },
  description: 'Smart pantry management for reducing food waste',
  keywords: ['pantry', 'food', 'inventory', 'recipes', 'shopping list'],
  authors: [{ name: 'Pantry Pal Team' }],
  creator: 'Pantry Pal',
  publisher: 'Pantry Pal',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Pantry Pal',
    description: 'Smart pantry management for reducing food waste',
    siteName: 'Pantry Pal',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Pantry Pal',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Pantry Pal',
    description: 'Smart pantry management for reducing food waste',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Pantry Pal',
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#10b981' },
    { media: '(prefers-color-scheme: dark)', color: '#059669' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Pantry Pal" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#10b981" />
        <meta name="msapplication-tap-highlight" content="no" />
      </head>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
```

### 2. Providers Setup ( `src/components/providers.tsx` )

```typescript
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { AuthProvider } from '@/lib/auth/auth-context';
import { PWAProvider } from '@/lib/pwa/pwa-context';
import { ThemeProvider } from 'next-themes';
import { useState } from 'react';

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000, // 5 minutes
            gcTime: 10 * 60 * 1000, // 10 minutes
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors except 408, 429
              if (error?.status >= 400 && error?.status < 500) {
                return error?.status === 408 || error?.status === 429;
              }
              return failureCount < 3;
            },
          },
          mutations: {
            retry: (failureCount, error: any) => {
              // Don't retry mutations on 4xx errors
              if (error?.status >= 400 && error?.status < 500) {
                return false;
              }
              return failureCount < 2;
            },
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <AuthProvider>
          <PWAProvider>
            {children}
          </PWAProvider>
        </AuthProvider>
      </ThemeProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
```
