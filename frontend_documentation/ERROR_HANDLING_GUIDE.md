# Error Handling Guide for Next.js PWA

## Overview

This guide provides comprehensive error handling strategies specifically for Next.js PWA applications integrating with the Pantry Pal API. It covers server-side error handling, client-side error boundaries, offline error scenarios, and PWA-specific error patterns.

## Error Response Structure

### Standard Error Response

```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ErrorInfo;
  message?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

interface ErrorInfo {
  code: string;
  message: string;
  details?: Record<string, any>;
}
```

### Error Response Examples

#### Validation Error

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": {
      "fields": {
        "email": "Invalid email format",
        "password": "Password must be at least 8 characters"
      }
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

#### Authentication Error

```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

#### Business Rule Violation

```json
{
  "success": false,
  "error": {
    "code": "BUSINESS_RULE_VIOLATION",
    "message": "Cannot delete pantry with active inventory",
    "details": {
      "rule": "pantry_deletion_with_inventory",
      "inventory_count": 15
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

## Error Codes Reference

### Authentication & Authorization

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `UNAUTHORIZED` | 401 | Authentication required | Redirect to login |
| `FORBIDDEN` | 403 | Insufficient permissions | Show permission error |
| `TOKEN_EXPIRED` | 401 | Access token expired | Refresh token automatically |
| `INVALID_CREDENTIALS` | 401 | Invalid login credentials | Show login error |

### Validation Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `VALIDATION_FAILED` | 400 | Request validation failed | Show field errors |
| `INVALID_INPUT` | 400 | Invalid input format | Show input error |
| `MISSING_REQUIRED_FIELD` | 400 | Required field missing | Highlight missing fields |

### Resource Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `NOT_FOUND` | 404 | Resource not found | Show not found message |
| `CONFLICT` | 409 | Resource conflict | Show conflict resolution |
| `DUPLICATE_RESOURCE` | 409 | Resource already exists | Show duplicate error |

### Business Logic Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `BUSINESS_RULE_VIOLATION` | 422 | Business rule violated | Show business rule error |
| `INSUFFICIENT_INVENTORY` | 422 | Not enough inventory | Show quantity error |
| `PANTRY_ACCESS_DENIED` | 403 | No access to pantry | Show access denied |

### System Errors

| Code | HTTP Status | Description | User Action |
|------|-------------|-------------|-------------|
| `INTERNAL_SERVER_ERROR` | 500 | Server error | Show generic error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable | Show retry message |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests | Show rate limit message |

## Error Handling Implementation

### Error Handler Class

```typescript
interface ErrorHandlerConfig {
  showToasts: boolean;
  logErrors: boolean;
  retryAttempts: number;
  retryDelay: number;
}

class ErrorHandler {
  private config: ErrorHandlerConfig;
  private retryQueue: Map<string, number> = new Map();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      showToasts: true,
      logErrors: true,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config,
    };
  }

  handleError(error: any, context?: string): ErrorInfo | null {
    const errorInfo = this.extractErrorInfo(error);
    
    if (this.config.logErrors) {
      this.logError(error, errorInfo, context);
    }

    if (this.config.showToasts) {
      this.showErrorToast(errorInfo);
    }

    // Handle specific error types
    switch (errorInfo.code) {
      case 'UNAUTHORIZED':
      case 'TOKEN_EXPIRED':
        this.handleAuthError();
        break;
      case 'VALIDATION_FAILED':
        return this.handleValidationError(errorInfo);
      case 'RATE_LIMIT_EXCEEDED':
        this.handleRateLimitError(errorInfo);
        break;
      case 'SERVICE_UNAVAILABLE':
        this.handleServiceUnavailableError(errorInfo);
        break;
      default:
        this.handleGenericError(errorInfo);
    }

    return errorInfo;
  }

  private extractErrorInfo(error: any): ErrorInfo {
    // Network error
    if (!error.response) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed. Please check your internet connection.',
        details: { originalError: error.message }
      };
    }

    // API error response
    if (error.response?.data?.error) {
      return error.response.data.error;
    }

    // HTTP error without API error structure
    return {
      code: 'HTTP_ERROR',
      message: `HTTP ${error.response.status}: ${error.response.statusText}`,
      details: { status: error.response.status }
    };
  }

  private logError(originalError: any, errorInfo: ErrorInfo, context?: string): void {
    console.error('Error occurred:', {
      context,
      errorInfo,
      originalError,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });

    // Send to error tracking service (e.g., Sentry)
    if (window.Sentry) {
      window.Sentry.captureException(originalError, {
        tags: { context },
        extra: { errorInfo },
      });
    }
  }

  private showErrorToast(errorInfo: ErrorInfo): void {
    // Implementation depends on your toast library
    // Example with react-hot-toast:
    // toast.error(this.getUserFriendlyMessage(errorInfo));
  }

  private handleAuthError(): void {
    // Clear tokens and redirect to login
    TokenManager.clearTokens();
    window.location.href = '/login';
  }

  private handleValidationError(errorInfo: ErrorInfo): ErrorInfo {
    // Return validation errors for form handling
    return errorInfo;
  }

  private handleRateLimitError(errorInfo: ErrorInfo): void {
    const retryAfter = errorInfo.details?.retry_after || 60;
    this.showErrorToast({
      ...errorInfo,
      message: `Too many requests. Please wait ${retryAfter} seconds before trying again.`
    });
  }

  private handleServiceUnavailableError(errorInfo: ErrorInfo): void {
    this.showErrorToast({
      ...errorInfo,
      message: 'Service is temporarily unavailable. Please try again later.'
    });
  }

  private handleGenericError(errorInfo: ErrorInfo): void {
    this.showErrorToast({
      ...errorInfo,
      message: 'An unexpected error occurred. Please try again.'
    });
  }

  getUserFriendlyMessage(errorInfo: ErrorInfo): string {
    const friendlyMessages: Record<string, string> = {
      'NETWORK_ERROR': 'Please check your internet connection and try again.',
      'UNAUTHORIZED': 'Please log in to continue.',
      'FORBIDDEN': 'You don\'t have permission to perform this action.',
      'NOT_FOUND': 'The requested item could not be found.',
      'VALIDATION_FAILED': 'Please check your input and try again.',
      'BUSINESS_RULE_VIOLATION': 'This action is not allowed.',
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait a moment.',
      'INTERNAL_SERVER_ERROR': 'Something went wrong. Please try again later.',
    };

    return friendlyMessages[errorInfo.code] || errorInfo.message;
  }

  async retryOperation<T>(
    operation: () => Promise<T>,
    operationId: string,
    maxRetries?: number
  ): Promise<T> {
    const retries = maxRetries || this.config.retryAttempts;
    const currentRetry = this.retryQueue.get(operationId) || 0;

    try {
      const result = await operation();
      this.retryQueue.delete(operationId);
      return result;
    } catch (error) {
      if (currentRetry < retries && this.shouldRetry(error)) {
        this.retryQueue.set(operationId, currentRetry + 1);
        
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelay * Math.pow(2, currentRetry))
        );
        
        return this.retryOperation(operation, operationId, maxRetries);
      }

      this.retryQueue.delete(operationId);
      throw error;
    }
  }

  private shouldRetry(error: any): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'SERVICE_UNAVAILABLE',
      'INTERNAL_SERVER_ERROR'
    ];

    const errorInfo = this.extractErrorInfo(error);
    return retryableCodes.includes(errorInfo.code);
  }
}

export const errorHandler = new ErrorHandler();
```

### React Error Boundary

```typescript
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error boundary caught an error:', error, errorInfo);
    
    // Log to error tracking service
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        contexts: { react: errorInfo },
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button onClick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### API Client Error Integration

```typescript
import axios, { AxiosError } from 'axios';
import { errorHandler } from './ErrorHandler';

// Extend the API client with error handling
class APIClientWithErrorHandling extends APIClient {
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      return await super.get<T>(url, config);
    } catch (error) {
      const errorInfo = errorHandler.handleError(error, `GET ${url}`);
      throw new APIError(errorInfo);
    }
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      return await super.post<T>(url, data, config);
    } catch (error) {
      const errorInfo = errorHandler.handleError(error, `POST ${url}`);
      throw new APIError(errorInfo);
    }
  }

  // Similar for put, delete, etc.
}

class APIError extends Error {
  constructor(public errorInfo: ErrorInfo) {
    super(errorInfo.message);
    this.name = 'APIError';
  }
}

export const apiClient = new APIClientWithErrorHandling(process.env.NEXT_PUBLIC_API_URL!);

## Next.js PWA Error Handling Patterns

### App Router Error Boundaries

```typescript
// app/error.tsx - Global error boundary
'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error:', error);

    if (window.Sentry) {
      window.Sentry.captureException(error);
    }
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-bold text-gray-900">Something went wrong!</h2>
        <p className="text-gray-600">
          We're sorry, but something unexpected happened.
        </p>
        <div className="space-x-4">
          <Button onClick={reset}>Try again</Button>
          <Button variant="outline" onClick={() => window.location.href = '/'}>
            Go home
          </Button>
        </div>
      </div>
    </div>
  );
}

// app/pantries/error.tsx - Route-specific error boundary
'use client';

import { useEffect } from 'react';
import { usePWA } from '@/lib/pwa/pwa-context';

export default function PantriesError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const { isOnline } = usePWA();

  useEffect(() => {
    console.error('Pantries error:', error);
  }, [error]);

  if (!isOnline) {
    return (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          You're offline
        </h2>
        <p className="text-gray-600 mb-4">
          Please check your internet connection and try again.
        </p>
        <Button onClick={reset}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="text-center py-8">
      <h2 className="text-xl font-semibold text-gray-900 mb-2">
        Failed to load pantries
      </h2>
      <p className="text-gray-600 mb-4">
        There was an error loading your pantries.
      </p>
      <Button onClick={reset}>Try again</Button>
    </div>
  );
}
```

### Server Action Error Handling

```typescript
// app/actions/pantry-actions.ts
'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { createPantrySchema } from '@/lib/validations/pantry';
import { APIError } from '@/lib/errors/api-error';

export async function createPantryAction(
  prevState: any,
  formData: FormData
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return {
        success: false,
        error: 'You must be logged in to create a pantry'
      };
    }

    const validatedFields = createPantrySchema.safeParse({
      name: formData.get('name'),
      description: formData.get('description'),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
      };
    }

    const response = await fetch(`${process.env.API_URL}/pantries`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(validatedFields.data),
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle API errors
      if (data.error?.code === 'VALIDATION_FAILED') {
        return {
          success: false,
          errors: data.error.details?.fields || {},
        };
      }

      throw new APIError(data.error?.message || 'Failed to create pantry', response.status);
    }

    revalidatePath('/pantries');
    redirect(`/pantries/${data.data.id}`);
  } catch (error) {
    console.error('Create pantry error:', error);

    if (error instanceof APIError) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
    };
  }
}
```

### PWA-Specific Error Components

```typescript
// components/errors/offline-error.tsx
'use client';

import { usePWA } from '@/lib/pwa/pwa-context';
import { WifiSlashIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface OfflineErrorProps {
  onRetry?: () => void;
  message?: string;
}

export function OfflineError({ onRetry, message }: OfflineErrorProps) {
  const { isOnline } = usePWA();

  return (
    <div className="text-center py-8 px-4">
      <WifiSlashIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        You're offline
      </h3>
      <p className="text-gray-600 mb-4">
        {message || 'This content is not available offline. Please check your internet connection.'}
      </p>
      {onRetry && (
        <button
          onClick={onRetry}
          disabled={!isOnline}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ArrowPathIcon className="h-4 w-4 mr-2" />
          {isOnline ? 'Retry' : 'Waiting for connection...'}
        </button>
      )}
    </div>
  );
}

// components/errors/sync-error.tsx
'use client';

import { useState } from 'react';
import { ExclamationTriangleIcon, CloudArrowUpIcon } from '@heroicons/react/24/outline';
import { syncManager } from '@/lib/sync/sync-manager';

interface SyncErrorProps {
  pendingCount: number;
  onRetrySync?: () => void;
}

export function SyncError({ pendingCount, onRetrySync }: SyncErrorProps) {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetrySync = async () => {
    setIsRetrying(true);
    try {
      await syncManager.startSync();
      onRetrySync?.();
    } catch (error) {
      console.error('Sync retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <div className="flex items-start">
        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            Sync Issues Detected
          </h3>
          <p className="text-sm text-yellow-700 mt-1">
            {pendingCount} item(s) failed to sync. Your changes are saved locally.
          </p>
          <button
            onClick={handleRetrySync}
            disabled={isRetrying}
            className="mt-2 inline-flex items-center text-sm font-medium text-yellow-800 hover:text-yellow-900 disabled:opacity-50"
          >
            <CloudArrowUpIcon className="h-4 w-4 mr-1" />
            {isRetrying ? 'Retrying...' : 'Retry sync'}
          </button>
        </div>
      </div>
    </div>
  );
}
```

### React Query Error Handling

```typescript
// lib/hooks/use-error-handler.ts
import { useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { usePWA } from '@/lib/pwa/pwa-context';

export function useErrorHandler() {
  const { isOnline } = usePWA();

  const handleError = useCallback((error: any, context?: string) => {
    console.error(`Error in ${context}:`, error);

    // Don't show error toasts for offline scenarios
    if (!isOnline && error?.code === 'NETWORK_ERROR') {
      return;
    }

    // Handle specific error types
    if (error?.response?.status === 401) {
      toast.error('Your session has expired. Please log in again.');
      // Redirect to login
      window.location.href = '/auth/login';
      return;
    }

    if (error?.response?.status === 403) {
      toast.error('You don\'t have permission to perform this action.');
      return;
    }

    if (error?.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
      return;
    }

    // Default error message
    const message = error?.response?.data?.error?.message ||
                   error?.message ||
                   'An unexpected error occurred';

    toast.error(message);
  }, [isOnline]);

  return { handleError };
}

// lib/hooks/use-pantries.ts
import { useQuery } from '@tanstack/react-query';
import { useErrorHandler } from './use-error-handler';
import { pantryService } from '@/lib/api/pantries';

export function usePantries() {
  const { handleError } = useErrorHandler();

  return useQuery({
    queryKey: ['pantries'],
    queryFn: pantryService.getPantries,
    onError: (error) => handleError(error, 'usePantries'),
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
```

### Form Error Handling with Server Actions

```typescript
// components/forms/create-pantry-form.tsx
'use client';

import { useFormState } from 'react-dom';
import { useFormStatus } from 'react-dom';
import { createPantryAction } from '@/app/actions/pantry-actions';

function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <button
      type="submit"
      disabled={pending}
      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
    >
      {pending ? 'Creating...' : 'Create Pantry'}
    </button>
  );
}

export function CreatePantryForm() {
  const [state, formAction] = useFormState(createPantryAction, {
    success: false,
    error: null,
    errors: {},
  });

  return (
    <form action={formAction} className="space-y-4">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
          Pantry Name
        </label>
        <input
          type="text"
          name="name"
          id="name"
          required
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-emerald-500 focus:border-emerald-500"
        />
        {state.errors?.name && (
          <p className="mt-1 text-sm text-red-600">{state.errors.name[0]}</p>
        )}
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description (optional)
        </label>
        <textarea
          name="description"
          id="description"
          rows={3}
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-emerald-500 focus:border-emerald-500"
        />
        {state.errors?.description && (
          <p className="mt-1 text-sm text-red-600">{state.errors.description[0]}</p>
        )}
      </div>

      {state.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-600">{state.error}</p>
        </div>
      )}

      <SubmitButton />
    </form>
  );
}
```

## Form Validation Error Handling

### React Hook Form Integration

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export const LoginForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isSubmitting }
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      await authService.login(data);
    } catch (error) {
      if (error instanceof APIError && error.errorInfo.code === 'VALIDATION_FAILED') {
        // Set server validation errors on form fields
        const fieldErrors = error.errorInfo.details?.fields || {};
        Object.entries(fieldErrors).forEach(([field, message]) => {
          setError(field as keyof LoginFormData, {
            type: 'server',
            message: message as string,
          });
        });
      } else {
        // Handle other errors
        setError('root', {
          type: 'server',
          message: errorHandler.getUserFriendlyMessage(error.errorInfo),
        });
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <input
          {...register('email')}
          type="email"
          placeholder="Email"
        />
        {errors.email && <span className="error">{errors.email.message}</span>}
      </div>

      <div>
        <input
          {...register('password')}
          type="password"
          placeholder="Password"
        />
        {errors.password && <span className="error">{errors.password.message}</span>}
      </div>

      {errors.root && <div className="error">{errors.root.message}</div>}

      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
};
```

### Custom Hook for API Operations

```typescript
import { useState, useCallback } from 'react';

interface UseAPIOperationResult<T> {
  data: T | null;
  loading: boolean;
  error: ErrorInfo | null;
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
}

export function useAPIOperation<T>(
  operation: (...args: any[]) => Promise<T>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: ErrorInfo) => void;
    showErrorToast?: boolean;
  } = {}
): UseAPIOperationResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ErrorInfo | null>(null);

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await operation(...args);
      setData(result);
      options.onSuccess?.(result);
      return result;
    } catch (err) {
      const errorInfo = errorHandler.handleError(err, operation.name);
      setError(errorInfo);
      options.onError?.(errorInfo);
      return null;
    } finally {
      setLoading(false);
    }
  }, [operation, options]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return { data, loading, error, execute, reset };
}

// Usage example
export const PantryList: React.FC = () => {
  const {
    data: pantries,
    loading,
    error,
    execute: loadPantries
  } = useAPIOperation(pantryService.getPantries, {
    onError: (error) => {
      if (error.code !== 'UNAUTHORIZED') {
        toast.error('Failed to load pantries');
      }
    }
  });

  useEffect(() => {
    loadPantries();
  }, [loadPantries]);

  if (loading) return <div>Loading pantries...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {pantries?.map(pantry => (
        <div key={pantry.id}>{pantry.name}</div>
      ))}
    </div>
  );
};
```

## Error Recovery Strategies

### Retry with Exponential Backoff

```typescript
class RetryableOperation {
  static async execute<T>(
    operation: () => Promise<T>,
    options: {
      maxRetries?: number;
      baseDelay?: number;
      maxDelay?: number;
      shouldRetry?: (error: any) => boolean;
    } = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      shouldRetry = (error) => {
        const errorInfo = errorHandler.extractErrorInfo(error);
        return ['NETWORK_ERROR', 'SERVICE_UNAVAILABLE'].includes(errorInfo.code);
      }
    } = options;

    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries || !shouldRetry(error)) {
          throw error;
        }

        const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }
}
```

### Offline Support

```typescript
class OfflineManager {
  private static isOnline = navigator.onLine;
  private static pendingOperations: Array<{
    id: string;
    operation: () => Promise<any>;
    timestamp: number;
  }> = [];

  static init() {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  static isConnected(): boolean {
    return this.isOnline;
  }

  static async executeWhenOnline<T>(
    operation: () => Promise<T>,
    operationId: string
  ): Promise<T> {
    if (this.isOnline) {
      return operation();
    }

    // Queue operation for when online
    return new Promise((resolve, reject) => {
      this.pendingOperations.push({
        id: operationId,
        operation: async () => {
          try {
            const result = await operation();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        },
        timestamp: Date.now(),
      });
    });
  }

  private static async handleOnline() {
    this.isOnline = true;

    // Execute pending operations
    const operations = [...this.pendingOperations];
    this.pendingOperations = [];

    for (const { operation } of operations) {
      try {
        await operation();
      } catch (error) {
        console.error('Failed to execute pending operation:', error);
      }
    }
  }

  private static handleOffline() {
    this.isOnline = false;
  }
}
```

## Error Monitoring and Analytics

### Error Tracking Setup

```typescript
interface ErrorTrackingConfig {
  dsn: string;
  environment: string;
  userId?: string;
  userEmail?: string;
}

class ErrorTracking {
  static init(config: ErrorTrackingConfig) {
    // Initialize Sentry or similar service
    if (window.Sentry) {
      window.Sentry.init({
        dsn: config.dsn,
        environment: config.environment,
        beforeSend: (event) => {
          // Filter out sensitive information
          if (event.request?.headers?.Authorization) {
            delete event.request.headers.Authorization;
          }
          return event;
        },
      });

      if (config.userId) {
        window.Sentry.setUser({
          id: config.userId,
          email: config.userEmail,
        });
      }
    }
  }

  static captureError(error: Error, context?: Record<string, any>) {
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        extra: context,
      });
    }
  }

  static captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info') {
    if (window.Sentry) {
      window.Sentry.captureMessage(message, level);
    }
  }
}
```

## Testing Error Handling

### Unit Tests for Error Handler

```typescript
import { errorHandler } from './ErrorHandler';

describe('ErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle network errors', () => {
    const networkError = new Error('Network Error');
    networkError.code = 'NETWORK_ERROR';

    const result = errorHandler.handleError(networkError);

    expect(result.code).toBe('NETWORK_ERROR');
    expect(result.message).toContain('Network connection failed');
  });

  it('should handle API validation errors', () => {
    const apiError = {
      response: {
        data: {
          error: {
            code: 'VALIDATION_FAILED',
            message: 'Validation failed',
            details: {
              fields: {
                email: 'Invalid email format'
              }
            }
          }
        }
      }
    };

    const result = errorHandler.handleError(apiError);

    expect(result.code).toBe('VALIDATION_FAILED');
    expect(result.details.fields.email).toBe('Invalid email format');
  });

  it('should retry operations with exponential backoff', async () => {
    let attempts = 0;
    const operation = jest.fn().mockImplementation(() => {
      attempts++;
      if (attempts < 3) {
        throw new Error('Service unavailable');
      }
      return 'success';
    });

    const result = await errorHandler.retryOperation(operation, 'test-op');

    expect(result).toBe('success');
    expect(attempts).toBe(3);
  });
});
```

## Best Practices

### 1. Error Message Guidelines

* Use clear, user-friendly language
* Avoid technical jargon
* Provide actionable guidance when possible
* Be specific about what went wrong

### 2. Error Logging

* Log all errors with sufficient context
* Include user ID, request ID, and timestamp
* Don't log sensitive information (passwords, tokens)
* Use structured logging for better analysis

### 3. User Experience

* Show loading states during operations
* Provide clear feedback for errors
* Offer retry options for transient errors
* Gracefully degrade functionality when possible

### 4. Error Recovery

* Implement automatic retry for network errors
* Cache data for offline scenarios
* Provide manual refresh options
* Clear error states when appropriate

### 5. Testing

* Test error scenarios in unit tests
* Use error boundary testing
* Test network failure scenarios
* Validate error message display
```
