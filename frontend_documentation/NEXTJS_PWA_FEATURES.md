# Next.js PWA Features Implementation

## Overview

This guide covers the implementation of advanced PWA features for the Pantry Pal application, including offline synchronization, push notifications, background sync, and native-like interactions.

## Offline Synchronization

### 1. Sync Manager (`src/lib/sync/sync-manager.ts`)

```typescript
import { offlineDB } from '@/lib/db/offline-db';
import { apiClient } from '@/lib/api/client';
import { toast } from 'react-hot-toast';

interface SyncItem {
  id: string;
  operation: 'create' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: number;
  retries: number;
}

class SyncManager {
  private isRunning = false;
  private maxRetries = 3;
  private retryDelay = 1000; // 1 second

  async startSync(): Promise<void> {
    if (this.isRunning || !navigator.onLine) {
      return;
    }

    this.isRunning = true;
    console.log('Starting sync...');

    try {
      const syncQueue = await offlineDB.getSyncQueue();
      
      if (syncQueue.length === 0) {
        console.log('No items to sync');
        return;
      }

      toast.loading(`Syncing ${syncQueue.length} items...`, { id: 'sync' });

      for (const item of syncQueue) {
        await this.syncItem(item);
      }

      toast.success('Sync completed', { id: 'sync' });
    } catch (error) {
      console.error('Sync failed:', error);
      toast.error('Sync failed', { id: 'sync' });
    } finally {
      this.isRunning = false;
    }
  }

  private async syncItem(item: SyncItem): Promise<void> {
    try {
      switch (item.table) {
        case 'pantries':
          await this.syncPantry(item);
          break;
        case 'inventory':
          await this.syncInventoryItem(item);
          break;
        case 'recipes':
          await this.syncRecipe(item);
          break;
        default:
          console.warn(`Unknown sync table: ${item.table}`);
      }

      // Remove from sync queue on success
      await offlineDB.removeSyncItem(item.id);
    } catch (error) {
      console.error(`Failed to sync item ${item.id}:`, error);
      
      // Increment retry count
      item.retries += 1;
      
      if (item.retries >= this.maxRetries) {
        console.error(`Max retries reached for item ${item.id}, removing from queue`);
        await offlineDB.removeSyncItem(item.id);
      } else {
        // Update retry count in queue
        await offlineDB.addToSyncQueue(item.operation, item.table, {
          ...item.data,
          _syncId: item.id,
          _retries: item.retries,
        });
      }
    }
  }

  private async syncPantry(item: SyncItem): Promise<void> {
    switch (item.operation) {
      case 'create':
        await apiClient.post('/pantries', item.data);
        break;
      case 'update':
        await apiClient.put(`/pantries/${item.data.id}`, item.data);
        break;
      case 'delete':
        await apiClient.delete(`/pantries/${item.data.id}`);
        break;
    }
  }

  private async syncInventoryItem(item: SyncItem): Promise<void> {
    switch (item.operation) {
      case 'create':
        await apiClient.post(`/pantries/${item.data.pantry_id}/inventory`, item.data);
        break;
      case 'update':
        await apiClient.put(`/pantries/${item.data.pantry_id}/inventory/${item.data.id}`, item.data);
        break;
      case 'delete':
        await apiClient.delete(`/pantries/${item.data.pantry_id}/inventory/${item.data.id}`);
        break;
    }
  }

  private async syncRecipe(item: SyncItem): Promise<void> {
    switch (item.operation) {
      case 'create':
        await apiClient.post('/recipes', item.data);
        break;
      case 'update':
        await apiClient.put(`/recipes/${item.data.id}`, item.data);
        break;
      case 'delete':
        await apiClient.delete(`/recipes/${item.data.id}`);
        break;
    }
  }

  // Auto-sync when coming online
  setupAutoSync(): void {
    window.addEventListener('online', () => {
      setTimeout(() => this.startSync(), 1000);
    });

    // Periodic sync when online
    setInterval(() => {
      if (navigator.onLine && !this.isRunning) {
        this.startSync();
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }
}

export const syncManager = new SyncManager();
```

### 2. Background Sync Hook (`src/lib/hooks/use-background-sync.ts`)

```typescript
import { useEffect } from 'react';
import { syncManager } from '@/lib/sync/sync-manager';
import { usePWA } from '@/lib/pwa/pwa-context';

export function useBackgroundSync() {
  const { isOnline } = usePWA();

  useEffect(() => {
    // Setup auto-sync
    syncManager.setupAutoSync();

    // Register background sync if supported
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      navigator.serviceWorker.ready.then((registration) => {
        return registration.sync.register('background-sync');
      }).catch((error) => {
        console.error('Background sync registration failed:', error);
      });
    }
  }, []);

  useEffect(() => {
    if (isOnline) {
      // Trigger sync when coming online
      syncManager.startSync();
    }
  }, [isOnline]);

  return {
    triggerSync: () => syncManager.startSync(),
  };
}
```

## Push Notifications

### 1. Notification Manager (`src/lib/notifications/notification-manager.ts`)

```typescript
interface NotificationPermission {
  granted: boolean;
  denied: boolean;
  default: boolean;
}

class NotificationManager {
  private vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;

  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  async subscribeToPush(): Promise<PushSubscription | null> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.warn('Push messaging is not supported');
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      
      // Check if already subscribed
      let subscription = await registration.pushManager.getSubscription();
      
      if (!subscription) {
        // Subscribe to push notifications
        subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey!),
        });
      }

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  async unsubscribeFromPush(): Promise<boolean> {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();
      
      if (subscription) {
        await subscription.unsubscribe();
        await this.removeSubscriptionFromServer(subscription);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  async showLocalNotification(title: string, options: NotificationOptions = {}): Promise<void> {
    if (!await this.requestPermission()) {
      return;
    }

    const registration = await navigator.serviceWorker.ready;
    await registration.showNotification(title, {
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [200, 100, 200],
      ...options,
    });
  }

  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    const response = await fetch('/api/notifications/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscription: subscription.toJSON(),
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send subscription to server');
    }
  }

  private async removeSubscriptionFromServer(subscription: PushSubscription): Promise<void> {
    const response = await fetch('/api/notifications/unsubscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscription: subscription.toJSON(),
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to remove subscription from server');
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }
}

export const notificationManager = new NotificationManager();
```

### 2. Notification Hook (`src/lib/hooks/use-notifications.ts`)

```typescript
import { useState, useEffect } from 'react';
import { notificationManager } from '@/lib/notifications/notification-manager';

export function useNotifications() {
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }

    // Check if already subscribed
    checkSubscription();
  }, []);

  const checkSubscription = async () => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready;
        const subscription = await registration.pushManager.getSubscription();
        setIsSubscribed(!!subscription);
      } catch (error) {
        console.error('Error checking subscription:', error);
      }
    }
  };

  const requestPermission = async () => {
    setIsLoading(true);
    try {
      const granted = await notificationManager.requestPermission();
      setPermission(granted ? 'granted' : 'denied');
      return granted;
    } finally {
      setIsLoading(false);
    }
  };

  const subscribe = async () => {
    setIsLoading(true);
    try {
      const subscription = await notificationManager.subscribeToPush();
      setIsSubscribed(!!subscription);
      return !!subscription;
    } finally {
      setIsLoading(false);
    }
  };

  const unsubscribe = async () => {
    setIsLoading(true);
    try {
      const success = await notificationManager.unsubscribeFromPush();
      if (success) {
        setIsSubscribed(false);
      }
      return success;
    } finally {
      setIsLoading(false);
    }
  };

  const showNotification = async (title: string, options?: NotificationOptions) => {
    await notificationManager.showLocalNotification(title, options);
  };

  return {
    permission,
    isSubscribed,
    isLoading,
    requestPermission,
    subscribe,
    unsubscribe,
    showNotification,
  };
}
```

## App Shortcuts & Share Target

### 1. App Shortcuts Component (`src/components/pwa/app-shortcuts.tsx`)

```typescript
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export function AppShortcuts() {
  const router = useRouter();

  useEffect(() => {
    // Handle app shortcuts
    const handleShortcut = (event: Event) => {
      const customEvent = event as CustomEvent;
      const shortcut = customEvent.detail?.shortcut;

      switch (shortcut) {
        case 'add-item':
          router.push('/inventory/add');
          break;
        case 'shopping-list':
          router.push('/shopping-list');
          break;
        case 'recipes':
          router.push('/recipes');
          break;
        default:
          break;
      }
    };

    window.addEventListener('appshortcut', handleShortcut);

    return () => {
      window.removeEventListener('appshortcut', handleShortcut);
    };
  }, [router]);

  return null;
}
```

### 2. Share Target Handler (`src/lib/pwa/share-target.ts`)

```typescript
export function handleShareTarget() {
  // Check if the app was launched via share target
  const urlParams = new URLSearchParams(window.location.search);
  const sharedTitle = urlParams.get('title');
  const sharedText = urlParams.get('text');
  const sharedUrl = urlParams.get('url');

  if (sharedTitle || sharedText || sharedUrl) {
    // Handle shared content
    const sharedContent = {
      title: sharedTitle,
      text: sharedText,
      url: sharedUrl,
    };

    // Store in session storage for processing
    sessionStorage.setItem('shared-content', JSON.stringify(sharedContent));

    // Redirect to appropriate page
    if (sharedUrl && sharedUrl.includes('recipe')) {
      window.location.href = '/recipes/import';
    } else {
      window.location.href = '/dashboard';
    }
  }
}

export function getSharedContent() {
  const stored = sessionStorage.getItem('shared-content');
  if (stored) {
    sessionStorage.removeItem('shared-content');
    return JSON.parse(stored);
  }
  return null;
}
```
