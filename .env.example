# Application Configuration
APP_APP_ENVIRONMENT=development

# Database Configuration
APP_DATABASE_PASSWORD=your_database_password_here

# Authentication Configuration
APP_AUTH_JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Redis Configuration (optional, only if using Redis)
APP_REDIS_PASSWORD=your_redis_password_here

# Server Configuration (optional overrides)
# APP_SERVER_HOST=0.0.0.0
# APP_SERVER_PORT=8080

# Logger Configuration (optional overrides)
# APP_LOGGER_LEVEL=info
# APP_LOGGER_FORMAT=console
