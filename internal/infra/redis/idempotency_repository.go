package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// IdempotencyRepository implements Redis-based idempotency storage
type IdempotencyRepository struct {
	client redis.UniversalClient
	prefix string
	ctx    context.Context
}

// NewIdempotencyRepository creates a new Redis idempotency repository
func NewIdempotencyRepository(client redis.UniversalClient, prefix string) *IdempotencyRepository {
	if prefix == "" {
		prefix = "idempotency"
	}

	return &IdempotencyRepository{
		client: client,
		prefix: prefix,
		ctx:    context.Background(),
	}
}

// Store stores an idempotency key in Redis
func (r *IdempotencyRepository) Store(key *domain.IdempotencyKey) error {
	data, err := json.Marshal(key)
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to marshal idempotency key: "+err.Error())
	}

	redisKey := r.getRedisKey(key.Key)
	ttl := time.Until(key.ExpiresAt)

	err = r.client.Set(r.ctx, redisKey, data, ttl).Err()
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to store idempotency key: "+err.Error())
	}

	return nil
}

// Get retrieves an idempotency key from Redis
func (r *IdempotencyRepository) Get(key string) (*domain.IdempotencyKey, error) {
	redisKey := r.getRedisKey(key)

	data, err := r.client.Get(r.ctx, redisKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, errors.New(errors.ErrCodeNotFound, "Idempotency key not found")
		}
		return nil, errors.New(errors.ErrCodeInternalError, "Failed to get idempotency key: "+err.Error())
	}

	var idempotencyKey domain.IdempotencyKey
	err = json.Unmarshal([]byte(data), &idempotencyKey)
	if err != nil {
		return nil, errors.New(errors.ErrCodeInternalError, "Failed to unmarshal idempotency key: "+err.Error())
	}

	// Check if expired
	if idempotencyKey.IsExpired() {
		// Delete expired key
		r.client.Del(r.ctx, redisKey)
		return nil, errors.New(errors.ErrCodeNotFound, "Idempotency key expired")
	}

	return &idempotencyKey, nil
}

// Update updates an idempotency key in Redis
func (r *IdempotencyRepository) Update(key *domain.IdempotencyKey) error {
	data, err := json.Marshal(key)
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to marshal idempotency key: "+err.Error())
	}

	redisKey := r.getRedisKey(key.Key)
	ttl := time.Until(key.ExpiresAt)

	// Only update if key exists
	exists, err := r.client.Exists(r.ctx, redisKey).Result()
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to check key existence: "+err.Error())
	}

	if exists == 0 {
		return errors.New(errors.ErrCodeNotFound, "Idempotency key not found for update")
	}

	err = r.client.Set(r.ctx, redisKey, data, ttl).Err()
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to update idempotency key: "+err.Error())
	}

	return nil
}

// Delete removes an idempotency key from Redis
func (r *IdempotencyRepository) Delete(key string) error {
	redisKey := r.getRedisKey(key)

	err := r.client.Del(r.ctx, redisKey).Err()
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to delete idempotency key: "+err.Error())
	}

	return nil
}

// SetProcessing atomically sets a key to processing status if it doesn't exist
func (r *IdempotencyRepository) SetProcessing(key string, requestHash string, method string, path string, userID *uuid.UUID, ttl time.Duration) (*domain.IdempotencyKey, bool, error) {
	redisKey := r.getRedisKey(key)

	// Create new idempotency key
	idempotencyKey := domain.NewIdempotencyKey(key, requestHash, method, path, userID, ttl)
	idempotencyKey.MarkProcessingStarted()

	data, err := json.Marshal(idempotencyKey)
	if err != nil {
		return nil, false, errors.New(errors.ErrCodeInternalError, "Failed to marshal idempotency key: "+err.Error())
	}

	// Use SET with NX (only if not exists) and EX (expiration)
	result, err := r.client.SetNX(r.ctx, redisKey, data, ttl).Result()
	if err != nil {
		return nil, false, errors.New(errors.ErrCodeInternalError, "Failed to set processing key: "+err.Error())
	}

	if !result {
		// Key already exists, get the existing one
		existingKey, err := r.Get(key)
		if err != nil {
			return nil, false, err
		}
		return existingKey, false, nil
	}

	return idempotencyKey, true, nil
}

// GetExpiredKeys returns keys that have expired
func (r *IdempotencyRepository) GetExpiredKeys(limit int) ([]string, error) {
	// Redis automatically expires keys, so we don't need to manually track expired keys
	// This method is kept for interface compatibility but returns empty slice
	return []string{}, nil
}

// DeleteBatch deletes multiple keys in a batch
func (r *IdempotencyRepository) DeleteBatch(keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	redisKeys := make([]string, len(keys))
	for i, key := range keys {
		redisKeys[i] = r.getRedisKey(key)
	}

	err := r.client.Del(r.ctx, redisKeys...).Err()
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to delete batch keys: "+err.Error())
	}

	return nil
}

// getRedisKey creates a Redis key with prefix
func (r *IdempotencyRepository) getRedisKey(key string) string {
	return fmt.Sprintf("%s:%s", r.prefix, key)
}

// GetStats returns statistics about idempotency keys
func (r *IdempotencyRepository) GetStats() (map[string]interface{}, error) {
	pattern := r.getRedisKey("*")

	// Get all keys matching the pattern
	keys, err := r.client.Keys(r.ctx, pattern).Result()
	if err != nil {
		return nil, errors.New(errors.ErrCodeInternalError, "Failed to get keys: "+err.Error())
	}

	stats := map[string]interface{}{
		"total_keys": len(keys),
		"prefix":     r.prefix,
	}

	// Count by status (this would be expensive for large datasets)
	if len(keys) < 1000 { // Only do detailed stats for small datasets
		statusCounts := make(map[string]int)

		for _, redisKey := range keys {
			data, err := r.client.Get(r.ctx, redisKey).Result()
			if err != nil {
				continue
			}

			var idempotencyKey domain.IdempotencyKey
			err = json.Unmarshal([]byte(data), &idempotencyKey)
			if err != nil {
				continue
			}

			statusCounts[string(idempotencyKey.Status)]++
		}

		stats["status_counts"] = statusCounts
	}

	return stats, nil
}

// Cleanup removes all idempotency keys (for testing/maintenance)
func (r *IdempotencyRepository) Cleanup() error {
	pattern := r.getRedisKey("*")

	keys, err := r.client.Keys(r.ctx, pattern).Result()
	if err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to get keys for cleanup: "+err.Error())
	}

	if len(keys) > 0 {
		err = r.client.Del(r.ctx, keys...).Err()
		if err != nil {
			return errors.New(errors.ErrCodeInternalError, "Failed to cleanup keys: "+err.Error())
		}
	}

	return nil
}
