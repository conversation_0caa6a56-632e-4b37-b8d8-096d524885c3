package handler

import (
	"time"

	"github.com/gofiber/fiber/v2"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// APIResponse represents the standard API response structure
type APIResponse struct {
	Success   bool                   `json:"success"`
	Data      interface{}            `json:"data,omitempty"`
	Error     *ErrorInfo             `json:"error,omitempty"`
	Message   string                 `json:"message,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
}

// ErrorInfo represents error information in API responses
type ErrorInfo struct {
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	APIResponse
	Pagination *PaginationMeta `json:"pagination,omitempty"`
}

// SuccessResponse creates a successful API response
func SuccessResponse(c *fiber.Ctx, data interface{}, message string) error {
	response := APIResponse{
		Success:   true,
		Data:      data,
		Message:   message,
		Timestamp: time.Now(),
		RequestID: getRequestID(c),
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// SuccessResponseWithMetadata creates a successful API response with metadata
func SuccessResponseWithMetadata(c *fiber.Ctx, data interface{}, message string, metadata map[string]interface{}) error {
	response := APIResponse{
		Success:   true,
		Data:      data,
		Message:   message,
		Metadata:  metadata,
		Timestamp: time.Now(),
		RequestID: getRequestID(c),
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// CreatedResponse creates a successful creation response
func CreatedResponse(c *fiber.Ctx, data interface{}, message string) error {
	response := APIResponse{
		Success:   true,
		Data:      data,
		Message:   message,
		Timestamp: time.Now(),
		RequestID: getRequestID(c),
	}

	return c.Status(fiber.StatusCreated).JSON(response)
}

// NoContentResponse creates a successful no content response
func NoContentResponse(c *fiber.Ctx) error {
	return c.SendStatus(fiber.StatusNoContent)
}

// BadRequest creates a bad request error response
func BadRequest(c *fiber.Ctx, message string, details map[string]interface{}) error {
	errorInfo := &ErrorInfo{
		Code:    string(errors.ErrCodeInvalidInput),
		Message: message,
		Details: details,
	}

	response := APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
		RequestID: getRequestID(c),
	}

	return c.Status(fiber.StatusBadRequest).JSON(response)
}

// Conflict creates a conflict error response
func Conflict(c *fiber.Ctx, message string, details map[string]interface{}) error {
	errorInfo := &ErrorInfo{
		Code:    string(errors.ErrCodeConflict),
		Message: message,
		Details: details,
	}

	response := APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
		RequestID: getRequestID(c),
	}

	return c.Status(fiber.StatusConflict).JSON(response)
}

// ErrorResponse creates an error API response
func ErrorResponse(c *fiber.Ctx, err error) error {
	var errorInfo *ErrorInfo
	var statusCode int

	if appErr := errors.GetAppError(err); appErr != nil {
		errorInfo = &ErrorInfo{
			Code:    string(appErr.Code),
			Message: appErr.Message,
			Details: appErr.Details,
		}
		statusCode = appErr.HTTPStatus
	} else {
		// Handle non-AppError cases
		errorInfo = &ErrorInfo{
			Code:    string(errors.ErrCodeInternalError),
			Message: "An unexpected error occurred",
		}
		statusCode = fiber.StatusInternalServerError
	}

	response := APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
		RequestID: getRequestID(c),
	}

	return c.Status(statusCode).JSON(response)
}

// ValidationErrorResponse creates a validation error response
func ValidationErrorResponse(c *fiber.Ctx, validationErr *errors.ValidationError) error {
	errorInfo := &ErrorInfo{
		Code:    string(validationErr.Code),
		Message: validationErr.Message,
		Details: map[string]interface{}{
			"fields": validationErr.Fields,
		},
	}

	response := APIResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now(),
		RequestID: getRequestID(c),
	}

	return c.Status(validationErr.HTTPStatus).JSON(response)
}

// PaginatedSuccessResponse creates a successful paginated response
func PaginatedSuccessResponse(c *fiber.Ctx, data interface{}, pagination *PaginationMeta, message string) error {
	response := PaginatedResponse{
		APIResponse: APIResponse{
			Success:   true,
			Data:      data,
			Message:   message,
			Timestamp: time.Now(),
			RequestID: getRequestID(c),
		},
		Pagination: pagination,
	}

	return c.Status(fiber.StatusOK).JSON(response)
}

// CalculatePagination calculates pagination metadata
func CalculatePagination(page, limit int, total int64) *PaginationMeta {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	if totalPages < 1 {
		totalPages = 1
	}

	return &PaginationMeta{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// getRequestID extracts request ID from context
func getRequestID(c *fiber.Ctx) string {
	if requestID := c.Locals("request_id"); requestID != nil {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// ParsePaginationParams extracts pagination parameters from query string
func ParsePaginationParams(c *fiber.Ctx) (page, limit int) {
	page = c.QueryInt("page", 1)
	limit = c.QueryInt("limit", 10)

	// Enforce reasonable limits
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	return page, limit
}

// ParseSortParams extracts sorting parameters from query string
func ParseSortParams(c *fiber.Ctx, defaultSort string) (sortBy, sortOrder string) {
	sortBy = c.Query("sort_by", defaultSort)
	sortOrder = c.Query("sort_order", "asc")

	// Validate sort order
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "asc"
	}

	return sortBy, sortOrder
}

// FilterParams represents common filter parameters
type FilterParams struct {
	Search   string            `json:"search,omitempty"`
	Status   string            `json:"status,omitempty"`
	Category string            `json:"category,omitempty"`
	DateFrom *time.Time        `json:"date_from,omitempty"`
	DateTo   *time.Time        `json:"date_to,omitempty"`
	Custom   map[string]string `json:"custom,omitempty"`
}

// ParseFilterParams extracts filter parameters from query string
func ParseFilterParams(c *fiber.Ctx) *FilterParams {
	filters := &FilterParams{
		Search:   c.Query("search"),
		Status:   c.Query("status"),
		Category: c.Query("category"),
		Custom:   make(map[string]string),
	}

	// Parse date filters
	if dateFrom := c.Query("date_from"); dateFrom != "" {
		if parsed, err := time.Parse("2006-01-02", dateFrom); err == nil {
			filters.DateFrom = &parsed
		}
	}

	if dateTo := c.Query("date_to"); dateTo != "" {
		if parsed, err := time.Parse("2006-01-02", dateTo); err == nil {
			filters.DateTo = &parsed
		}
	}

	return filters
}
