package handler

import (
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// PantryHandler handles pantry-related HTTP requests
type PantryHandler struct {
	pantryRepo     domain.PantryRepository
	membershipRepo domain.PantryMembershipRepository
	locationRepo   domain.PantryLocationRepository
	userRepo       domain.UserRepository
	authzService   domain.PantryAuthorizationService
	logger         *logger.Logger
	validator      *validator.Validate
}

// NewPantryHandler creates a new pantry handler
func NewPantryHandler(
	pantryRepo domain.PantryRepository,
	membershipRepo domain.PantryMembershipRepository,
	locationRepo domain.PantryLocationRepository,
	userRepo domain.UserRepository,
	authzService domain.PantryAuthorizationService,
	log *logger.Logger,
) *PantryHandler {
	return &PantryHandler{
		pantryRepo:     pantryRepo,
		membershipRepo: membershipRepo,
		locationRepo:   locationRepo,
		userRepo:       userRepo,
		authzService:   authzService,
		logger:         log,
		validator:      validator.New(),
	}
}

// CreatePantry creates a new pantry
//
//	@Summary		Create a new pantry
//	@Description	Create a new pantry for the authenticated user
//	@Tags			Pantries
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			request	body		domain.CreatePantryRequest	true	"Pantry creation data"
//	@Success		201		{object}	APIResponse					"Pantry created successfully"
//	@Failure		400		{object}	APIResponse					"Invalid input or validation error"
//	@Failure		401		{object}	APIResponse					"Unauthorized"
//	@Failure		409		{object}	APIResponse					"Pantry name already exists"
//	@Failure		500		{object}	APIResponse					"Internal server error"
//	@Router			/pantries [post]
func (h *PantryHandler) CreatePantry(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.CreatePantryRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Check if pantry name already exists for this user
	exists, err := h.pantryRepo.ExistsByName(userID, req.Name)
	if err != nil {
		h.logger.LogError(err, "Failed to check pantry name existence", map[string]interface{}{
			"user_id": userIDStr,
			"name":    req.Name,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "You already have a pantry with this name"))
	}

	// Create pantry
	pantry := domain.NewPantry(req.Name, req.Description, userID)
	if err := h.pantryRepo.Create(pantry); err != nil {
		h.logger.LogError(err, "Failed to create pantry", map[string]interface{}{
			"user_id": userIDStr,
			"name":    req.Name,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.created", pantry.ID.String(), map[string]interface{}{
		"owner_user_id": userIDStr,
		"name":          pantry.Name,
	})

	// Convert to response
	response := h.pantryToResponse(pantry, userID)

	return CreatedResponse(c, response, "Pantry created successfully")
}

// GetPantries retrieves user's pantries
//
//	@Summary		Get user's pantries
//	@Description	Retrieve all pantries accessible to the authenticated user with pagination
//	@Tags			Pantries
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			page		query		int		false	"Page number (default: 1)"
//	@Param			limit		query		int		false	"Items per page (default: 10, max: 100)"
//	@Param			owner_only	query		bool	false	"Only return owned pantries (default: false)"
//	@Success		200			{object}	APIResponse	"Pantries retrieved successfully"
//	@Failure		400			{object}	APIResponse	"Invalid query parameters"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/pantries [get]
func (h *PantryHandler) GetPantries(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	// Parse pagination and filters
	page, limit := ParsePaginationParams(c)
	ownerOnly := c.QueryBool("owner_only", false)

	var pantries []*domain.Pantry
	var total int64

	if ownerOnly {
		pantries, total, err = h.pantryRepo.GetOwnedPantries(userID, page, limit)
	} else {
		pantries, total, err = h.pantryRepo.GetUserPantries(userID, page, limit)
	}

	if err != nil {
		h.logger.LogError(err, "Failed to get user pantries", map[string]interface{}{
			"user_id":    userIDStr,
			"owner_only": ownerOnly,
		})
		return ErrorResponse(c, err)
	}

	// Convert to responses
	responses := make([]domain.PantryResponse, len(pantries))
	for i, pantry := range pantries {
		responses[i] = h.pantryToResponse(pantry, userID)
	}

	// Calculate pagination
	pagination := CalculatePagination(page, limit, total)

	return PaginatedSuccessResponse(c, responses, pagination, "Pantries retrieved successfully")
}

// GetPantry retrieves a specific pantry
//
//	@Summary		Get pantry by ID
//	@Description	Retrieve a specific pantry by its ID
//	@Tags			Pantries
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string		true	"Pantry ID"
//	@Success		200			{object}	APIResponse	"Pantry retrieved successfully"
//	@Failure		400			{object}	APIResponse	"Invalid pantry ID"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - no access to this pantry"
//	@Failure		404			{object}	APIResponse	"Pantry not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/pantries/{pantryId} [get]
func (h *PantryHandler) GetPantry(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Check permission
	canView, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionViewPantry)
	if err != nil {
		h.logger.LogError(err, "Failed to check view permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canView {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Get pantry
	pantry, err := h.pantryRepo.GetByID(pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Convert to response
	response := h.pantryToResponse(pantry, userID)

	return SuccessResponse(c, response, "Pantry retrieved successfully")
}

// UpdatePantry updates a pantry
//
//	@Summary		Update pantry
//	@Description	Update pantry name and description
//	@Tags			Pantries
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string						true	"Pantry ID"
//	@Param			request		body		domain.UpdatePantryRequest	true	"Pantry update data"
//	@Success		200			{object}	APIResponse					"Pantry updated successfully"
//	@Failure		400			{object}	APIResponse					"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse					"Unauthorized"
//	@Failure		403			{object}	APIResponse					"Forbidden - no edit permission"
//	@Failure		404			{object}	APIResponse					"Pantry not found"
//	@Failure		409			{object}	APIResponse					"Pantry name already exists"
//	@Failure		500			{object}	APIResponse					"Internal server error"
//	@Router			/pantries/{pantryId} [put]
func (h *PantryHandler) UpdatePantry(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Check permission
	canEdit, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionEditPantry)
	if err != nil {
		h.logger.LogError(err, "Failed to check edit permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canEdit {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	var req domain.UpdatePantryRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get current pantry
	pantry, err := h.pantryRepo.GetByID(pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry for update", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Check if new name conflicts with existing pantries (if name changed)
	if pantry.Name != req.Name {
		exists, err := h.pantryRepo.ExistsByName(pantry.OwnerUserID, req.Name)
		if err != nil {
			h.logger.LogError(err, "Failed to check pantry name existence", map[string]interface{}{
				"user_id":   userIDStr,
				"pantry_id": pantryIDStr,
				"name":      req.Name,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}

		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "A pantry with this name already exists"))
		}
	}

	// Update pantry
	pantry.UpdateDetails(req.Name, req.Description)
	if err := h.pantryRepo.Update(pantry); err != nil {
		h.logger.LogError(err, "Failed to update pantry", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.updated", pantry.ID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"name":    pantry.Name,
	})

	// Convert to response
	response := h.pantryToResponse(pantry, userID)

	return SuccessResponse(c, response, "Pantry updated successfully")
}

// DeletePantry deletes a pantry
//
//	@Summary		Delete pantry
//	@Description	Delete a pantry and all its associated data
//	@Tags			Pantries
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string		true	"Pantry ID"
//	@Success		200			{object}	APIResponse	"Pantry deleted successfully"
//	@Failure		400			{object}	APIResponse	"Invalid pantry ID"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - no delete permission"
//	@Failure		404			{object}	APIResponse	"Pantry not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/pantries/{pantryId} [delete]
func (h *PantryHandler) DeletePantry(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Check permission
	canDelete, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionDeletePantry)
	if err != nil {
		h.logger.LogError(err, "Failed to check delete permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canDelete {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Get pantry to log details
	pantry, err := h.pantryRepo.GetByID(pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry for deletion", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Delete pantry
	if err := h.pantryRepo.Delete(pantryID); err != nil {
		h.logger.LogError(err, "Failed to delete pantry", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.deleted", pantryIDStr, map[string]interface{}{
		"user_id": userIDStr,
		"name":    pantry.Name,
	})

	return SuccessResponse(c, nil, "Pantry deleted successfully")
}

// TransferOwnership transfers pantry ownership
func (h *PantryHandler) TransferOwnership(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Check permission
	canTransfer, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionTransferOwnership)
	if err != nil {
		h.logger.LogError(err, "Failed to check transfer ownership permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canTransfer {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	var req domain.TransferOwnershipRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Verify new owner exists
	_, err = h.userRepo.GetByID(req.NewOwnerUserID)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "New owner user not found"))
		}
		h.logger.LogError(err, "Failed to get new owner user", map[string]interface{}{
			"new_owner_id": req.NewOwnerUserID.String(),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	// Check if new owner is a member of the pantry
	isMember, err := h.authzService.IsMember(req.NewOwnerUserID, pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to check new owner membership", map[string]interface{}{
			"new_owner_id": req.NewOwnerUserID.String(),
			"pantry_id":    pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !isMember {
		return ErrorResponse(c, errors.New(errors.ErrCodeConflict, "New owner must be a member of the pantry"))
	}

	// Get current pantry
	pantry, err := h.pantryRepo.GetByID(pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry for ownership transfer", map[string]interface{}{
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Transfer ownership
	pantry.TransferOwnership(req.NewOwnerUserID)
	if err := h.pantryRepo.Update(pantry); err != nil {
		h.logger.LogError(err, "Failed to transfer pantry ownership", map[string]interface{}{
			"pantry_id": pantryIDStr,
			"old_owner": userIDStr,
			"new_owner": req.NewOwnerUserID.String(),
		})
		return ErrorResponse(c, err)
	}

	// Remove new owner's membership (they're now the owner)
	if membership, err := h.membershipRepo.GetByPantryAndUser(pantryID, req.NewOwnerUserID); err == nil {
		membership.Remove("promoted_to_owner")
		h.membershipRepo.Update(membership)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.ownership_transferred", pantryIDStr, map[string]interface{}{
		"old_owner": userIDStr,
		"new_owner": req.NewOwnerUserID.String(),
	})

	// Convert to response
	response := h.pantryToResponse(pantry, userID)

	return SuccessResponse(c, response, "Ownership transferred successfully")
}

// Helper methods

func (h *PantryHandler) pantryToResponse(pantry *domain.Pantry, currentUserID uuid.UUID) domain.PantryResponse {
	response := domain.PantryResponse{
		ID:          pantry.ID,
		Name:        pantry.Name,
		Description: pantry.Description,
		OwnerUserID: pantry.OwnerUserID,
		CreatedAt:   pantry.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   pantry.UpdatedAt.Format(time.RFC3339),
		IsOwner:     pantry.OwnerUserID == currentUserID,
	}

	// Get user's role if not owner
	if !response.IsOwner {
		if role, err := h.authzService.GetUserRole(currentUserID, pantry.ID); err == nil && role != nil {
			roleStr := string(*role)
			response.UserRole = &roleStr
		}
	}

	return response
}

func (h *PantryHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "oneof":
				validationErrors[field] = "Invalid value"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}

	return validationErrors
}
