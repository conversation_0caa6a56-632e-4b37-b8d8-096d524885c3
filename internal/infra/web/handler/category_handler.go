package handler

import (
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// CategoryHandler handles category-related HTTP requests
type CategoryHandler struct {
	categoryRepo domain.CategoryRepository
	logger       *logger.Logger
	validator    *validator.Validate
}

// NewCategoryHandler creates a new category handler
func NewCategoryHandler(
	categoryRepo domain.CategoryRepository,
	log *logger.Logger,
) *CategoryHandler {
	return &CategoryHandler{
		categoryRepo: categoryRepo,
		logger:       log,
		validator:    validator.New(),
	}
}

// CreateCategory creates a new category
func (h *CategoryHandler) CreateCategory(c *fiber.Ctx) error {
	var req domain.CreateCategoryRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Validate category name
	if err := domain.ValidateCategoryName(req.Name); err != nil {
		return ErrorResponse(c, err)
	}
	
	// Check if category name already exists at the same level
	exists, err := h.categoryRepo.ExistsByName(req.Name, req.ParentCategoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to check category name existence", map[string]interface{}{
			"name":               req.Name,
			"parent_category_id": req.ParentCategoryID,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Category with this name already exists at this level"))
	}
	
	// Validate parent category exists if specified
	if req.ParentCategoryID != nil {
		_, err := h.categoryRepo.GetByID(*req.ParentCategoryID)
		if err != nil {
			if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
				return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "Parent category not found"))
			}
			h.logger.LogError(err, "Failed to validate parent category", map[string]interface{}{
				"parent_category_id": req.ParentCategoryID,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
	}
	
	// Create category
	category := domain.NewCategory(req.Name, req.Description, req.ParentCategoryID)
	if err := h.categoryRepo.Create(category); err != nil {
		h.logger.LogError(err, "Failed to create category", map[string]interface{}{
			"name":               req.Name,
			"parent_category_id": req.ParentCategoryID,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("category.created", category.ID.String(), map[string]interface{}{
		"name":               category.Name,
		"parent_category_id": category.ParentCategoryID,
	})
	
	// Convert to response
	response := h.categoryToResponse(category)
	
	return CreatedResponse(c, response, "Category created successfully")
}

// GetCategories retrieves all categories
func (h *CategoryHandler) GetCategories(c *fiber.Ctx) error {
	// Check if requesting top-level categories only
	topLevelOnly := c.QueryBool("top_level_only", false)
	
	var categories []*domain.Category
	var err error
	
	if topLevelOnly {
		categories, err = h.categoryRepo.GetTopLevelCategories()
	} else {
		categories, err = h.categoryRepo.GetAllCategories()
	}
	
	if err != nil {
		h.logger.LogError(err, "Failed to get categories", map[string]interface{}{
			"top_level_only": topLevelOnly,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.CategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = h.categoryToResponse(category)
	}
	
	return SuccessResponse(c, responses, "Categories retrieved successfully")
}

// GetCategory retrieves a specific category
func (h *CategoryHandler) GetCategory(c *fiber.Ctx) error {
	categoryIDStr := c.Params("categoryId")
	categoryID, err := uuid.Parse(categoryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid category ID"))
	}
	
	// Get category
	category, err := h.categoryRepo.GetByID(categoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get category", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to response
	response := h.categoryToResponse(category)
	
	return SuccessResponse(c, response, "Category retrieved successfully")
}

// GetSubCategories retrieves subcategories of a category
func (h *CategoryHandler) GetSubCategories(c *fiber.Ctx) error {
	categoryIDStr := c.Params("categoryId")
	categoryID, err := uuid.Parse(categoryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid category ID"))
	}
	
	// Get subcategories
	subcategories, err := h.categoryRepo.GetSubCategories(categoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get subcategories", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.CategoryResponse, len(subcategories))
	for i, category := range subcategories {
		responses[i] = h.categoryToResponse(category)
	}
	
	return SuccessResponse(c, responses, "Subcategories retrieved successfully")
}

// UpdateCategory updates a category
func (h *CategoryHandler) UpdateCategory(c *fiber.Ctx) error {
	categoryIDStr := c.Params("categoryId")
	categoryID, err := uuid.Parse(categoryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid category ID"))
	}
	
	var req domain.UpdateCategoryRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Validate category name
	if err := domain.ValidateCategoryName(req.Name); err != nil {
		return ErrorResponse(c, err)
	}
	
	// Get current category
	category, err := h.categoryRepo.GetByID(categoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get category for update", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Check if new name conflicts with existing categories (if name changed)
	if category.Name != req.Name {
		exists, err := h.categoryRepo.ExistsByName(req.Name, category.ParentCategoryID)
		if err != nil {
			h.logger.LogError(err, "Failed to check category name existence", map[string]interface{}{
				"category_id":        categoryIDStr,
				"name":               req.Name,
				"parent_category_id": category.ParentCategoryID,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Category with this name already exists at this level"))
		}
	}
	
	// Update category
	category.UpdateDetails(req.Name, req.Description)
	if err := h.categoryRepo.Update(category); err != nil {
		h.logger.LogError(err, "Failed to update category", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("category.updated", category.ID.String(), map[string]interface{}{
		"name": category.Name,
	})
	
	// Convert to response
	response := h.categoryToResponse(category)
	
	return SuccessResponse(c, response, "Category updated successfully")
}

// MoveCategory moves a category to a different parent
func (h *CategoryHandler) MoveCategory(c *fiber.Ctx) error {
	categoryIDStr := c.Params("categoryId")
	categoryID, err := uuid.Parse(categoryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid category ID"))
	}
	
	var req domain.MoveCategoryRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	// Get current category
	category, err := h.categoryRepo.GetByID(categoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get category for move", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Validate hierarchy (prevent cycles)
	if err := domain.ValidateCategoryHierarchy(categoryID, req.ParentCategoryID, h.categoryRepo); err != nil {
		return ErrorResponse(c, err)
	}
	
	// Validate new parent exists if specified
	if req.ParentCategoryID != nil {
		_, err := h.categoryRepo.GetByID(*req.ParentCategoryID)
		if err != nil {
			if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
				return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "New parent category not found"))
			}
			h.logger.LogError(err, "Failed to validate new parent category", map[string]interface{}{
				"parent_category_id": req.ParentCategoryID,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
	}
	
	// Check if name conflicts at new location
	exists, err := h.categoryRepo.ExistsByName(category.Name, req.ParentCategoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to check category name existence at new location", map[string]interface{}{
			"category_id":        categoryIDStr,
			"name":               category.Name,
			"parent_category_id": req.ParentCategoryID,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Category with this name already exists at the target location"))
	}
	
	// Move category
	category.Move(req.ParentCategoryID)
	if err := h.categoryRepo.Update(category); err != nil {
		h.logger.LogError(err, "Failed to move category", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("category.moved", category.ID.String(), map[string]interface{}{
		"new_parent_id": req.ParentCategoryID,
	})
	
	// Convert to response
	response := h.categoryToResponse(category)
	
	return SuccessResponse(c, response, "Category moved successfully")
}

// DeleteCategory deletes a category
func (h *CategoryHandler) DeleteCategory(c *fiber.Ctx) error {
	categoryIDStr := c.Params("categoryId")
	categoryID, err := uuid.Parse(categoryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid category ID"))
	}
	
	// Get category to log details
	category, err := h.categoryRepo.GetByID(categoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get category for deletion", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Check if category has subcategories
	subcategories, err := h.categoryRepo.GetSubCategories(categoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to check subcategories", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if len(subcategories) > 0 {
		return ErrorResponse(c, errors.New(errors.ErrCodeConflict, "Cannot delete category with subcategories"))
	}
	
	// Delete category
	if err := h.categoryRepo.Delete(categoryID); err != nil {
		h.logger.LogError(err, "Failed to delete category", map[string]interface{}{
			"category_id": categoryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("category.deleted", categoryIDStr, map[string]interface{}{
		"name": category.Name,
	})
	
	return SuccessResponse(c, nil, "Category deleted successfully")
}

// Helper methods

func (h *CategoryHandler) categoryToResponse(category *domain.Category) domain.CategoryResponse {
	return domain.CategoryResponse{
		ID:               category.ID,
		Name:             category.Name,
		Description:      category.Description,
		ParentCategoryID: category.ParentCategoryID,
		CreatedAt:        category.CreatedAt.Format(time.RFC3339),
		UpdatedAt:        category.UpdatedAt.Format(time.RFC3339),
	}
}

func (h *CategoryHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)
	
	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}
	
	return validationErrors
}
