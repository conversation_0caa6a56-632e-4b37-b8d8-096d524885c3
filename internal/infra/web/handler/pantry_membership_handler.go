package handler

import (
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// PantryMembershipHandler handles pantry membership-related HTTP requests
type PantryMembershipHandler struct {
	pantryRepo     domain.PantryRepository
	membershipRepo domain.PantryMembershipRepository
	userRepo       domain.UserRepository
	authzService   domain.PantryAuthorizationService
	logger         *logger.Logger
	validator      *validator.Validate
}

// NewPantryMembershipHandler creates a new pantry membership handler
func NewPantryMembershipHandler(
	pantryRepo domain.PantryRepository,
	membershipRepo domain.PantryMembershipRepository,
	userRepo domain.UserRepository,
	authzService domain.PantryAuthorizationService,
	log *logger.Logger,
) *PantryMembershipHandler {
	return &PantryMembershipHandler{
		pantryRepo:     pantryRepo,
		membershipRepo: membershipRepo,
		userRepo:       userRepo,
		authzService:   authzService,
		logger:         log,
		validator:      validator.New(),
	}
}

// InviteMember invites a user to join a pantry
func (h *PantryMembershipHandler) InviteMember(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	var req domain.InviteMemberRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Check if user can invite with this role
	canInvite, err := h.authzService.CanInviteWithRole(userID, pantryID, req.Role)
	if err != nil {
		h.logger.LogError(err, "Failed to check invite permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
			"role":      string(req.Role),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canInvite {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Find user by email
	inviteeUser, err := h.userRepo.GetByEmail(req.Email)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "User with this email not found"))
		}
		h.logger.LogError(err, "Failed to get user by email", map[string]interface{}{
			"email": req.Email,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	// Check if user is already a member
	exists, err := h.membershipRepo.ExistsActiveMembership(pantryID, inviteeUser.ID)
	if err != nil {
		h.logger.LogError(err, "Failed to check existing membership", map[string]interface{}{
			"pantry_id":  pantryIDStr,
			"invitee_id": inviteeUser.ID.String(),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "User is already a member of this pantry"))
	}

	// Check if user is the owner
	isOwner, err := h.authzService.IsOwner(inviteeUser.ID, pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to check ownership", map[string]interface{}{
			"pantry_id":  pantryIDStr,
			"invitee_id": inviteeUser.ID.String(),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if isOwner {
		return ErrorResponse(c, errors.New(errors.ErrCodeConflict, "Cannot invite the pantry owner"))
	}

	// Create membership invitation
	membership := domain.NewPantryMembership(pantryID, inviteeUser.ID, req.Role, &userID)
	if err := h.membershipRepo.Create(membership); err != nil {
		h.logger.LogError(err, "Failed to create membership invitation", map[string]interface{}{
			"pantry_id":  pantryIDStr,
			"invitee_id": inviteeUser.ID.String(),
			"role":       string(req.Role),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.member_invited", pantryIDStr, map[string]interface{}{
		"inviter_id": userIDStr,
		"invitee_id": inviteeUser.ID.String(),
		"role":       string(req.Role),
	})

	// Convert to response
	response := h.membershipToResponse(membership, nil)

	return CreatedResponse(c, response, "Member invited successfully")
}

// GetMembers retrieves pantry members
func (h *PantryMembershipHandler) GetMembers(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Check permission
	canView, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionViewMembers)
	if err != nil {
		h.logger.LogError(err, "Failed to check view members permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canView {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Parse pagination
	page, limit := ParsePaginationParams(c)

	// Get members
	memberships, total, err := h.membershipRepo.GetPantryMembers(pantryID, page, limit)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry members", map[string]interface{}{
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Convert to responses
	responses := make([]domain.PantryMemberResponse, len(memberships))
	for i, membership := range memberships {
		responses[i] = h.membershipToResponse(membership, nil)
	}

	// Calculate pagination
	pagination := CalculatePagination(page, limit, total)

	return PaginatedSuccessResponse(c, responses, pagination, "Members retrieved successfully")
}

// AcceptInvitation accepts a pantry invitation
func (h *PantryMembershipHandler) AcceptInvitation(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.AcceptInvitationRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get membership
	membership, err := h.membershipRepo.GetByID(req.MembershipID)
	if err != nil {
		h.logger.LogError(err, "Failed to get membership", map[string]interface{}{
			"membership_id": req.MembershipID.String(),
		})
		return ErrorResponse(c, err)
	}

	// Verify user owns this invitation
	if membership.UserID != userID {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Accept invitation
	if err := membership.AcceptInvitation(); err != nil {
		return ErrorResponse(c, err)
	}

	// Update membership
	if err := h.membershipRepo.Update(membership); err != nil {
		h.logger.LogError(err, "Failed to update membership", map[string]interface{}{
			"membership_id": req.MembershipID.String(),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.member_joined", membership.PantryID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"role":    string(membership.Role),
	})

	// Convert to response
	response := h.membershipToResponse(membership, nil)

	return SuccessResponse(c, response, "Invitation accepted successfully")
}

// RejectInvitation rejects a pantry invitation
func (h *PantryMembershipHandler) RejectInvitation(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.RejectInvitationRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get membership
	membership, err := h.membershipRepo.GetByID(req.MembershipID)
	if err != nil {
		h.logger.LogError(err, "Failed to get membership", map[string]interface{}{
			"membership_id": req.MembershipID.String(),
		})
		return ErrorResponse(c, err)
	}

	// Verify user owns this invitation
	if membership.UserID != userID {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Reject invitation
	if err := membership.RejectInvitation(); err != nil {
		return ErrorResponse(c, err)
	}

	// Update membership
	if err := h.membershipRepo.Update(membership); err != nil {
		h.logger.LogError(err, "Failed to update membership", map[string]interface{}{
			"membership_id": req.MembershipID.String(),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.invitation_rejected", membership.PantryID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"role":    string(membership.Role),
	})

	return SuccessResponse(c, nil, "Invitation rejected successfully")
}

// GetPendingInvitations retrieves user's pending pantry invitations
func (h *PantryMembershipHandler) GetPendingInvitations(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	// Get pending invitations
	memberships, err := h.membershipRepo.GetPendingInvitations(userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pending invitations", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Convert to responses with pantry details
	responses := make([]domain.PantryInvitationResponse, len(memberships))
	for i, membership := range memberships {
		responses[i] = h.membershipToInvitationResponse(membership)
	}

	return SuccessResponse(c, responses, "Pending invitations retrieved successfully")
}

// UpdateMemberRole updates a member's role
func (h *PantryMembershipHandler) UpdateMemberRole(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	memberIDStr := c.Params("memberId")
	memberID, err := uuid.Parse(memberIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid member ID"))
	}

	var req domain.UpdateMemberRoleRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get membership
	membership, err := h.membershipRepo.GetByID(memberID)
	if err != nil {
		h.logger.LogError(err, "Failed to get membership", map[string]interface{}{
			"membership_id": memberIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Verify membership belongs to the pantry
	if membership.PantryID != pantryID {
		return ErrorResponse(c, errors.ErrNotFound)
	}

	// Check if user can update this member's role
	canUpdate, err := h.authzService.CanUpdateMemberRole(userID, pantryID, membership.UserID, req.Role)
	if err != nil {
		h.logger.LogError(err, "Failed to check update member role permission", map[string]interface{}{
			"user_id":     userIDStr,
			"pantry_id":   pantryIDStr,
			"target_user": membership.UserID.String(),
			"new_role":    string(req.Role),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canUpdate {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Update role
	membership.UpdateRole(req.Role)
	if err := h.membershipRepo.Update(membership); err != nil {
		h.logger.LogError(err, "Failed to update member role", map[string]interface{}{
			"membership_id": memberIDStr,
			"new_role":      string(req.Role),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.member_role_updated", pantryIDStr, map[string]interface{}{
		"user_id":     userIDStr,
		"target_user": membership.UserID.String(),
		"new_role":    string(req.Role),
	})

	// Convert to response
	response := h.membershipToResponse(membership, nil)

	return SuccessResponse(c, response, "Member role updated successfully")
}

// RemoveMember removes a member from a pantry
func (h *PantryMembershipHandler) RemoveMember(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	memberIDStr := c.Params("memberId")
	memberID, err := uuid.Parse(memberIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid member ID"))
	}

	// Get membership
	membership, err := h.membershipRepo.GetByID(memberID)
	if err != nil {
		h.logger.LogError(err, "Failed to get membership", map[string]interface{}{
			"membership_id": memberIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Verify membership belongs to the pantry
	if membership.PantryID != pantryID {
		return ErrorResponse(c, errors.ErrNotFound)
	}

	// Check if user can remove this member
	canRemove, err := h.authzService.CanRemoveMember(userID, pantryID, membership.UserID)
	if err != nil {
		h.logger.LogError(err, "Failed to check remove member permission", map[string]interface{}{
			"user_id":     userIDStr,
			"pantry_id":   pantryIDStr,
			"target_user": membership.UserID.String(),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if !canRemove {
		return ErrorResponse(c, errors.ErrForbidden)
	}

	// Remove member
	membership.Remove("removed_by_admin")
	if err := h.membershipRepo.Update(membership); err != nil {
		h.logger.LogError(err, "Failed to remove member", map[string]interface{}{
			"membership_id": memberIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.member_removed", pantryIDStr, map[string]interface{}{
		"user_id":     userIDStr,
		"target_user": membership.UserID.String(),
		"reason":      "removed_by_admin",
	})

	return SuccessResponse(c, nil, "Member removed successfully")
}

// LeavePantry allows a user to leave a pantry
func (h *PantryMembershipHandler) LeavePantry(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Check if user is the owner
	isOwner, err := h.authzService.IsOwner(userID, pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to check ownership", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	if isOwner {
		return ErrorResponse(c, errors.New(errors.ErrCodeConflict, "Owner cannot leave pantry. Transfer ownership first."))
	}

	// Get membership
	membership, err := h.membershipRepo.GetByPantryAndUser(pantryID, userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get membership", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Leave pantry
	membership.Remove("left_voluntarily")
	if err := h.membershipRepo.Update(membership); err != nil {
		h.logger.LogError(err, "Failed to leave pantry", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("pantry.member_left", pantryIDStr, map[string]interface{}{
		"user_id": userIDStr,
	})

	return SuccessResponse(c, nil, "Left pantry successfully")
}

// Helper methods

func (h *PantryMembershipHandler) membershipToResponse(membership *domain.PantryMembership, user *domain.User) domain.PantryMemberResponse {
	response := domain.PantryMemberResponse{
		ID:              membership.ID,
		PantryID:        membership.PantryID,
		UserID:          membership.UserID,
		Role:            membership.Role,
		Status:          membership.Status,
		JoinedAt:        membership.JoinedAt.Format(time.RFC3339),
		InvitedByUserID: membership.InvitedByUserID,
	}

	if user != nil {
		response.UserEmail = &user.Email
		response.UserFirstName = user.FirstName
		response.UserLastName = user.LastName
	}

	return response
}

func (h *PantryMembershipHandler) membershipToInvitationResponse(membership *domain.PantryMembership) domain.PantryInvitationResponse {
	response := domain.PantryInvitationResponse{
		ID:              membership.ID,
		PantryID:        membership.PantryID,
		Role:            membership.Role,
		Status:          membership.Status,
		JoinedAt:        membership.JoinedAt.Format(time.RFC3339),
		InvitedByUserID: membership.InvitedByUserID,
	}

	// Get pantry details
	if pantry, err := h.pantryRepo.GetByID(membership.PantryID); err == nil {
		response.PantryName = pantry.Name
		response.PantryDescription = pantry.Description
		response.PantryOwnerUserID = pantry.OwnerUserID
	}

	// Get inviter details
	if membership.InvitedByUserID != nil {
		if inviter, err := h.userRepo.GetByID(*membership.InvitedByUserID); err == nil {
			response.InviterEmail = &inviter.Email
			response.InviterFirstName = inviter.FirstName
			response.InviterLastName = inviter.LastName
		}
	}

	return response
}

func (h *PantryMembershipHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "email":
				validationErrors[field] = "Invalid email format"
			case "oneof":
				validationErrors[field] = "Invalid value"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}

	return validationErrors
}
