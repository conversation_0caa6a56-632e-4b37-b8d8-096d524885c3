package handler

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// RecipeHandler handles recipe-related HTTP requests
type RecipeHandler struct {
	recipeUsecase *usecases.RecipeUsecase
	logger        *logger.Logger
	validator     *validator.Validate
}

// NewRecipeHandler creates a new recipe handler
func NewRecipeHandler(recipeUsecase *usecases.RecipeUsecase, log *logger.Logger) *RecipeHandler {
	return &RecipeHandler{
		recipeUsecase: recipeUsecase,
		logger:        log,
		validator:     validator.New(),
	}
}

// CreateRecipe creates a new recipe
//
//	@Summary		Create a new recipe
//	@Description	Create a new recipe with ingredients, instructions, and optional media
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			request	body		domain.CreateRecipeRequest	true	"Recipe creation data"
//	@Success		201		{object}	APIResponse					"Recipe created successfully"
//	@Failure		400		{object}	APIResponse					"Invalid input or validation error"
//	@Failure		401		{object}	APIResponse					"Unauthorized"
//	@Failure		500		{object}	APIResponse					"Internal server error"
//	@Router			/recipes [post]
func (h *RecipeHandler) CreateRecipe(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.CreateRecipeRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	recipe, err := h.recipeUsecase.CreateRecipe(c.Context(), userID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to create recipe", map[string]interface{}{
			"user_id": userIDStr,
			"title":   req.Title,
		})
		return ErrorResponse(c, err)
	}

	response := h.convertRecipeToResponse(recipe)
	return CreatedResponse(c, response, "Recipe created successfully")
}

// GetRecipe retrieves a recipe by ID
//
//	@Summary		Get recipe by ID
//	@Description	Retrieve a specific recipe by its ID
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			recipeId	path		string		true	"Recipe ID"
//	@Success		200			{object}	APIResponse	"Recipe retrieved successfully"
//	@Failure		400			{object}	APIResponse	"Invalid recipe ID"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - no access to this recipe"
//	@Failure		404			{object}	APIResponse	"Recipe not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/recipes/{recipeId} [get]
func (h *RecipeHandler) GetRecipe(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	recipeIDStr := c.Params("recipeId")
	recipeID, err := uuid.Parse(recipeIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid recipe ID"))
	}

	recipe, err := h.recipeUsecase.GetRecipe(c.Context(), userID, recipeID)
	if err != nil {
		h.logger.LogError(err, "Failed to get recipe", map[string]interface{}{
			"user_id":   userIDStr,
			"recipe_id": recipeIDStr,
		})
		return ErrorResponse(c, err)
	}

	response := h.convertRecipeToResponse(recipe)
	return SuccessResponse(c, response, "Recipe retrieved successfully")
}

// GetUserRecipes retrieves recipes for the authenticated user
//
//	@Summary		Get user's recipes
//	@Description	Retrieve all recipes created by the authenticated user with pagination and filtering
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			page			query		int		false	"Page number (default: 1)"
//	@Param			limit			query		int		false	"Items per page (default: 10, max: 100)"
//	@Param			search			query		string	false	"Search in recipe title and description"
//	@Param			cuisine			query		string	false	"Filter by cuisine"
//	@Param			category		query		string	false	"Filter by category"
//	@Param			difficulty		query		string	false	"Filter by difficulty (easy, medium, hard, expert)"
//	@Param			max_prep_time	query		int		false	"Maximum preparation time in minutes"
//	@Param			max_cook_time	query		int		false	"Maximum cooking time in minutes"
//	@Param			max_calories	query		int		false	"Maximum calories per serving"
//	@Param			is_favorite		query		bool	false	"Filter favorite recipes only"
//	@Param			sort_by			query		string	false	"Sort by field (created_at, updated_at, title, rating, cook_count)"
//	@Param			sort_order		query		string	false	"Sort order (asc, desc)"
//	@Success		200				{object}	APIResponse	"Recipes retrieved successfully"
//	@Failure		400				{object}	APIResponse	"Invalid query parameters"
//	@Failure		401				{object}	APIResponse	"Unauthorized"
//	@Failure		500				{object}	APIResponse	"Internal server error"
//	@Router			/recipes [get]
func (h *RecipeHandler) GetUserRecipes(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	// Parse query parameters
	params := h.parseRecipeQueryParams(c)

	recipes, total, err := h.recipeUsecase.GetUserRecipes(c.Context(), userID, params)
	if err != nil {
		h.logger.LogError(err, "Failed to get user recipes", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Convert to responses
	responses := make([]interface{}, len(recipes))
	for i, recipe := range recipes {
		responses[i] = h.convertRecipeToResponse(recipe)
	}

	// Calculate pagination
	pagination := CalculatePagination(params.Page, params.Limit, total)

	return PaginatedSuccessResponse(c, responses, pagination, "Recipes retrieved successfully")
}

// GetPublicRecipes retrieves public recipes
//
//	@Summary		Get public recipes
//	@Description	Retrieve all public recipes with pagination and filtering
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Param			page			query		int		false	"Page number (default: 1)"
//	@Param			limit			query		int		false	"Items per page (default: 10, max: 100)"
//	@Param			search			query		string	false	"Search in recipe title and description"
//	@Param			cuisine			query		string	false	"Filter by cuisine"
//	@Param			category		query		string	false	"Filter by category"
//	@Param			difficulty		query		string	false	"Filter by difficulty (easy, medium, hard, expert)"
//	@Param			max_prep_time	query		int		false	"Maximum preparation time in minutes"
//	@Param			max_cook_time	query		int		false	"Maximum cooking time in minutes"
//	@Param			max_calories	query		int		false	"Maximum calories per serving"
//	@Param			sort_by			query		string	false	"Sort by field (created_at, updated_at, title, rating, cook_count)"
//	@Param			sort_order		query		string	false	"Sort order (asc, desc)"
//	@Success		200				{object}	APIResponse	"Public recipes retrieved successfully"
//	@Failure		400				{object}	APIResponse	"Invalid query parameters"
//	@Failure		500				{object}	APIResponse	"Internal server error"
//	@Router			/recipes/public [get]
func (h *RecipeHandler) GetPublicRecipes(c *fiber.Ctx) error {
	// Parse query parameters
	params := h.parseRecipeQueryParams(c)

	recipes, total, err := h.recipeUsecase.GetPublicRecipes(c.Context(), params)
	if err != nil {
		h.logger.LogError(err, "Failed to get public recipes", map[string]interface{}{})
		return ErrorResponse(c, err)
	}

	// Convert to responses
	responses := make([]interface{}, len(recipes))
	for i, recipe := range recipes {
		responses[i] = h.convertRecipeToResponse(recipe)
	}

	// Calculate pagination
	pagination := CalculatePagination(params.Page, params.Limit, total)

	return PaginatedSuccessResponse(c, responses, pagination, "Public recipes retrieved successfully")
}

// UpdateRecipe updates a recipe
//
//	@Summary		Update recipe
//	@Description	Update an existing recipe
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			recipeId	path		string						true	"Recipe ID"
//	@Param			request		body		domain.UpdateRecipeRequest	true	"Recipe update data"
//	@Success		200			{object}	APIResponse					"Recipe updated successfully"
//	@Failure		400			{object}	APIResponse					"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse					"Unauthorized"
//	@Failure		403			{object}	APIResponse					"Forbidden - not recipe owner"
//	@Failure		404			{object}	APIResponse					"Recipe not found"
//	@Failure		500			{object}	APIResponse					"Internal server error"
//	@Router			/recipes/{recipeId} [put]
func (h *RecipeHandler) UpdateRecipe(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	recipeIDStr := c.Params("recipeId")
	recipeID, err := uuid.Parse(recipeIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid recipe ID"))
	}

	var req domain.UpdateRecipeRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	recipe, err := h.recipeUsecase.UpdateRecipe(c.Context(), userID, recipeID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to update recipe", map[string]interface{}{
			"user_id":   userIDStr,
			"recipe_id": recipeIDStr,
		})
		return ErrorResponse(c, err)
	}

	response := h.convertRecipeToResponse(recipe)
	return SuccessResponse(c, response, "Recipe updated successfully")
}

// DeleteRecipe deletes a recipe
//
//	@Summary		Delete recipe
//	@Description	Delete a recipe and all its associated data
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			recipeId	path		string		true	"Recipe ID"
//	@Success		200			{object}	APIResponse	"Recipe deleted successfully"
//	@Failure		400			{object}	APIResponse	"Invalid recipe ID"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - not recipe owner"
//	@Failure		404			{object}	APIResponse	"Recipe not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/recipes/{recipeId} [delete]
func (h *RecipeHandler) DeleteRecipe(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	recipeIDStr := c.Params("recipeId")
	recipeID, err := uuid.Parse(recipeIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid recipe ID"))
	}

	err = h.recipeUsecase.DeleteRecipe(c.Context(), userID, recipeID)
	if err != nil {
		h.logger.LogError(err, "Failed to delete recipe", map[string]interface{}{
			"user_id":   userIDStr,
			"recipe_id": recipeIDStr,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, nil, "Recipe deleted successfully")
}

// ScaleRecipe scales a recipe to different servings
//
//	@Summary		Scale recipe
//	@Description	Scale a recipe to a different number of servings
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			recipeId	path		string					true	"Recipe ID"
//	@Param			request		body		domain.ScaleRecipeRequest	true	"Scaling data"
//	@Success		200			{object}	APIResponse				"Recipe scaled successfully"
//	@Failure		400			{object}	APIResponse				"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse				"Unauthorized"
//	@Failure		403			{object}	APIResponse				"Forbidden - no access to recipe"
//	@Failure		404			{object}	APIResponse				"Recipe not found"
//	@Failure		500			{object}	APIResponse				"Internal server error"
//	@Router			/recipes/{recipeId}/scale [post]
func (h *RecipeHandler) ScaleRecipe(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	recipeIDStr := c.Params("recipeId")
	recipeID, err := uuid.Parse(recipeIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid recipe ID"))
	}

	var req domain.ScaleRecipeRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	recipe, err := h.recipeUsecase.ScaleRecipe(c.Context(), userID, recipeID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to scale recipe", map[string]interface{}{
			"user_id":   userIDStr,
			"recipe_id": recipeIDStr,
			"servings":  req.Servings,
		})
		return ErrorResponse(c, err)
	}

	response := h.convertRecipeToResponse(recipe)
	return SuccessResponse(c, response, "Recipe scaled successfully")
}

// MarkAsCooked marks a recipe as cooked
//
//	@Summary		Mark recipe as cooked
//	@Description	Mark a recipe as cooked to track cooking frequency
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			recipeId	path		string		true	"Recipe ID"
//	@Success		200			{object}	APIResponse	"Recipe marked as cooked successfully"
//	@Failure		400			{object}	APIResponse	"Invalid recipe ID"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - no access to recipe"
//	@Failure		404			{object}	APIResponse	"Recipe not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/recipes/{recipeId}/cook [post]
func (h *RecipeHandler) MarkAsCooked(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	recipeIDStr := c.Params("recipeId")
	recipeID, err := uuid.Parse(recipeIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid recipe ID"))
	}

	err = h.recipeUsecase.MarkAsCooked(c.Context(), userID, recipeID)
	if err != nil {
		h.logger.LogError(err, "Failed to mark recipe as cooked", map[string]interface{}{
			"user_id":   userIDStr,
			"recipe_id": recipeIDStr,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, nil, "Recipe marked as cooked successfully")
}

// CheckInventoryAvailability checks ingredient availability in pantry
//
//	@Summary		Check ingredient availability
//	@Description	Check if recipe ingredients are available in pantry inventory
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			recipeId	path		string							true	"Recipe ID"
//	@Param			request		body		domain.InventoryCheckRequest	true	"Inventory check data"
//	@Success		200			{object}	APIResponse						"Inventory availability checked successfully"
//	@Failure		400			{object}	APIResponse						"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse						"Unauthorized"
//	@Failure		403			{object}	APIResponse						"Forbidden - no access to recipe or pantry"
//	@Failure		404			{object}	APIResponse						"Recipe not found"
//	@Failure		500			{object}	APIResponse						"Internal server error"
//	@Router			/recipes/{recipeId}/check-inventory [post]
func (h *RecipeHandler) CheckInventoryAvailability(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	recipeIDStr := c.Params("recipeId")
	recipeID, err := uuid.Parse(recipeIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid recipe ID"))
	}

	var req domain.InventoryCheckRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	response, err := h.recipeUsecase.CheckInventoryAvailability(c.Context(), userID, recipeID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to check inventory availability", map[string]interface{}{
			"user_id":   userIDStr,
			"recipe_id": recipeIDStr,
			"pantry_id": req.PantryID,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, response, "Inventory availability checked successfully")
}

// SearchRecipes searches for recipes
//
//	@Summary		Search recipes
//	@Description	Search for recipes by title, description, ingredients, or tags
//	@Tags			Recipes
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			q				query		string	true	"Search query"
//	@Param			page			query		int		false	"Page number (default: 1)"
//	@Param			limit			query		int		false	"Items per page (default: 10, max: 100)"
//	@Param			cuisine			query		string	false	"Filter by cuisine"
//	@Param			category		query		string	false	"Filter by category"
//	@Param			difficulty		query		string	false	"Filter by difficulty (easy, medium, hard, expert)"
//	@Param			max_prep_time	query		int		false	"Maximum preparation time in minutes"
//	@Param			max_cook_time	query		int		false	"Maximum cooking time in minutes"
//	@Param			max_calories	query		int		false	"Maximum calories per serving"
//	@Param			sort_by			query		string	false	"Sort by field (created_at, updated_at, title, rating, cook_count)"
//	@Param			sort_order		query		string	false	"Sort order (asc, desc)"
//	@Success		200				{object}	APIResponse	"Recipes found successfully"
//	@Failure		400				{object}	APIResponse	"Invalid query parameters"
//	@Failure		401				{object}	APIResponse	"Unauthorized"
//	@Failure		500				{object}	APIResponse	"Internal server error"
//	@Router			/recipes/search [get]
func (h *RecipeHandler) SearchRecipes(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	query := c.Query("q")
	if query == "" {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Search query is required"))
	}

	// Parse query parameters
	params := h.parseRecipeQueryParams(c)

	recipes, total, err := h.recipeUsecase.SearchRecipes(c.Context(), userID, query, params)
	if err != nil {
		h.logger.LogError(err, "Failed to search recipes", map[string]interface{}{
			"user_id": userIDStr,
			"query":   query,
		})
		return ErrorResponse(c, err)
	}

	// Convert to responses
	responses := make([]interface{}, len(recipes))
	for i, recipe := range recipes {
		responses[i] = h.convertRecipeToResponse(recipe)
	}

	// Calculate pagination
	pagination := CalculatePagination(params.Page, params.Limit, total)

	return PaginatedSuccessResponse(c, responses, pagination, "Recipes found successfully")
}

// Helper methods

func (h *RecipeHandler) parseRecipeQueryParams(c *fiber.Ctx) domain.RecipeQueryParams {
	params := domain.RecipeQueryParams{
		Page:        getIntQuery(c, "page", 1),
		Limit:       getIntQuery(c, "limit", 10),
		Search:      c.Query("search"),
		Cuisine:     c.Query("cuisine"),
		Category:    c.Query("category"),
		Difficulty:  c.Query("difficulty"),
		MaxPrepTime: getIntQuery(c, "max_prep_time", 0),
		MaxCookTime: getIntQuery(c, "max_cook_time", 0),
		MaxCalories: getIntQuery(c, "max_calories", 0),
		IsFavorite:  getBoolQuery(c, "is_favorite", false),
		IsPublic:    getBoolQuery(c, "is_public", false),
		SortBy:      c.Query("sort_by", "created_at"),
		SortOrder:   c.Query("sort_order", "desc"),
	}

	// Parse tags
	if tagsStr := c.Query("tags"); tagsStr != "" {
		params.Tags = strings.Split(tagsStr, ",")
		for i, tag := range params.Tags {
			params.Tags[i] = strings.TrimSpace(tag)
		}
	}

	return params
}

func (h *RecipeHandler) convertRecipeToResponse(recipe *domain.Recipe) map[string]interface{} {
	response := map[string]interface{}{
		"id":             recipe.ID,
		"title":          recipe.Title,
		"description":    recipe.Description,
		"cuisine":        recipe.Cuisine,
		"category":       recipe.Category,
		"difficulty":     recipe.Difficulty,
		"prep_time":      recipe.PrepTime,
		"cook_time":      recipe.CookTime,
		"total_time":     recipe.TotalTime,
		"servings":       recipe.Servings,
		"calories":       recipe.Calories,
		"is_public":      recipe.IsPublic,
		"is_favorite":    recipe.IsFavorite,
		"rating":         recipe.Rating,
		"cook_count":     recipe.CookCount,
		"last_cooked_at": recipe.LastCookedAt,
		"source":         recipe.Source,
		"notes":          recipe.Notes,
		"created_at":     recipe.CreatedAt,
		"updated_at":     recipe.UpdatedAt,
		"ingredients":    h.convertIngredientsToResponse(recipe.Ingredients),
		"instructions":   h.convertInstructionsToResponse(recipe.Instructions),
		"media":          h.convertMediaToResponse(recipe.Media),
		"nutrition":      h.convertNutritionToResponse(recipe.Nutrition),
		"tags":           h.convertTagsToResponse(recipe.Tags),
	}

	// Add calculated fields
	if caloriesPerServing := recipe.GetEstimatedCaloriesPerServing(); caloriesPerServing != nil {
		response["calories_per_serving"] = *caloriesPerServing
	}

	if mainImage := recipe.GetMainImage(); mainImage != nil {
		response["main_image"] = map[string]interface{}{
			"url":      mainImage.URL,
			"alt_text": mainImage.AltText,
			"caption":  mainImage.Caption,
		}
	}

	return response
}

func (h *RecipeHandler) convertIngredientsToResponse(ingredients []domain.RecipeIngredient) []map[string]interface{} {
	responses := make([]map[string]interface{}, len(ingredients))
	for i, ingredient := range ingredients {
		responses[i] = map[string]interface{}{
			"id":                 ingredient.ID,
			"product_variant_id": ingredient.ProductVariantID,
			"name":               ingredient.Name,
			"quantity":           ingredient.Quantity,
			"unit_of_measure_id": ingredient.UnitOfMeasureID,
			"unit":               ingredient.Unit,
			"preparation":        ingredient.Preparation,
			"is_optional":        ingredient.IsOptional,
			"is_garnish":         ingredient.IsGarnish,
			"order":              ingredient.Order,
			"notes":              ingredient.Notes,
		}
	}
	return responses
}

func (h *RecipeHandler) convertInstructionsToResponse(instructions []domain.RecipeInstruction) []map[string]interface{} {
	responses := make([]map[string]interface{}, len(instructions))
	for i, instruction := range instructions {
		responses[i] = map[string]interface{}{
			"id":          instruction.ID,
			"step_number": instruction.StepNumber,
			"title":       instruction.Title,
			"instruction": instruction.Instruction,
			"duration":    instruction.Duration,
			"temperature": instruction.Temperature,
			"image_url":   instruction.ImageURL,
			"video_url":   instruction.VideoURL,
			"tips":        instruction.Tips,
		}
	}
	return responses
}

func (h *RecipeHandler) convertMediaToResponse(media []domain.RecipeMedia) []map[string]interface{} {
	responses := make([]map[string]interface{}, len(media))
	for i, m := range media {
		responses[i] = map[string]interface{}{
			"id":        m.ID,
			"type":      m.Type,
			"url":       m.URL,
			"filename":  m.Filename,
			"size":      m.Size,
			"mime_type": m.MimeType,
			"width":     m.Width,
			"height":    m.Height,
			"duration":  m.Duration,
			"is_main":   m.IsMain,
			"order":     m.Order,
			"caption":   m.Caption,
			"alt_text":  m.AltText,
		}
	}
	return responses
}

func (h *RecipeHandler) convertNutritionToResponse(nutrition *domain.RecipeNutrition) map[string]interface{} {
	if nutrition == nil {
		return nil
	}

	return map[string]interface{}{
		"calories":      nutrition.Calories,
		"protein":       nutrition.Protein,
		"carbohydrates": nutrition.Carbohydrates,
		"fat":           nutrition.Fat,
		"fiber":         nutrition.Fiber,
		"sugar":         nutrition.Sugar,
		"sodium":        nutrition.Sodium,
		"cholesterol":   nutrition.Cholesterol,
		"vitamin_a":     nutrition.VitaminA,
		"vitamin_c":     nutrition.VitaminC,
		"calcium":       nutrition.Calcium,
		"iron":          nutrition.Iron,
		"serving_size":  nutrition.ServingSize,
	}
}

func (h *RecipeHandler) convertTagsToResponse(tags []domain.RecipeTag) []map[string]interface{} {
	responses := make([]map[string]interface{}, len(tags))
	for i, tag := range tags {
		responses[i] = map[string]interface{}{
			"id":          tag.ID,
			"name":        tag.Name,
			"description": tag.Description,
			"color":       tag.Color,
			"icon":        tag.Icon,
			"usage_count": tag.UsageCount,
		}
	}
	return responses
}

func (h *RecipeHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)
	if validatorErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validatorErrors {
			field := strings.ToLower(e.Field())
			switch e.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = fmt.Sprintf("Minimum value is %s", e.Param())
			case "max":
				validationErrors[field] = fmt.Sprintf("Maximum value is %s", e.Param())
			case "email":
				validationErrors[field] = "Invalid email format"
			case "oneof":
				validationErrors[field] = fmt.Sprintf("Must be one of: %s", e.Param())
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}
	return validationErrors
}

// Helper functions for query parameter parsing
func getIntQuery(c *fiber.Ctx, key string, defaultValue int) int {
	if value := c.Query(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getBoolQuery(c *fiber.Ctx, key string, defaultValue bool) bool {
	if value := c.Query(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}
