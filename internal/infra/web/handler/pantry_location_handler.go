package handler

import (
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// PantryLocationHandler handles pantry location-related HTTP requests
type PantryLocationHandler struct {
	pantryRepo   domain.PantryRepository
	locationRepo domain.PantryLocationRepository
	authzService domain.PantryAuthorizationService
	logger       *logger.Logger
	validator    *validator.Validate
}

// NewPantryLocationHandler creates a new pantry location handler
func NewPantryLocationHandler(
	pantryRepo domain.PantryRepository,
	locationRepo domain.PantryLocationRepository,
	authzService domain.PantryAuthorizationService,
	log *logger.Logger,
) *PantryLocationHandler {
	return &PantryLocationHandler{
		pantryRepo:   pantryRepo,
		locationRepo: locationRepo,
		authzService: authzService,
		logger:       log,
		validator:    validator.New(),
	}
}

// CreateLocation creates a new pantry location
func (h *PantryLocationHandler) CreateLocation(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}
	
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}
	
	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}
	
	// Check permission
	canCreate, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionCreateLocations)
	if err != nil {
		h.logger.LogError(err, "Failed to check create location permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if !canCreate {
		return ErrorResponse(c, errors.ErrForbidden)
	}
	
	var req domain.CreateLocationRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Check if location name already exists in this pantry
	exists, err := h.locationRepo.ExistsByName(pantryID, req.Name)
	if err != nil {
		h.logger.LogError(err, "Failed to check location name existence", map[string]interface{}{
			"pantry_id": pantryIDStr,
			"name":      req.Name,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "A location with this name already exists in this pantry"))
	}
	
	// Create location
	location := domain.NewPantryLocation(pantryID, req.Name, req.Description)
	if err := h.locationRepo.Create(location); err != nil {
		h.logger.LogError(err, "Failed to create pantry location", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
			"name":      req.Name,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("pantry.location_created", location.ID.String(), map[string]interface{}{
		"pantry_id": pantryIDStr,
		"user_id":   userIDStr,
		"name":      location.Name,
	})
	
	// Convert to response
	response := h.locationToResponse(location)
	
	return CreatedResponse(c, response, "Location created successfully")
}

// GetLocations retrieves all locations for a pantry
func (h *PantryLocationHandler) GetLocations(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}
	
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}
	
	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}
	
	// Check permission
	canView, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionViewLocations)
	if err != nil {
		h.logger.LogError(err, "Failed to check view locations permission", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if !canView {
		return ErrorResponse(c, errors.ErrForbidden)
	}
	
	// Get locations
	locations, err := h.locationRepo.GetByPantry(pantryID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry locations", map[string]interface{}{
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.PantryLocationResponse, len(locations))
	for i, location := range locations {
		responses[i] = h.locationToResponse(location)
	}
	
	return SuccessResponse(c, responses, "Locations retrieved successfully")
}

// GetLocation retrieves a specific location
func (h *PantryLocationHandler) GetLocation(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}
	
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}
	
	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}
	
	locationIDStr := c.Params("locationId")
	locationID, err := uuid.Parse(locationIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid location ID"))
	}
	
	// Check permission
	canView, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionViewLocations)
	if err != nil {
		h.logger.LogError(err, "Failed to check view location permission", map[string]interface{}{
			"user_id":     userIDStr,
			"pantry_id":   pantryIDStr,
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if !canView {
		return ErrorResponse(c, errors.ErrForbidden)
	}
	
	// Get location
	location, err := h.locationRepo.GetByID(locationID)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry location", map[string]interface{}{
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Verify location belongs to the pantry
	if location.PantryID != pantryID {
		return ErrorResponse(c, errors.ErrNotFound)
	}
	
	// Convert to response
	response := h.locationToResponse(location)
	
	return SuccessResponse(c, response, "Location retrieved successfully")
}

// UpdateLocation updates a pantry location
func (h *PantryLocationHandler) UpdateLocation(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}
	
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}
	
	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}
	
	locationIDStr := c.Params("locationId")
	locationID, err := uuid.Parse(locationIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid location ID"))
	}
	
	// Check permission
	canEdit, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionEditLocations)
	if err != nil {
		h.logger.LogError(err, "Failed to check edit location permission", map[string]interface{}{
			"user_id":     userIDStr,
			"pantry_id":   pantryIDStr,
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if !canEdit {
		return ErrorResponse(c, errors.ErrForbidden)
	}
	
	var req domain.UpdateLocationRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Get current location
	location, err := h.locationRepo.GetByID(locationID)
	if err != nil {
		h.logger.LogError(err, "Failed to get location for update", map[string]interface{}{
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Verify location belongs to the pantry
	if location.PantryID != pantryID {
		return ErrorResponse(c, errors.ErrNotFound)
	}
	
	// Check if new name conflicts with existing locations (if name changed)
	if location.Name != req.Name {
		exists, err := h.locationRepo.ExistsByName(pantryID, req.Name)
		if err != nil {
			h.logger.LogError(err, "Failed to check location name existence", map[string]interface{}{
				"pantry_id":   pantryIDStr,
				"location_id": locationIDStr,
				"name":        req.Name,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "A location with this name already exists in this pantry"))
		}
	}
	
	// Update location
	location.UpdateDetails(req.Name, req.Description)
	if err := h.locationRepo.Update(location); err != nil {
		h.logger.LogError(err, "Failed to update pantry location", map[string]interface{}{
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("pantry.location_updated", location.ID.String(), map[string]interface{}{
		"pantry_id": pantryIDStr,
		"user_id":   userIDStr,
		"name":      location.Name,
	})
	
	// Convert to response
	response := h.locationToResponse(location)
	
	return SuccessResponse(c, response, "Location updated successfully")
}

// DeleteLocation deletes a pantry location
func (h *PantryLocationHandler) DeleteLocation(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}
	
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}
	
	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}
	
	locationIDStr := c.Params("locationId")
	locationID, err := uuid.Parse(locationIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid location ID"))
	}
	
	// Check permission
	canDelete, err := h.authzService.CheckPermission(userID, pantryID, domain.PermissionDeleteLocations)
	if err != nil {
		h.logger.LogError(err, "Failed to check delete location permission", map[string]interface{}{
			"user_id":     userIDStr,
			"pantry_id":   pantryIDStr,
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if !canDelete {
		return ErrorResponse(c, errors.ErrForbidden)
	}
	
	// Get location to verify it belongs to the pantry
	location, err := h.locationRepo.GetByID(locationID)
	if err != nil {
		h.logger.LogError(err, "Failed to get location for deletion", map[string]interface{}{
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Verify location belongs to the pantry
	if location.PantryID != pantryID {
		return ErrorResponse(c, errors.ErrNotFound)
	}
	
	// Delete location
	if err := h.locationRepo.Delete(locationID); err != nil {
		h.logger.LogError(err, "Failed to delete pantry location", map[string]interface{}{
			"location_id": locationIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("pantry.location_deleted", locationIDStr, map[string]interface{}{
		"pantry_id": pantryIDStr,
		"user_id":   userIDStr,
		"name":      location.Name,
	})
	
	return SuccessResponse(c, nil, "Location deleted successfully")
}

// Helper methods

func (h *PantryLocationHandler) locationToResponse(location *domain.PantryLocation) domain.PantryLocationResponse {
	return domain.PantryLocationResponse{
		ID:          location.ID,
		PantryID:    location.PantryID,
		Name:        location.Name,
		Description: location.Description,
		CreatedAt:   location.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   location.UpdatedAt.Format(time.RFC3339),
	}
}

func (h *PantryLocationHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)
	
	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}
	
	return validationErrors
}
