package handler

import "time"

// AuthResponse represents authentication response data
type AuthResponse struct {
	User        UserResponse `json:"user"`
	AccessToken string       `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	TokenType   string       `json:"token_type" example:"Bearer"`
	ExpiresAt   time.Time    `json:"expires_at" example:"2024-01-15T10:30:00Z"`
}

// UserResponse represents user data in responses
type UserResponse struct {
	ID        string  `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Username  string  `json:"username" example:"johndoe"`
	Email     string  `json:"email" example:"<EMAIL>"`
	FirstName *string `json:"first_name,omitempty" example:"John"`
	LastName  *string `json:"last_name,omitempty" example:"Doe"`
	CreatedAt string  `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt string  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
}

// PantryResponse represents pantry data in responses
type PantryResponse struct {
	ID          string    `json:"id" example:"550e8400-e29b-41d4-a716-446655440001"`
	Name        string    `json:"name" example:"My Kitchen"`
	Description *string   `json:"description,omitempty" example:"Main kitchen pantry"`
	OwnerID     string    `json:"owner_id" example:"550e8400-e29b-41d4-a716-446655440000"`
	CreatedAt   time.Time `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt   time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
}

// InventoryItemResponse represents inventory item data in responses
type InventoryItemResponse struct {
	ID               string                  `json:"id" example:"550e8400-e29b-41d4-a716-446655440002"`
	PantryID         string                  `json:"pantry_id" example:"550e8400-e29b-41d4-a716-446655440001"`
	ProductVariantID string                  `json:"product_variant_id" example:"550e8400-e29b-41d4-a716-446655440003"`
	Quantity         float64                 `json:"quantity" example:"5.0"`
	UnitOfMeasureID  string                  `json:"unit_of_measure_id" example:"550e8400-e29b-41d4-a716-446655440004"`
	LocationID       *string                 `json:"location_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440005"`
	ExpirationDate   *string                 `json:"expiration_date,omitempty" example:"2024-02-15"`
	PurchaseDate     *string                 `json:"purchase_date,omitempty" example:"2024-01-15"`
	PurchasePrice    *float64                `json:"purchase_price,omitempty" example:"12.99"`
	Notes            *string                 `json:"notes,omitempty" example:"Organic brand"`
	CreatedAt        time.Time               `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt        time.Time               `json:"updated_at" example:"2024-01-15T10:30:00Z"`
	ProductVariant   *ProductVariantResponse `json:"product_variant,omitempty"`
	UnitOfMeasure    *UnitOfMeasureResponse  `json:"unit_of_measure,omitempty"`
	Location         *PantryLocationResponse `json:"location,omitempty"`
}

// ProductVariantResponse represents product variant data in responses
type ProductVariantResponse struct {
	ID          string           `json:"id" example:"550e8400-e29b-41d4-a716-446655440003"`
	ProductID   string           `json:"product_id" example:"550e8400-e29b-41d4-a716-446655440006"`
	Name        string           `json:"name" example:"1 Liter"`
	Description *string          `json:"description,omitempty" example:"1 liter bottle"`
	Barcode     *string          `json:"barcode,omitempty" example:"1234567890123"`
	SKU         *string          `json:"sku,omitempty" example:"MILK-1L-001"`
	Size        *float64         `json:"size,omitempty" example:"1.0"`
	SizeUnit    *string          `json:"size_unit,omitempty" example:"L"`
	CreatedAt   time.Time        `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt   time.Time        `json:"updated_at" example:"2024-01-15T10:30:00Z"`
	Product     *ProductResponse `json:"product,omitempty"`
}

// ProductResponse represents product data in responses
type ProductResponse struct {
	ID          string            `json:"id" example:"550e8400-e29b-41d4-a716-446655440006"`
	Name        string            `json:"name" example:"Organic Milk"`
	Description *string           `json:"description,omitempty" example:"Fresh organic whole milk"`
	Brand       *string           `json:"brand,omitempty" example:"Organic Valley"`
	CategoryID  string            `json:"category_id" example:"550e8400-e29b-41d4-a716-446655440007"`
	CreatedAt   time.Time         `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt   time.Time         `json:"updated_at" example:"2024-01-15T10:30:00Z"`
	Category    *CategoryResponse `json:"category,omitempty"`
}

// CategoryResponse represents category data in responses
type CategoryResponse struct {
	ID          string    `json:"id" example:"550e8400-e29b-41d4-a716-446655440007"`
	Name        string    `json:"name" example:"Dairy"`
	Description *string   `json:"description,omitempty" example:"Dairy products"`
	ParentID    *string   `json:"parent_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440008"`
	CreatedAt   time.Time `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt   time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
}

// UnitOfMeasureResponse represents unit of measure data in responses
type UnitOfMeasureResponse struct {
	ID               string    `json:"id" example:"550e8400-e29b-41d4-a716-446655440004"`
	Name             string    `json:"name" example:"pieces"`
	Symbol           string    `json:"symbol" example:"pcs"`
	Type             string    `json:"type" example:"count"`
	BaseUnitID       *string   `json:"base_unit_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440009"`
	ConversionFactor *float64  `json:"conversion_factor,omitempty" example:"1.0"`
	CreatedAt        time.Time `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt        time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
}

// PantryLocationResponse represents pantry location data in responses
type PantryLocationResponse struct {
	ID          string    `json:"id" example:"550e8400-e29b-41d4-a716-446655440005"`
	PantryID    string    `json:"pantry_id" example:"550e8400-e29b-41d4-a716-446655440001"`
	Name        string    `json:"name" example:"Main Fridge"`
	Description *string   `json:"description,omitempty" example:"Main refrigerator"`
	CreatedAt   time.Time `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt   time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
}

// ShoppingListResponse represents shopping list data in responses
type ShoppingListResponse struct {
	TotalItems          int                         `json:"total_items" example:"12"`
	HighPriorityItems   int                         `json:"high_priority_items" example:"3"`
	MediumPriorityItems int                         `json:"medium_priority_items" example:"5"`
	LowPriorityItems    int                         `json:"low_priority_items" example:"4"`
	EstimatedCost       float64                     `json:"estimated_cost" example:"45.67"`
	GeneratedAt         time.Time                   `json:"generated_at" example:"2024-01-15T10:30:00Z"`
	Items               []ShoppingListItemResponse  `json:"items"`
	Summary             ShoppingListSummaryResponse `json:"summary"`
}

// ShoppingListItemResponse represents shopping list item data in responses
type ShoppingListItemResponse struct {
	ProductVariantID    string                  `json:"product_variant_id" example:"550e8400-e29b-41d4-a716-446655440003"`
	ProductName         string                  `json:"product_name" example:"Organic Milk"`
	VariantName         string                  `json:"variant_name" example:"1 Liter"`
	CategoryID          string                  `json:"category_id" example:"550e8400-e29b-41d4-a716-446655440007"`
	CategoryName        string                  `json:"category_name" example:"Dairy"`
	RecommendedQuantity float64                 `json:"recommended_quantity" example:"2.0"`
	UnitOfMeasureID     string                  `json:"unit_of_measure_id" example:"550e8400-e29b-41d4-a716-446655440004"`
	UnitName            string                  `json:"unit_name" example:"pieces"`
	UnitSymbol          string                  `json:"unit_symbol" example:"pcs"`
	Priority            string                  `json:"priority" example:"high"`
	Reason              string                  `json:"reason" example:"Out of stock"`
	EstimatedPrice      *float64                `json:"estimated_price,omitempty" example:"12.99"`
	CurrentStock        float64                 `json:"current_stock" example:"0.0"`
	ProductVariant      *ProductVariantResponse `json:"product_variant,omitempty"`
}

// ShoppingListSummaryResponse represents shopping list summary data
type ShoppingListSummaryResponse struct {
	TotalCost         float64                       `json:"total_cost" example:"45.67"`
	TopCategories     []ShoppingListCategorySummary `json:"top_categories"`
	RecommendedStores []string                      `json:"recommended_stores,omitempty"`
}

// ShoppingListCategorySummary represents category summary in shopping list
type ShoppingListCategorySummary struct {
	CategoryID   string  `json:"category_id" example:"550e8400-e29b-41d4-a716-446655440007"`
	CategoryName string  `json:"category_name" example:"Dairy"`
	ItemCount    int     `json:"item_count" example:"3"`
	TotalCost    float64 `json:"total_cost" example:"25.50"`
}

// ExpirationTrackingResponse represents expiration tracking data in responses
type ExpirationTrackingResponse struct {
	TotalItems    int                       `json:"total_items" example:"12"`
	WarningItems  int                       `json:"warning_items" example:"5"`
	AlertItems    int                       `json:"alert_items" example:"4"`
	CriticalItems int                       `json:"critical_items" example:"2"`
	ExpiredItems  int                       `json:"expired_items" example:"1"`
	AlertsSent    int                       `json:"alerts_sent" example:"8"`
	CheckedAt     time.Time                 `json:"checked_at" example:"2024-01-15T10:30:00Z"`
	Items         []ExpirationItemResponse  `json:"items"`
	Summary       ExpirationSummaryResponse `json:"summary"`
}

// ExpirationItemResponse represents expiring item data in responses
type ExpirationItemResponse struct {
	ItemID           string                     `json:"item_id" example:"550e8400-e29b-41d4-a716-446655440002"`
	ProductVariantID string                     `json:"product_variant_id" example:"550e8400-e29b-41d4-a716-446655440003"`
	ProductName      string                     `json:"product_name" example:"Organic Milk"`
	VariantName      string                     `json:"variant_name" example:"1 Liter"`
	CategoryID       string                     `json:"category_id" example:"550e8400-e29b-41d4-a716-446655440007"`
	CategoryName     string                     `json:"category_name" example:"Dairy"`
	Quantity         float64                    `json:"quantity" example:"2.0"`
	UnitOfMeasureID  string                     `json:"unit_of_measure_id" example:"550e8400-e29b-41d4-a716-446655440004"`
	UnitName         string                     `json:"unit_name" example:"pieces"`
	UnitSymbol       string                     `json:"unit_symbol" example:"pcs"`
	ExpirationDate   string                     `json:"expiration_date" example:"2024-01-17"`
	DaysUntilExpiry  int                        `json:"days_until_expiry" example:"2"`
	ExpirationStatus string                     `json:"expiration_status" example:"critical"`
	LocationID       *string                    `json:"location_id,omitempty" example:"550e8400-e29b-41d4-a716-446655440005"`
	LocationName     *string                    `json:"location_name,omitempty" example:"Main Fridge"`
	PurchasePrice    *float64                   `json:"purchase_price,omitempty" example:"7.98"`
	EstimatedValue   float64                    `json:"estimated_value" example:"7.98"`
	Actions          []ExpirationActionResponse `json:"actions"`
}

// ExpirationActionResponse represents recommended action for expiring item
type ExpirationActionResponse struct {
	Type        string `json:"type" example:"consume"`
	Description string `json:"description" example:"Use immediately or today"`
	Priority    int    `json:"priority" example:"1"`
}

// ExpirationSummaryResponse represents expiration summary data
type ExpirationSummaryResponse struct {
	TotalValue         float64                     `json:"total_value" example:"47.85"`
	WarningValue       float64                     `json:"warning_value" example:"18.50"`
	AlertValue         float64                     `json:"alert_value" example:"15.47"`
	CriticalValue      float64                     `json:"critical_value" example:"7.98"`
	ExpiredValue       float64                     `json:"expired_value" example:"5.99"`
	TopCategories      []ExpirationCategorySummary `json:"top_categories"`
	RecommendedActions []string                    `json:"recommended_actions"`
}

// ExpirationCategorySummary represents category summary in expiration tracking
type ExpirationCategorySummary struct {
	CategoryID   string  `json:"category_id" example:"550e8400-e29b-41d4-a716-446655440007"`
	CategoryName string  `json:"category_name" example:"Dairy"`
	ItemCount    int     `json:"item_count" example:"6"`
	TotalValue   float64 `json:"total_value" example:"28.47"`
	WorstStatus  string  `json:"worst_status" example:"expired"`
}
