package handler

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// CreatePurchaseRequest represents the request body for creating a purchase
type CreatePurchaseRequest struct {
	PantryID        uuid.UUID                `json:"pantry_id" validate:"required"`
	PurchaseDate    string                   `json:"purchase_date" validate:"required"`
	TotalAmount     float64                  `json:"total_amount" validate:"required,min=0"`
	Currency        string                   `json:"currency" validate:"required,len=3"`
	StoreID         *uuid.UUID              `json:"store_id,omitempty"`
	StoreName       *string                 `json:"store_name,omitempty"`
	ReceiptImageURL *string                 `json:"receipt_image_url,omitempty"`
	Notes           *string                 `json:"notes,omitempty"`
	Items           []CreatePurchaseItemRequest `json:"items" validate:"required,min=1,dive"`
}

// CreatePurchaseItemRequest represents a purchase item in the create request
type CreatePurchaseItemRequest struct {
	ProductVariantID uuid.UUID `json:"product_variant_id" validate:"required"`
	QuantityBought   float64   `json:"quantity_bought" validate:"required,min=0"`
	UnitOfMeasureID  uuid.UUID `json:"unit_of_measure_id" validate:"required"`
	PricePerUnit     float64   `json:"price_per_unit" validate:"required,min=0"`
	Notes            *string   `json:"notes,omitempty"`
}

// PurchaseHandler handles purchase-related HTTP requests
type PurchaseHandler struct {
	purchaseUsecase *usecases.PurchaseUsecase
}

// NewPurchaseHandler creates a new PurchaseHandler
func NewPurchaseHandler(purchaseUsecase *usecases.PurchaseUsecase) *PurchaseHandler {
	return &PurchaseHandler{
		purchaseUsecase: purchaseUsecase,
	}
}

// RegisterRoutes registers the purchase routes
func (h *PurchaseHandler) RegisterRoutes(router fiber.Router) {
	purchases := router.Group("/purchases")
	{
		purchases.Post("/", h.CreatePurchase)
		purchases.Get("/pantry/:pantry_id", h.GetPurchasesByPantry)
		purchases.Get("/:id", h.GetPurchaseByID)
		purchases.Post("/:id/inventory", h.LinkPurchaseToInventory)
		purchases.Delete("/:id", h.DeletePurchase)
	}
}

// CreatePurchase handles the creation of a new purchase
// @Summary Create a new purchase
// @Description Creates a new purchase with items
// @Tags Purchases
// @Accept json
// @Produce json
// @Param body body CreatePurchaseRequest true "Purchase details"
// @Success 201 {object} APIResponse{data=domain.Purchase}
// @Failure 400 {object} APIResponse{error=ErrorInfo}
// @Failure 403 {object} APIResponse{error=ErrorInfo}
// @Router /purchases [post]
func (h *PurchaseHandler) CreatePurchase(c *fiber.Ctx) error {
	var req CreatePurchaseRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Parse purchase date
	purchaseDate, err := time.Parse("2006-01-02", req.PurchaseDate)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid purchase date format"))
	}

	// Get user ID from context
	userID := c.Locals("userID").(uuid.UUID)

	// Create purchase request
	createReq := &usecases.CreatePurchaseRequest{
		PantryID:        req.PantryID,
		PurchaseDate:    purchaseDate,
		TotalAmount:     req.TotalAmount,
		Currency:        req.Currency,
		StoreID:         req.StoreID,
		StoreName:       req.StoreName,
		ReceiptImageURL: req.ReceiptImageURL,
		Notes:           req.Notes,
		Items:           make([]usecases.CreatePurchaseItemRequest, len(req.Items)),
	}

	// Convert items
	for i, item := range req.Items {
		createReq.Items[i] = usecases.CreatePurchaseItemRequest{
			ProductVariantID: item.ProductVariantID,
			QuantityBought:   item.QuantityBought,
			UnitOfMeasureID:  item.UnitOfMeasureID,
			PricePerUnit:     item.PricePerUnit,
			Notes:            item.Notes,
		}
	}

	// Create purchase
	purchase, err := h.purchaseUsecase.CreatePurchase(c.Context(), userID, createReq)
	if err != nil {
		return ErrorResponse(c, err)
	}

	return CreatedResponse(c, purchase, "Purchase created successfully")
}

// GetPurchasesByPantry handles retrieving purchases for a pantry
// @Summary Get pantry purchases
// @Description Retrieves purchases for a specific pantry with pagination
// @Tags Purchases
// @Produce json
// @Param pantry_id path string true "Pantry ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} PaginatedResponse{data=[]domain.Purchase}
// @Failure 403 {object} APIResponse{error=ErrorInfo}
// @Router /purchases/pantry/{pantry_id} [get]
func (h *PurchaseHandler) GetPurchasesByPantry(c *fiber.Ctx) error {
	pantryID, err := uuid.Parse(c.Params("pantry_id"))
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	page, limit := ParsePaginationParams(c)
	userID := c.Locals("userID").(uuid.UUID)

	purchases, total, err := h.purchaseUsecase.GetPurchasesByPantry(c.Context(), userID, pantryID, page, limit)
	if err != nil {
		return ErrorResponse(c, err)
	}

	pagination := CalculatePagination(page, limit, total)
	return PaginatedSuccessResponse(c, purchases, pagination, "Purchases retrieved successfully")
}

// GetPurchaseByID handles retrieving a specific purchase
// @Summary Get purchase by ID
// @Description Retrieves a specific purchase by ID
// @Tags Purchases
// @Produce json
// @Param id path string true "Purchase ID"
// @Success 200 {object} APIResponse{data=domain.Purchase}
// @Failure 403 {object} APIResponse{error=ErrorInfo}
// @Failure 404 {object} APIResponse{error=ErrorInfo}
// @Router /purchases/{id} [get]
func (h *PurchaseHandler) GetPurchaseByID(c *fiber.Ctx) error {
	purchaseID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid purchase ID"))
	}

	userID := c.Locals("userID").(uuid.UUID)

	purchase, err := h.purchaseUsecase.GetPurchase(c.Context(), userID, purchaseID)
	if err != nil {
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, purchase, "Purchase retrieved successfully")
}

// LinkPurchaseToInventory handles linking a purchase to inventory items
// @Summary Link purchase to inventory
// @Description Creates inventory items from a purchase
// @Tags Purchases
// @Produce json
// @Param id path string true "Purchase ID"
// @Param location_id query string false "Location ID"
// @Success 200 {object} APIResponse
// @Failure 403 {object} APIResponse{error=ErrorInfo}
// @Failure 404 {object} APIResponse{error=ErrorInfo}
// @Router /purchases/{id}/inventory [post]
func (h *PurchaseHandler) LinkPurchaseToInventory(c *fiber.Ctx) error {
	purchaseID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid purchase ID"))
	}

	var locationID *uuid.UUID
	if locID := c.Query("location_id"); locID != "" {
		parsed, err := uuid.Parse(locID)
		if err != nil {
			return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid location ID"))
		}
		locationID = &parsed
	}

	userID := c.Locals("userID").(uuid.UUID)

	if err = h.purchaseUsecase.LinkPurchaseToInventory(c.Context(), userID, purchaseID, locationID); err != nil {
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, nil, "Purchase linked to inventory successfully")
}

// DeletePurchase handles deleting a purchase
// @Summary Delete purchase
// @Description Deletes a purchase by ID
// @Tags Purchases
// @Param id path string true "Purchase ID"
// @Success 204 "No Content"
// @Failure 403 {object} APIResponse{error=ErrorInfo}
// @Failure 404 {object} APIResponse{error=ErrorInfo}
// @Router /purchases/{id} [delete]
func (h *PurchaseHandler) DeletePurchase(c *fiber.Ctx) error {
	purchaseID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid purchase ID"))
	}

	userID := c.Locals("userID").(uuid.UUID)

	if err = h.purchaseUsecase.DeletePurchase(c.Context(), userID, purchaseID); err != nil {
		return ErrorResponse(c, err)
	}

	return NoContentResponse(c)
}