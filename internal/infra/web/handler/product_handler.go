package handler

import (
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// ProductHandler handles product-related HTTP requests
type ProductHandler struct {
	productRepo  domain.ProductRepository
	categoryRepo domain.CategoryRepository
	logger       *logger.Logger
	validator    *validator.Validate
}

// NewProductHandler creates a new product handler
func NewProductHandler(
	productRepo domain.ProductRepository,
	categoryRepo domain.CategoryRepository,
	log *logger.Logger,
) *ProductHandler {
	return &ProductHandler{
		productRepo:  productRepo,
		categoryRepo: categoryRepo,
		logger:       log,
		validator:    validator.New(),
	}
}

// CreateProduct creates a new product
func (h *ProductHandler) CreateProduct(c *fiber.Ctx) error {
	var req domain.CreateProductRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Validate category exists
	_, err := h.categoryRepo.GetByID(req.CategoryID)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "Category not found"))
		}
		h.logger.LogError(err, "Failed to validate category", map[string]interface{}{
			"category_id": req.CategoryID,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	// Check if product already exists
	exists, err := h.productRepo.ExistsByNameAndBrandAndCategory(req.Name, req.Brand, req.CategoryID)
	if err != nil {
		h.logger.LogError(err, "Failed to check product existence", map[string]interface{}{
			"name":        req.Name,
			"brand":       req.Brand,
			"category_id": req.CategoryID,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Product with this name, brand, and category already exists"))
	}
	
	// Create product
	product := domain.NewProduct(req.Name, req.Description, req.CategoryID, req.Brand)
	if err := h.productRepo.Create(product); err != nil {
		h.logger.LogError(err, "Failed to create product", map[string]interface{}{
			"name":        req.Name,
			"brand":       req.Brand,
			"category_id": req.CategoryID,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("product.created", product.ID.String(), map[string]interface{}{
		"name":        product.Name,
		"brand":       product.Brand,
		"category_id": product.CategoryID,
	})
	
	// Convert to response
	response := h.productToResponse(product)
	
	return CreatedResponse(c, response, "Product created successfully")
}

// GetProducts retrieves products with optional filtering and search
func (h *ProductHandler) GetProducts(c *fiber.Ctx) error {
	// Parse query parameters
	query := c.Query("query", "")
	categoryIDStr := c.Query("category_id", "")
	pageStr := c.Query("page", "1")
	limitStr := c.Query("limit", "20")
	
	// Parse pagination
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}
	
	// Parse category ID if provided
	var categoryID *uuid.UUID
	if categoryIDStr != "" {
		parsedCategoryID, err := uuid.Parse(categoryIDStr)
		if err != nil {
			return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid category ID"))
		}
		categoryID = &parsedCategoryID
	}
	
	// Search products
	products, total, err := h.productRepo.SearchProducts(query, categoryID, page, limit)
	if err != nil {
		h.logger.LogError(err, "Failed to search products", map[string]interface{}{
			"query":       query,
			"category_id": categoryID,
			"page":        page,
			"limit":       limit,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.ProductResponse, len(products))
	for i, product := range products {
		responses[i] = h.productToResponse(product)
	}
	
	// Create pagination metadata
	totalPages := (total + int64(limit) - 1) / int64(limit)
	metadata := map[string]interface{}{
		"page":        page,
		"limit":       limit,
		"total":       total,
		"total_pages": totalPages,
		"has_next":    page < int(totalPages),
		"has_prev":    page > 1,
	}
	
	return SuccessResponseWithMetadata(c, responses, "Products retrieved successfully", metadata)
}

// GetProduct retrieves a specific product
func (h *ProductHandler) GetProduct(c *fiber.Ctx) error {
	productIDStr := c.Params("productId")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid product ID"))
	}
	
	// Get product
	product, err := h.productRepo.GetByID(productID)
	if err != nil {
		h.logger.LogError(err, "Failed to get product", map[string]interface{}{
			"product_id": productIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to response
	response := h.productToResponse(product)
	
	return SuccessResponse(c, response, "Product retrieved successfully")
}

// UpdateProduct updates a product
func (h *ProductHandler) UpdateProduct(c *fiber.Ctx) error {
	productIDStr := c.Params("productId")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid product ID"))
	}
	
	var req domain.UpdateProductRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Get current product
	product, err := h.productRepo.GetByID(productID)
	if err != nil {
		h.logger.LogError(err, "Failed to get product for update", map[string]interface{}{
			"product_id": productIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Validate category exists
	_, err = h.categoryRepo.GetByID(req.CategoryID)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "Category not found"))
		}
		h.logger.LogError(err, "Failed to validate category", map[string]interface{}{
			"category_id": req.CategoryID,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	// Check if updated product conflicts with existing products
	if product.Name != req.Name || 
	   (product.Brand == nil && req.Brand != nil) ||
	   (product.Brand != nil && req.Brand == nil) ||
	   (product.Brand != nil && req.Brand != nil && *product.Brand != *req.Brand) ||
	   product.CategoryID != req.CategoryID {
		
		exists, err := h.productRepo.ExistsByNameAndBrandAndCategory(req.Name, req.Brand, req.CategoryID)
		if err != nil {
			h.logger.LogError(err, "Failed to check product existence", map[string]interface{}{
				"product_id":  productIDStr,
				"name":        req.Name,
				"brand":       req.Brand,
				"category_id": req.CategoryID,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Product with this name, brand, and category already exists"))
		}
	}
	
	// Update product
	product.UpdateDetails(req.Name, req.Description, req.CategoryID, req.Brand)
	if err := h.productRepo.Update(product); err != nil {
		h.logger.LogError(err, "Failed to update product", map[string]interface{}{
			"product_id": productIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("product.updated", product.ID.String(), map[string]interface{}{
		"name":        product.Name,
		"brand":       product.Brand,
		"category_id": product.CategoryID,
	})
	
	// Convert to response
	response := h.productToResponse(product)
	
	return SuccessResponse(c, response, "Product updated successfully")
}

// DeleteProduct deletes a product
func (h *ProductHandler) DeleteProduct(c *fiber.Ctx) error {
	productIDStr := c.Params("productId")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid product ID"))
	}
	
	// Get product to log details
	product, err := h.productRepo.GetByID(productID)
	if err != nil {
		h.logger.LogError(err, "Failed to get product for deletion", map[string]interface{}{
			"product_id": productIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Delete product
	if err := h.productRepo.Delete(productID); err != nil {
		h.logger.LogError(err, "Failed to delete product", map[string]interface{}{
			"product_id": productIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("product.deleted", productIDStr, map[string]interface{}{
		"name":        product.Name,
		"brand":       product.Brand,
		"category_id": product.CategoryID,
	})
	
	return SuccessResponse(c, nil, "Product deleted successfully")
}

// Helper methods

func (h *ProductHandler) productToResponse(product *domain.Product) domain.ProductResponse {
	return domain.ProductResponse{
		ID:          product.ID,
		Name:        product.Name,
		Description: product.Description,
		CategoryID:  product.CategoryID,
		Brand:       product.Brand,
		CreatedAt:   product.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   product.UpdatedAt.Format(time.RFC3339),
	}
}

func (h *ProductHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)
	
	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "url":
				validationErrors[field] = "Must be a valid URL"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}
	
	return validationErrors
}
