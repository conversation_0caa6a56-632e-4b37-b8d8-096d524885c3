package handler

import (
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// InventoryHandler handles inventory-related HTTP requests
type InventoryHandler struct {
	inventoryUsecase *usecases.InventoryUsecase
	logger           *logger.Logger
	validator        *validator.Validate
}

// NewInventoryHandler creates a new inventory handler
func NewInventoryHandler(inventoryUsecase *usecases.InventoryUsecase, log *logger.Logger) *InventoryHandler {
	return &InventoryHandler{
		inventoryUsecase: inventoryUsecase,
		logger:           log,
		validator:        validator.New(),
	}
}

// CreateInventoryItem creates a new inventory item
//
//	@Summary		Create inventory item
//	@Description	Create a new inventory item in the specified pantry. Returns enhanced inventory item with human-readable names for all related entities.
//	@Tags			Inventory
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string								true	"Pantry ID"
//	@Param			request		body		domain.CreateInventoryItemRequest	true	"Inventory item data"
//	@Success		201			{object}	APIResponse{data=InventoryItemResponse}	"Inventory item created successfully with enhanced response including names"
//	@Failure		400			{object}	APIResponse							"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden - no access to pantry"
//	@Failure		404			{object}	APIResponse							"Pantry or product variant not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/pantries/{pantryId}/inventory [post]
func (h *InventoryHandler) CreateInventoryItem(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	var req domain.CreateInventoryItemRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.CreateInventoryItem(c.Context(), userID, pantryID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to create inventory item", map[string]interface{}{
			"user_id":            userIDStr,
			"pantry_id":          pantryIDStr,
			"product_variant_id": req.ProductVariantID,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.item_created", response.ID.String(), map[string]interface{}{
		"user_id":            userIDStr,
		"pantry_id":          pantryIDStr,
		"product_variant_id": req.ProductVariantID,
		"quantity":           response.Quantity,
	})

	return CreatedResponse(c, response, "Inventory item created successfully")
}

// GetInventoryItem retrieves an inventory item by ID
//
//	@Summary		Get inventory item
//	@Description	Retrieve a specific inventory item by ID. Returns enhanced inventory item with human-readable names for all related entities.
//	@Tags			Inventory
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			itemId		path		string	true	"Inventory item ID"
//	@Success		200			{object}	APIResponse{data=InventoryItemResponse}	"Inventory item retrieved successfully with enhanced response including names"
//	@Failure		400			{object}	APIResponse	"Invalid item ID"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - no access to item"
//	@Failure		404			{object}	APIResponse	"Inventory item not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/inventory/{itemId} [get]
func (h *InventoryHandler) GetInventoryItem(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid item ID"))
	}

	response, err := h.inventoryUsecase.GetInventoryItem(c.Context(), userID, itemID)
	if err != nil {
		h.logger.LogError(err, "Failed to get inventory item", map[string]interface{}{
			"user_id": userIDStr,
			"item_id": itemIDStr,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, response, "Inventory item retrieved successfully")
}

// GetPantryInventory retrieves inventory items for a pantry
//
//	@Summary		Get pantry inventory
//	@Description	Retrieve all inventory items in the specified pantry with pagination. Returns enhanced inventory items with human-readable names for all related entities.
//	@Tags			Inventory
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string	true	"Pantry ID"
//	@Param			page		query		int		false	"Page number (default: 1)"
//	@Param			limit		query		int		false	"Items per page (default: 10, max: 100)"
//	@Success		200			{object}	PaginatedResponse{data=[]InventoryItemResponse}	"Inventory retrieved successfully with enhanced response including names"
//	@Failure		400			{object}	APIResponse	"Invalid pantry ID or query parameters"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - no access to pantry"
//	@Failure		404			{object}	APIResponse	"Pantry not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/pantries/{pantryId}/inventory [get]
func (h *InventoryHandler) GetPantryInventory(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	// Parse pagination parameters
	page, limit := ParsePaginationParams(c)

	items, total, err := h.inventoryUsecase.GetPantryInventory(c.Context(), userID, pantryID, page, limit)
	if err != nil {
		h.logger.LogError(err, "Failed to get pantry inventory", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	responses := make([]domain.InventoryItemResponse, len(items))
	for i, item := range items {
		responses[i] = *item
	}

	// Calculate pagination
	pagination := CalculatePagination(page, limit, total)

	return PaginatedSuccessResponse(c, responses, pagination, "Pantry inventory retrieved successfully")
}

// UpdateInventoryItem updates an inventory item
//
//	@Summary		Update inventory item
//	@Description	Update an existing inventory item. Returns enhanced inventory item with human-readable names for all related entities.
//	@Tags			Inventory
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			itemId		path		string								true	"Inventory item ID"
//	@Param			request		body		domain.UpdateInventoryItemRequest	true	"Updated inventory item data"
//	@Success		200			{object}	APIResponse{data=InventoryItemResponse}	"Inventory item updated successfully with enhanced response including names"
//	@Failure		400			{object}	APIResponse							"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden - no access to item"
//	@Failure		404			{object}	APIResponse							"Inventory item not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/inventory/{itemId} [put]
func (h *InventoryHandler) UpdateInventoryItem(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid item ID"))
	}

	var req domain.UpdateInventoryItemRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.UpdateInventoryItem(c.Context(), userID, itemID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to update inventory item", map[string]interface{}{
			"user_id": userIDStr,
			"item_id": itemIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.item_updated", response.ID.String(), map[string]interface{}{
		"user_id": userIDStr,
		"item_id": itemIDStr,
	})

	return SuccessResponse(c, response, "Inventory item updated successfully")
}

// ConsumeInventoryItem consumes quantity from an inventory item
//
//	@Summary		Consume inventory item
//	@Description	Consume a specified quantity from an inventory item. Returns enhanced inventory item with human-readable names for all related entities.
//	@Tags			Inventory
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			itemId		path		string							true	"Inventory item ID"
//	@Param			request		body		domain.ConsumeInventoryRequest	true	"Consumption data"
//	@Success		200			{object}	APIResponse{data=InventoryItemResponse}	"Inventory item consumed successfully with enhanced response including names"
//	@Failure		400			{object}	APIResponse						"Invalid input or insufficient quantity"
//	@Failure		401			{object}	APIResponse						"Unauthorized"
//	@Failure		403			{object}	APIResponse						"Forbidden - no access to item"
//	@Failure		404			{object}	APIResponse						"Inventory item not found"
//	@Failure		500			{object}	APIResponse						"Internal server error"
//	@Router			/inventory/{itemId}/consume [post]
func (h *InventoryHandler) ConsumeInventoryItem(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	itemIDStr := c.Params("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid item ID"))
	}

	var req domain.ConsumeInventoryRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.ConsumeInventoryItem(c.Context(), userID, itemID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to consume inventory item", map[string]interface{}{
			"user_id":           userIDStr,
			"item_id":           itemIDStr,
			"consumed_quantity": req.ConsumedQuantity,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.item_consumed", response.ID.String(), map[string]interface{}{
		"user_id":           userIDStr,
		"item_id":           itemIDStr,
		"consumed_quantity": req.ConsumedQuantity,
		"remaining":         response.Quantity,
	})

	return SuccessResponse(c, response, "Inventory item consumed successfully")
}

// Helper methods

// convertInventoryItemToResponse converts a domain inventory item to response format
func (h *InventoryHandler) convertInventoryItemToResponse(item *domain.InventoryItem) *domain.InventoryItemResponse {
	response := &domain.InventoryItemResponse{
		ID:               item.ID,
		PantryID:         item.PantryID,
		LocationID:       item.LocationID,
		ProductVariantID: item.ProductVariantID,
		Quantity:         item.Quantity,
		UnitOfMeasureID:  item.UnitOfMeasureID,
		PurchasePrice:    item.PurchasePrice,
		Notes:            item.Notes,
		Status:           item.GetStatus(), // Get current status
		CreatedAt:        item.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:        item.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Convert dates to string pointers
	if item.PurchaseDate != nil {
		dateStr := item.PurchaseDate.Format("2006-01-02")
		response.PurchaseDate = &dateStr
	}

	if item.ExpirationDate != nil {
		dateStr := item.ExpirationDate.Format("2006-01-02")
		response.ExpirationDate = &dateStr
	}

	return response
}

// parseValidationErrors converts validation errors to a map
func (h *InventoryHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "gt":
				validationErrors[field] = "Value must be greater than 0"
			case "gte":
				validationErrors[field] = "Value must be greater than or equal to 0"
			case "oneof":
				validationErrors[field] = "Invalid value"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}

	return validationErrors
}

// BulkCreateInventoryItems creates multiple inventory items
func (h *InventoryHandler) BulkCreateInventoryItems(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	var req domain.BulkCreateInventoryItemsRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.BulkCreateInventoryItems(c.Context(), userID, pantryID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to bulk create inventory items", map[string]interface{}{
			"user_id":     userIDStr,
			"pantry_id":   pantryIDStr,
			"total_items": len(req.Items),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.bulk_items_created", pantryIDStr, map[string]interface{}{
		"user_id":       userIDStr,
		"pantry_id":     pantryIDStr,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return SuccessResponse(c, response, "Bulk inventory items created successfully")
}

// BulkUpdateInventoryItems updates multiple inventory items
func (h *InventoryHandler) BulkUpdateInventoryItems(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.BulkUpdateInventoryItemsRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.BulkUpdateInventoryItems(c.Context(), userID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to bulk update inventory items", map[string]interface{}{
			"user_id":     userIDStr,
			"total_items": len(req.Updates),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.bulk_items_updated", userIDStr, map[string]interface{}{
		"user_id":       userIDStr,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return SuccessResponse(c, response, "Bulk inventory items updated successfully")
}

// BulkConsumeInventoryItems consumes quantities from multiple inventory items
func (h *InventoryHandler) BulkConsumeInventoryItems(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.BulkConsumeInventoryRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.BulkConsumeInventoryItems(c.Context(), userID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to bulk consume inventory items", map[string]interface{}{
			"user_id":     userIDStr,
			"total_items": len(req.Consumptions),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.bulk_items_consumed", userIDStr, map[string]interface{}{
		"user_id":        userIDStr,
		"total_items":    response.TotalCount,
		"success_count":  response.SuccessCount,
		"failure_count":  response.FailureCount,
		"success_rate":   response.Summary.SuccessRate,
		"total_consumed": response.Summary.TotalQuantityConsumed,
	})

	return SuccessResponse(c, response, "Bulk inventory items consumed successfully")
}

// BulkDeleteInventoryItems deletes multiple inventory items
func (h *InventoryHandler) BulkDeleteInventoryItems(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.BulkDeleteInventoryItemsRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.BulkDeleteInventoryItems(c.Context(), userID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to bulk delete inventory items", map[string]interface{}{
			"user_id":     userIDStr,
			"total_items": len(req.ItemIDs),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.bulk_items_deleted", userIDStr, map[string]interface{}{
		"user_id":       userIDStr,
		"total_items":   response.TotalCount,
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"success_rate":  response.Summary.SuccessRate,
	})

	return SuccessResponse(c, response, "Bulk inventory items deleted successfully")
}

// ConsumeRecipeIngredients consumes ingredients for a recipe
func (h *InventoryHandler) ConsumeRecipeIngredients(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	var req domain.RecipeConsumptionRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.ConsumeRecipeIngredients(c.Context(), userID, pantryID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to consume recipe ingredients", map[string]interface{}{
			"user_id":           userIDStr,
			"pantry_id":         pantryIDStr,
			"recipe_id":         req.RecipeID,
			"recipe_name":       req.RecipeName,
			"total_ingredients": len(req.Ingredients),
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.recipe_ingredients_consumed", pantryIDStr, map[string]interface{}{
		"user_id":           userIDStr,
		"pantry_id":         pantryIDStr,
		"recipe_id":         req.RecipeID,
		"recipe_name":       req.RecipeName,
		"total_ingredients": response.TotalIngredients,
		"success_count":     response.SuccessCount,
		"partial_count":     response.PartialCount,
		"failure_count":     response.FailureCount,
		"completion_rate":   response.Summary.CompletionRate,
		"can_proceed":       response.CanProceed,
		"total_value":       response.Summary.TotalValue,
		"items_consumed":    response.Summary.TotalItemsConsumed,
	})

	return SuccessResponse(c, response, "Recipe ingredients consumed successfully")
}

// GenerateShoppingList generates a shopping list based on low stock and other criteria
func (h *InventoryHandler) GenerateShoppingList(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	var req domain.GenerateShoppingListRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.inventoryUsecase.GenerateShoppingList(c.Context(), userID, pantryID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to generate shopping list", map[string]interface{}{
			"user_id":              userIDStr,
			"pantry_id":            pantryIDStr,
			"include_low_stock":    req.IncludeLowStock,
			"include_empty":        req.IncludeEmpty,
			"include_expiring":     req.IncludeExpiringSoon,
			"use_consumption_data": req.UseConsumptionData,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("inventory.shopping_list_generated", pantryIDStr, map[string]interface{}{
		"user_id":              userIDStr,
		"pantry_id":            pantryIDStr,
		"total_items":          response.TotalItems,
		"low_stock_items":      response.LowStockItems,
		"empty_items":          response.EmptyItems,
		"expiring_items":       response.ExpiringItems,
		"recipe_items":         response.RecipeItems,
		"estimated_total":      response.Summary.EstimatedTotal,
		"high_priority_items":  response.Summary.HighPriorityItems,
		"include_low_stock":    req.IncludeLowStock,
		"include_empty":        req.IncludeEmpty,
		"include_expiring":     req.IncludeExpiringSoon,
		"use_consumption_data": req.UseConsumptionData,
	})

	return SuccessResponse(c, response, "Shopping list generated successfully")
}
