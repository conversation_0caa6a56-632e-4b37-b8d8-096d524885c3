package handler

import (
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// ExpirationHandler handles expiration tracking HTTP requests
type ExpirationHandler struct {
	expirationUsecase *usecases.ExpirationUsecase
	validator         *validator.Validate
	logger            *logger.Logger
}

// NewExpirationHandler creates a new expiration handler
func NewExpirationHandler(
	expirationUsecase *usecases.ExpirationUsecase,
	log *logger.Logger,
) *ExpirationHandler {
	return &ExpirationHandler{
		expirationUsecase: expirationUsecase,
		validator:         validator.New(),
		logger:            log,
	}
}

// TrackExpiringItems tracks expiring items in a pantry
//
//	@Summary		Track expiring items
//	@Description	Track and analyze expiring items in a pantry with configurable thresholds
//	@Tags			Expiration Tracking
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string								true	"Pantry ID"
//	@Param			request		body		domain.ExpirationTrackingRequest	true	"Expiration tracking configuration"
//	@Success		200			{object}	APIResponse							"Expiring items tracked successfully"
//	@Failure		400			{object}	APIResponse							"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden - no access to pantry"
//	@Failure		404			{object}	APIResponse							"Pantry not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/pantries/{pantryId}/expiration/track [post]
func (h *ExpirationHandler) TrackExpiringItems(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	pantryIDStr := c.Params("pantryId")
	pantryID, err := uuid.Parse(pantryIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
	}

	var req domain.ExpirationTrackingRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set default values if not provided
	if req.WarningDays == 0 {
		req.WarningDays = 7
	}
	if req.AlertDays == 0 {
		req.AlertDays = 3
	}
	if req.CriticalDays == 0 {
		req.CriticalDays = 1
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	response, err := h.expirationUsecase.TrackExpiringItems(c.Context(), userID, pantryID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to track expiring items", map[string]interface{}{
			"user_id":       userIDStr,
			"pantry_id":     pantryIDStr,
			"warning_days":  req.WarningDays,
			"alert_days":    req.AlertDays,
			"critical_days": req.CriticalDays,
			"send_alerts":   req.SendAlerts,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("expiration.items_tracked", pantryIDStr, map[string]interface{}{
		"user_id":        userIDStr,
		"pantry_id":      pantryIDStr,
		"total_items":    response.TotalItems,
		"warning_items":  response.WarningItems,
		"alert_items":    response.AlertItems,
		"critical_items": response.CriticalItems,
		"expired_items":  response.ExpiredItems,
		"alerts_sent":    response.AlertsSent,
		"total_value":    response.Summary.TotalValue,
		"warning_days":   req.WarningDays,
		"alert_days":     req.AlertDays,
		"critical_days":  req.CriticalDays,
	})

	return SuccessResponse(c, response, "Expiring items tracked successfully")
}

// ConfigureAlerts configures expiration alerts for a user/pantry
//
//	@Summary		Configure expiration alerts
//	@Description	Configure expiration alert settings for a pantry or globally
//	@Tags			Expiration Tracking
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string								false	"Pantry ID (optional for global config)"
//	@Param			request		body		domain.AlertConfigurationRequest	true	"Alert configuration"
//	@Success		200			{object}	APIResponse							"Alert configuration saved successfully"
//	@Failure		400			{object}	APIResponse							"Invalid input or validation error"
//	@Failure		401			{object}	APIResponse							"Unauthorized"
//	@Failure		403			{object}	APIResponse							"Forbidden - no access to pantry"
//	@Failure		404			{object}	APIResponse							"Pantry not found"
//	@Failure		500			{object}	APIResponse							"Internal server error"
//	@Router			/pantries/{pantryId}/expiration/alerts [post]
//	@Router			/expiration/alerts/global [post]
func (h *ExpirationHandler) ConfigureAlerts(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var pantryID *uuid.UUID
	pantryIDStr := c.Params("pantryId")
	if pantryIDStr != "" {
		id, err := uuid.Parse(pantryIDStr)
		if err != nil {
			return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
		}
		pantryID = &id
	}

	var req domain.AlertConfigurationRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Set default values if not provided
	if req.WarningDays == 0 {
		req.WarningDays = 7
	}
	if req.AlertDays == 0 {
		req.AlertDays = 3
	}
	if req.CriticalDays == 0 {
		req.CriticalDays = 1
	}

	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	config, err := h.expirationUsecase.ConfigureAlerts(c.Context(), userID, pantryID, &req)
	if err != nil {
		h.logger.LogError(err, "Failed to configure expiration alerts", map[string]interface{}{
			"user_id":       userIDStr,
			"pantry_id":     pantryIDStr,
			"enabled":       req.Enabled,
			"warning_days":  req.WarningDays,
			"alert_days":    req.AlertDays,
			"critical_days": req.CriticalDays,
			"channels":      req.Channels,
		})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("expiration.alerts_configured", config.ID.String(), map[string]interface{}{
		"user_id":       userIDStr,
		"pantry_id":     pantryIDStr,
		"config_id":     config.ID,
		"enabled":       config.Enabled,
		"warning_days":  config.WarningDays,
		"alert_days":    config.AlertDays,
		"critical_days": config.CriticalDays,
		"channels":      config.Channels,
	})

	return SuccessResponse(c, config, "Alert configuration saved successfully")
}

// GetAlertConfiguration retrieves alert configuration for a user/pantry
//
//	@Summary		Get alert configuration
//	@Description	Retrieve expiration alert configuration for a pantry or globally
//	@Tags			Expiration Tracking
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string		false	"Pantry ID (optional for global config)"
//	@Success		200			{object}	APIResponse	"Alert configuration retrieved successfully"
//	@Failure		400			{object}	APIResponse	"Invalid pantry ID"
//	@Failure		401			{object}	APIResponse	"Unauthorized"
//	@Failure		403			{object}	APIResponse	"Forbidden - no access to pantry"
//	@Failure		404			{object}	APIResponse	"Pantry not found"
//	@Failure		500			{object}	APIResponse	"Internal server error"
//	@Router			/pantries/{pantryId}/expiration/alerts [get]
//	@Router			/expiration/alerts/global [get]
func (h *ExpirationHandler) GetAlertConfiguration(c *fiber.Ctx) error {
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var pantryID *uuid.UUID
	pantryIDStr := c.Params("pantryId")
	if pantryIDStr != "" {
		id, err := uuid.Parse(pantryIDStr)
		if err != nil {
			return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid pantry ID"))
		}
		pantryID = &id
	}

	config, err := h.expirationUsecase.GetAlertConfiguration(c.Context(), userID, pantryID)
	if err != nil {
		if appErr, ok := err.(*errors.AppError); ok && appErr.Code == errors.ErrCodeNotFound {
			// Return default configuration if none exists
			defaultConfig := &domain.AlertConfiguration{
				ID:           uuid.New(),
				UserID:       userID,
				PantryID:     pantryID,
				Enabled:      false,
				WarningDays:  7,
				AlertDays:    3,
				CriticalDays: 1,
				Channels:     []domain.NotificationChannel{},
			}
			return SuccessResponse(c, defaultConfig, "Default alert configuration")
		}

		h.logger.LogError(err, "Failed to get alert configuration", map[string]interface{}{
			"user_id":   userIDStr,
			"pantry_id": pantryIDStr,
		})
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, config, "Alert configuration retrieved successfully")
}

// ProcessScheduledAlerts processes scheduled expiration alerts (admin endpoint)
func (h *ExpirationHandler) ProcessScheduledAlerts(c *fiber.Ctx) error {
	// TODO: Add admin authentication check

	err := h.expirationUsecase.ProcessScheduledAlerts(c.Context())
	if err != nil {
		h.logger.LogError(err, "Failed to process scheduled alerts", map[string]interface{}{})
		return ErrorResponse(c, err)
	}

	// Log business event
	h.logger.LogBusinessEvent("expiration.scheduled_alerts_processed", "system", map[string]interface{}{
		"processed_at": c.Context().Value("request_time"),
	})

	return SuccessResponse(c, map[string]interface{}{
		"processed_at": c.Context().Value("request_time"),
	}, "Scheduled alerts processed successfully")
}

// parseValidationErrors parses validation errors into a map
func (h *ExpirationHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validatorErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validatorErrors {
			field := e.Field()
			tag := e.Tag()

			switch tag {
			case "required":
				validationErrors[field] = field + " is required"
			case "min":
				validationErrors[field] = field + " must be at least " + e.Param()
			case "max":
				validationErrors[field] = field + " must be at most " + e.Param()
			case "gte":
				validationErrors[field] = field + " must be greater than or equal to " + e.Param()
			case "lte":
				validationErrors[field] = field + " must be less than or equal to " + e.Param()
			default:
				validationErrors[field] = field + " is invalid"
			}
		}
	}

	return validationErrors
}
