package handler

import (
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// StoreHandler handles store-related HTTP requests
type StoreHandler struct {
	storeUsecase *usecases.StoreUsecase
}

// NewStoreHandler creates a new StoreHandler
func NewStoreHandler(storeUsecase *usecases.StoreUsecase) *StoreHandler {
	return &StoreHandler{
		storeUsecase: storeUsecase,
	}
}

// RegisterRoutes registers the store routes
func (h *StoreHandler) RegisterRoutes(router fiber.Router) {
	stores := router.Group("/stores")
	{
		stores.Post("/", h.CreateStore)
		stores.Get("/", h.ListStores)
		stores.Get("/search", h.SearchStores)
		stores.Get("/:id", h.GetStoreByID)
		stores.Put("/:id", h.UpdateStore)
		stores.Delete("/:id", h.DeleteStore)
	}
}

// @Summary Create a new store
// @Description Creates a new store with the given details
// @Tags stores
// @Accept json
// @Produce json
// @Param body body usecases.CreateStoreRequest true "Store details"
// @Success 201 {object} APIResponse{data=domain.Store}
// @Failure 400 {object} APIResponse
// @Failure 500 {object} APIResponse
// @Router /stores [post]
func (h *StoreHandler) CreateStore(c *fiber.Ctx) error {
	var req usecases.CreateStoreRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	store, err := h.storeUsecase.CreateStore(c.Context(), &req)
	if err != nil {
		return ErrorResponse(c, err)
	}

	return CreatedResponse(c, store, "Store created successfully")
}

// @Summary Get all stores
// @Description Get a paginated list of stores
// @Tags stores
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Results per page (default: 10)"
// @Success 200 {object} PaginatedResponse{data=[]domain.Store}
// @Failure 500 {object} APIResponse
// @Router /stores [get]
func (h *StoreHandler) ListStores(c *fiber.Ctx) error {
	page, limit := ParsePaginationParams(c)

	stores, total, err := h.storeUsecase.ListStores(c.Context(), page, limit)
	if err != nil {
		return ErrorResponse(c, err)
	}

	pagination := CalculatePagination(page, limit, total)
	return PaginatedSuccessResponse(c, stores, pagination, "Stores retrieved successfully")
}

// @Summary Get a store by ID
// @Description Get a store's details by its ID
// @Tags stores
// @Produce json
// @Param id path string true "Store ID"
// @Success 200 {object} APIResponse{data=domain.Store}
// @Failure 400 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /stores/{id} [get]
func (h *StoreHandler) GetStoreByID(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return ErrorResponse(c, errors.NewValidationError("Invalid store ID", nil))
	}

	store, err := h.storeUsecase.GetStore(c.Context(), id)
	if err != nil {
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, store, "Store retrieved successfully")
}

// @Summary Update a store
// @Description Update a store's details
// @Tags stores
// @Accept json
// @Produce json
// @Param id path string true "Store ID"
// @Param body body usecases.UpdateStoreRequest true "Store details"
// @Success 200 {object} APIResponse{data=domain.Store}
// @Failure 400 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /stores/{id} [put]
func (h *StoreHandler) UpdateStore(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return ErrorResponse(c, errors.NewValidationError("Invalid store ID", nil))
	}

	var req usecases.UpdateStoreRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	store, err := h.storeUsecase.UpdateStore(c.Context(), id, &req)
	if err != nil {
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, store, "Store updated successfully")
}

// @Summary Delete a store
// @Description Soft delete a store by its ID
// @Tags stores
// @Param id path string true "Store ID"
// @Success 204 "No Content"
// @Failure 400 {object} APIResponse
// @Failure 404 {object} APIResponse
// @Router /stores/{id} [delete]
func (h *StoreHandler) DeleteStore(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return ErrorResponse(c, errors.NewValidationError("Invalid store ID", nil))
	}

	if err := h.storeUsecase.DeleteStore(c.Context(), id); err != nil {
		return ErrorResponse(c, err)
	}

	return NoContentResponse(c)
}

// @Summary Search stores
// @Description Search for stores by name
// @Tags stores
// @Produce json
// @Param query query string true "Search query"
// @Success 200 {object} APIResponse{data=[]domain.Store}
// @Failure 400 {object} APIResponse
// @Failure 500 {object} APIResponse
// @Router /stores/search [get]
func (h *StoreHandler) SearchStores(c *fiber.Ctx) error {
	query := c.Query("query")
	if query == "" {
		return ErrorResponse(c, errors.NewValidationError("Search query is required", nil))
	}

	stores, err := h.storeUsecase.SearchStores(c.Context(), query)
	if err != nil {
		return ErrorResponse(c, err)
	}

	return SuccessResponse(c, stores, "Stores found successfully")
}
