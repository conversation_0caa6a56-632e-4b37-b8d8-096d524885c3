package handler

import (
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// ProductVariantHandler handles product variant-related HTTP requests
type ProductVariantHandler struct {
	variantRepo domain.ProductVariantRepository
	productRepo domain.ProductRepository
	unitRepo    domain.UnitOfMeasureRepository
	logger      *logger.Logger
	validator   *validator.Validate
}

// NewProductVariantHandler creates a new product variant handler
func NewProductVariantHandler(
	variantRepo domain.ProductVariantRepository,
	productRepo domain.ProductRepository,
	unitRepo domain.UnitOfMeasureRepository,
	log *logger.Logger,
) *ProductVariantHandler {
	return &ProductVariantHandler{
		variantRepo: variantRepo,
		productRepo: productRepo,
		unitRepo:    unitRepo,
		logger:      log,
		validator:   validator.New(),
	}
}

// CreateProductVariant creates a new product variant
func (h *ProductVariantHandler) CreateProductVariant(c *fiber.Ctx) error {
	productIDStr := c.Params("productId")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid product ID"))
	}
	
	var req domain.CreateProductVariantRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Validate product exists
	_, err = h.productRepo.GetByID(productID)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "Product not found"))
		}
		h.logger.LogError(err, "Failed to validate product", map[string]interface{}{
			"product_id": productIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	// Validate unit of measure if provided
	if req.DefaultUnitOfMeasureID != nil {
		_, err := h.unitRepo.GetByID(*req.DefaultUnitOfMeasureID)
		if err != nil {
			if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
				return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "Unit of measure not found"))
			}
			h.logger.LogError(err, "Failed to validate unit of measure", map[string]interface{}{
				"unit_id": req.DefaultUnitOfMeasureID,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
	}
	
	// Validate barcode if provided
	if req.BarcodeGTIN != nil {
		if err := domain.ValidateBarcodeGTIN(*req.BarcodeGTIN); err != nil {
			return ErrorResponse(c, err)
		}
		
		// Check if barcode already exists
		exists, err := h.variantRepo.ExistsByBarcode(*req.BarcodeGTIN)
		if err != nil {
			h.logger.LogError(err, "Failed to check barcode existence", map[string]interface{}{
				"barcode": req.BarcodeGTIN,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Product variant with this barcode already exists"))
		}
	}
	
	// Check if variant name already exists for this product
	exists, err := h.variantRepo.ExistsByProductAndName(productID, req.Name)
	if err != nil {
		h.logger.LogError(err, "Failed to check variant name existence", map[string]interface{}{
			"product_id": productIDStr,
			"name":       req.Name,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Product variant with this name already exists for this product"))
	}
	
	// Create product variant
	variant := domain.NewProductVariant(
		productID,
		req.Name,
		req.Description,
		req.BarcodeGTIN,
		req.ImageURL,
		req.PackagingType,
		req.DefaultUnitOfMeasureID,
		nil)
	
	if err := h.variantRepo.Create(variant); err != nil {
		h.logger.LogError(err, "Failed to create product variant", map[string]interface{}{
			"product_id": productIDStr,
			"name":       req.Name,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("product_variant.created", variant.ID.String(), map[string]interface{}{
		"product_id":     variant.ProductID,
		"name":           variant.Name,
		"packaging_type": variant.PackagingType,
		"has_barcode":    variant.HasBarcode(),
	})
	
	// Convert to response
	response := h.variantToResponse(variant)
	
	return CreatedResponse(c, response, "Product variant created successfully")
}

// GetProductVariants retrieves variants for a product
func (h *ProductVariantHandler) GetProductVariants(c *fiber.Ctx) error {
	productIDStr := c.Params("productId")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid product ID"))
	}
	
	// Get variants
	variants, err := h.variantRepo.GetByProductID(productID)
	if err != nil {
		h.logger.LogError(err, "Failed to get product variants", map[string]interface{}{
			"product_id": productIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.ProductVariantResponse, len(variants))
	for i, variant := range variants {
		responses[i] = h.variantToResponse(variant)
	}
	
	return SuccessResponse(c, responses, "Product variants retrieved successfully")
}

// GetProductVariant retrieves a specific product variant
func (h *ProductVariantHandler) GetProductVariant(c *fiber.Ctx) error {
	variantIDStr := c.Params("variantId")
	variantID, err := uuid.Parse(variantIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid variant ID"))
	}
	
	// Get variant
	variant, err := h.variantRepo.GetByID(variantID)
	if err != nil {
		h.logger.LogError(err, "Failed to get product variant", map[string]interface{}{
			"variant_id": variantIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to response
	response := h.variantToResponse(variant)
	
	return SuccessResponse(c, response, "Product variant retrieved successfully")
}

// SearchProductVariants searches for product variants
func (h *ProductVariantHandler) SearchProductVariants(c *fiber.Ctx) error {
	// Parse query parameters
	query := c.Query("query", "")
	productIDStr := c.Query("product_id", "")
	pageStr := c.Query("page", "1")
	limitStr := c.Query("limit", "20")
	
	// Parse pagination
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}
	
	// Parse product ID if provided
	var productID *uuid.UUID
	if productIDStr != "" {
		parsedProductID, err := uuid.Parse(productIDStr)
		if err != nil {
			return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid product ID"))
		}
		productID = &parsedProductID
	}
	
	// Search variants
	variants, total, err := h.variantRepo.SearchVariants(query, productID, page, limit)
	if err != nil {
		h.logger.LogError(err, "Failed to search product variants", map[string]interface{}{
			"query":      query,
			"product_id": productID,
			"page":       page,
			"limit":      limit,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.ProductVariantResponse, len(variants))
	for i, variant := range variants {
		responses[i] = h.variantToResponse(variant)
	}
	
	// Create pagination metadata
	totalPages := (total + int64(limit) - 1) / int64(limit)
	metadata := map[string]interface{}{
		"page":        page,
		"limit":       limit,
		"total":       total,
		"total_pages": totalPages,
		"has_next":    page < int(totalPages),
		"has_prev":    page > 1,
	}
	
	return SuccessResponseWithMetadata(c, responses, "Product variants retrieved successfully", metadata)
}

// GetProductVariantByBarcode retrieves a product variant by barcode
func (h *ProductVariantHandler) GetProductVariantByBarcode(c *fiber.Ctx) error {
	barcode := c.Params("barcode")
	
	// Validate barcode format
	if err := domain.ValidateBarcodeGTIN(barcode); err != nil {
		return ErrorResponse(c, err)
	}
	
	// Get variant by barcode
	variant, err := h.variantRepo.GetByBarcode(barcode)
	if err != nil {
		h.logger.LogError(err, "Failed to get product variant by barcode", map[string]interface{}{
			"barcode": barcode,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to response
	response := h.variantToResponse(variant)
	
	return SuccessResponse(c, response, "Product variant retrieved successfully")
}

// UpdateProductVariant updates a product variant
func (h *ProductVariantHandler) UpdateProductVariant(c *fiber.Ctx) error {
	variantIDStr := c.Params("variantId")
	variantID, err := uuid.Parse(variantIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid variant ID"))
	}
	
	var req domain.UpdateProductVariantRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Get current variant
	variant, err := h.variantRepo.GetByID(variantID)
	if err != nil {
		h.logger.LogError(err, "Failed to get variant for update", map[string]interface{}{
			"variant_id": variantIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Validate unit of measure if provided
	if req.DefaultUnitOfMeasureID != nil {
		_, err := h.unitRepo.GetByID(*req.DefaultUnitOfMeasureID)
		if err != nil {
			if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
				return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "Unit of measure not found"))
			}
			h.logger.LogError(err, "Failed to validate unit of measure", map[string]interface{}{
				"unit_id": req.DefaultUnitOfMeasureID,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
	}
	
	// Check if new name conflicts (if name changed)
	if variant.Name != req.Name {
		exists, err := h.variantRepo.ExistsByProductAndName(variant.ProductID, req.Name)
		if err != nil {
			h.logger.LogError(err, "Failed to check variant name existence", map[string]interface{}{
				"variant_id": variantIDStr,
				"name":       req.Name,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Product variant with this name already exists for this product"))
		}
	}
	
	// Update variant
	variant.UpdateDetails(req.Name, req.Description, req.ImageURL, req.PackagingType, req.DefaultUnitOfMeasureID)
	if err := h.variantRepo.Update(variant); err != nil {
		h.logger.LogError(err, "Failed to update product variant", map[string]interface{}{
			"variant_id": variantIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("product_variant.updated", variant.ID.String(), map[string]interface{}{
		"name":           variant.Name,
		"packaging_type": variant.PackagingType,
	})
	
	// Convert to response
	response := h.variantToResponse(variant)
	
	return SuccessResponse(c, response, "Product variant updated successfully")
}

// UpdateProductVariantBarcode updates a product variant's barcode
func (h *ProductVariantHandler) UpdateProductVariantBarcode(c *fiber.Ctx) error {
	variantIDStr := c.Params("variantId")
	variantID, err := uuid.Parse(variantIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid variant ID"))
	}
	
	var req domain.UpdateVariantBarcodeRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Get variant
	variant, err := h.variantRepo.GetByID(variantID)
	if err != nil {
		h.logger.LogError(err, "Failed to get variant for barcode update", map[string]interface{}{
			"variant_id": variantIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Validate barcode if provided
	if req.BarcodeGTIN != nil {
		if err := domain.ValidateBarcodeGTIN(*req.BarcodeGTIN); err != nil {
			return ErrorResponse(c, err)
		}
		
		// Check if barcode already exists (if different from current)
		if variant.BarcodeGTIN == nil || *variant.BarcodeGTIN != *req.BarcodeGTIN {
			exists, err := h.variantRepo.ExistsByBarcode(*req.BarcodeGTIN)
			if err != nil {
				h.logger.LogError(err, "Failed to check barcode existence", map[string]interface{}{
					"barcode": req.BarcodeGTIN,
				})
				return ErrorResponse(c, errors.ErrInternalError)
			}
			
			if exists {
				return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Product variant with this barcode already exists"))
			}
		}
	}
	
	// Update barcode
	variant.UpdateBarcode(req.BarcodeGTIN)
	if err := h.variantRepo.Update(variant); err != nil {
		h.logger.LogError(err, "Failed to update variant barcode", map[string]interface{}{
			"variant_id": variantIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("product_variant.barcode_updated", variant.ID.String(), map[string]interface{}{
		"barcode": req.BarcodeGTIN,
	})
	
	// Convert to response
	response := h.variantToResponse(variant)
	
	return SuccessResponse(c, response, "Product variant barcode updated successfully")
}

// DeleteProductVariant deletes a product variant
func (h *ProductVariantHandler) DeleteProductVariant(c *fiber.Ctx) error {
	variantIDStr := c.Params("variantId")
	variantID, err := uuid.Parse(variantIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid variant ID"))
	}
	
	// Get variant to log details
	variant, err := h.variantRepo.GetByID(variantID)
	if err != nil {
		h.logger.LogError(err, "Failed to get variant for deletion", map[string]interface{}{
			"variant_id": variantIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Delete variant
	if err := h.variantRepo.Delete(variantID); err != nil {
		h.logger.LogError(err, "Failed to delete product variant", map[string]interface{}{
			"variant_id": variantIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("product_variant.deleted", variantIDStr, map[string]interface{}{
		"name":        variant.Name,
		"product_id":  variant.ProductID,
		"has_barcode": variant.HasBarcode(),
	})
	
	return SuccessResponse(c, nil, "Product variant deleted successfully")
}

// Helper methods

func (h *ProductVariantHandler) variantToResponse(variant *domain.ProductVariant) domain.ProductVariantResponse {
	return domain.ProductVariantResponse{
		ID:                     variant.ID,
		ProductID:              variant.ProductID,
		Name:                   variant.Name,
		Description:            variant.Description,
		BarcodeGTIN:            variant.BarcodeGTIN,
		ImageURL:               variant.ImageURL,
		PackagingType:          variant.PackagingType,
		DefaultUnitOfMeasureID: variant.DefaultUnitOfMeasureID,
		CreatedAt:              variant.CreatedAt.Format(time.RFC3339),
		UpdatedAt:              variant.UpdatedAt.Format(time.RFC3339),
	}
}

func (h *ProductVariantHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)
	
	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "url":
				validationErrors[field] = "Must be a valid URL"
			case "oneof":
				validationErrors[field] = "Invalid value"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}
	
	return validationErrors
}
