package handler

import (
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// UnitOfMeasureHandler handles unit of measure-related HTTP requests
type UnitOfMeasureHandler struct {
	unitRepo  domain.UnitOfMeasureRepository
	logger    *logger.Logger
	validator *validator.Validate
}

// NewUnitOfMeasureHandler creates a new unit of measure handler
func NewUnitOfMeasureHandler(
	unitRepo domain.UnitOfMeasureRepository,
	log *logger.Logger,
) *UnitOfMeasureHandler {
	return &UnitOfMeasureHandler{
		unitRepo:  unitRepo,
		logger:    log,
		validator: validator.New(),
	}
}

// CreateUnit creates a new base unit of measure
func (h *UnitOfMeasureHandler) CreateUnit(c *fiber.Ctx) error {
	var req domain.CreateUnitOfMeasureRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Check if unit name already exists
	exists, err := h.unitRepo.ExistsByName(req.Name)
	if err != nil {
		h.logger.LogError(err, "Failed to check unit name existence", map[string]interface{}{
			"name": req.Name,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Unit with this name already exists"))
	}
	
	// Check if unit symbol already exists
	exists, err = h.unitRepo.ExistsBySymbol(req.Symbol)
	if err != nil {
		h.logger.LogError(err, "Failed to check unit symbol existence", map[string]interface{}{
			"symbol": req.Symbol,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Unit with this symbol already exists"))
	}
	
	// Create unit
	unit := domain.NewUnitOfMeasure(req.Name, req.Symbol, req.Type, req.Description)
	if err := h.unitRepo.Create(unit); err != nil {
		h.logger.LogError(err, "Failed to create unit", map[string]interface{}{
			"name":   req.Name,
			"symbol": req.Symbol,
			"type":   req.Type,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("unit_of_measure.created", unit.ID.String(), map[string]interface{}{
		"name":   unit.Name,
		"symbol": unit.Symbol,
		"type":   unit.Type,
	})
	
	// Convert to response
	response := h.unitToResponse(unit)
	
	return CreatedResponse(c, response, "Unit of measure created successfully")
}

// CreateDerivedUnit creates a new derived unit of measure
func (h *UnitOfMeasureHandler) CreateDerivedUnit(c *fiber.Ctx) error {
	var req domain.CreateDerivedUnitRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Check if unit name already exists
	exists, err := h.unitRepo.ExistsByName(req.Name)
	if err != nil {
		h.logger.LogError(err, "Failed to check unit name existence", map[string]interface{}{
			"name": req.Name,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Unit with this name already exists"))
	}
	
	// Check if unit symbol already exists
	exists, err = h.unitRepo.ExistsBySymbol(req.Symbol)
	if err != nil {
		h.logger.LogError(err, "Failed to check unit symbol existence", map[string]interface{}{
			"symbol": req.Symbol,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Unit with this symbol already exists"))
	}
	
	// Validate base unit exists
	baseUnit, err := h.unitRepo.GetByID(req.BaseUnitID)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return ErrorResponse(c, errors.New(errors.ErrCodeNotFound, "Base unit not found"))
		}
		h.logger.LogError(err, "Failed to validate base unit", map[string]interface{}{
			"base_unit_id": req.BaseUnitID,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}
	
	// Validate types match
	if baseUnit.Type != req.Type {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Derived unit type must match base unit type"))
	}
	
	// Create derived unit
	unit := domain.NewDerivedUnitOfMeasure(req.Name, req.Symbol, req.Type, req.Description, req.BaseUnitID, req.ConversionFactor)
	if err := h.unitRepo.Create(unit); err != nil {
		h.logger.LogError(err, "Failed to create derived unit", map[string]interface{}{
			"name":              req.Name,
			"symbol":            req.Symbol,
			"type":              req.Type,
			"base_unit_id":      req.BaseUnitID,
			"conversion_factor": req.ConversionFactor,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("unit_of_measure.created", unit.ID.String(), map[string]interface{}{
		"name":              unit.Name,
		"symbol":            unit.Symbol,
		"type":              unit.Type,
		"is_base_unit":      unit.IsBaseUnit,
		"base_unit_id":      unit.BaseUnitID,
		"conversion_factor": unit.ConversionFactor,
	})
	
	// Convert to response
	response := h.unitToResponse(unit)
	
	return CreatedResponse(c, response, "Derived unit of measure created successfully")
}

// GetUnits retrieves units of measure with optional filtering
func (h *UnitOfMeasureHandler) GetUnits(c *fiber.Ctx) error {
	// Check query parameters
	unitType := c.Query("type")
	baseUnitsOnly := c.QueryBool("base_units_only", false)
	
	var units []*domain.UnitOfMeasure
	var err error
	
	if baseUnitsOnly {
		units, err = h.unitRepo.GetBaseUnits()
	} else if unitType != "" {
		units, err = h.unitRepo.GetByType(domain.UnitOfMeasureType(unitType))
	} else {
		units, err = h.unitRepo.GetAllUnits()
	}
	
	if err != nil {
		h.logger.LogError(err, "Failed to get units", map[string]interface{}{
			"type":             unitType,
			"base_units_only":  baseUnitsOnly,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.UnitOfMeasureResponse, len(units))
	for i, unit := range units {
		responses[i] = h.unitToResponse(unit)
	}
	
	return SuccessResponse(c, responses, "Units of measure retrieved successfully")
}

// GetUnit retrieves a specific unit of measure
func (h *UnitOfMeasureHandler) GetUnit(c *fiber.Ctx) error {
	unitIDStr := c.Params("unitId")
	unitID, err := uuid.Parse(unitIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid unit ID"))
	}
	
	// Get unit
	unit, err := h.unitRepo.GetByID(unitID)
	if err != nil {
		h.logger.LogError(err, "Failed to get unit", map[string]interface{}{
			"unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to response
	response := h.unitToResponse(unit)
	
	return SuccessResponse(c, response, "Unit of measure retrieved successfully")
}

// GetDerivedUnits retrieves derived units for a base unit
func (h *UnitOfMeasureHandler) GetDerivedUnits(c *fiber.Ctx) error {
	unitIDStr := c.Params("unitId")
	unitID, err := uuid.Parse(unitIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid unit ID"))
	}
	
	// Get derived units
	derivedUnits, err := h.unitRepo.GetDerivedUnits(unitID)
	if err != nil {
		h.logger.LogError(err, "Failed to get derived units", map[string]interface{}{
			"base_unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Convert to responses
	responses := make([]domain.UnitOfMeasureResponse, len(derivedUnits))
	for i, unit := range derivedUnits {
		responses[i] = h.unitToResponse(unit)
	}
	
	return SuccessResponse(c, responses, "Derived units retrieved successfully")
}

// UpdateUnit updates a unit of measure
func (h *UnitOfMeasureHandler) UpdateUnit(c *fiber.Ctx) error {
	unitIDStr := c.Params("unitId")
	unitID, err := uuid.Parse(unitIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid unit ID"))
	}
	
	var req domain.UpdateUnitOfMeasureRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Get current unit
	unit, err := h.unitRepo.GetByID(unitID)
	if err != nil {
		h.logger.LogError(err, "Failed to get unit for update", map[string]interface{}{
			"unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Check if new name conflicts (if name changed)
	if unit.Name != req.Name {
		exists, err := h.unitRepo.ExistsByName(req.Name)
		if err != nil {
			h.logger.LogError(err, "Failed to check unit name existence", map[string]interface{}{
				"unit_id": unitIDStr,
				"name":    req.Name,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Unit with this name already exists"))
		}
	}
	
	// Check if new symbol conflicts (if symbol changed)
	if unit.Symbol != req.Symbol {
		exists, err := h.unitRepo.ExistsBySymbol(req.Symbol)
		if err != nil {
			h.logger.LogError(err, "Failed to check unit symbol existence", map[string]interface{}{
				"unit_id": unitIDStr,
				"symbol":  req.Symbol,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if exists {
			return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Unit with this symbol already exists"))
		}
	}
	
	// Update unit
	unit.UpdateDetails(req.Name, req.Symbol, req.Description)
	if err := h.unitRepo.Update(unit); err != nil {
		h.logger.LogError(err, "Failed to update unit", map[string]interface{}{
			"unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("unit_of_measure.updated", unit.ID.String(), map[string]interface{}{
		"name":   unit.Name,
		"symbol": unit.Symbol,
	})
	
	// Convert to response
	response := h.unitToResponse(unit)
	
	return SuccessResponse(c, response, "Unit of measure updated successfully")
}

// UpdateConversionFactor updates the conversion factor for a derived unit
func (h *UnitOfMeasureHandler) UpdateConversionFactor(c *fiber.Ctx) error {
	unitIDStr := c.Params("unitId")
	unitID, err := uuid.Parse(unitIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid unit ID"))
	}
	
	var req domain.UpdateConversionFactorRequest
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}
	
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}
	
	// Get unit
	unit, err := h.unitRepo.GetByID(unitID)
	if err != nil {
		h.logger.LogError(err, "Failed to get unit for conversion factor update", map[string]interface{}{
			"unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Update conversion factor
	if err := unit.UpdateConversionFactor(req.ConversionFactor); err != nil {
		return ErrorResponse(c, err)
	}
	
	if err := h.unitRepo.Update(unit); err != nil {
		h.logger.LogError(err, "Failed to update unit conversion factor", map[string]interface{}{
			"unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("unit_of_measure.conversion_updated", unit.ID.String(), map[string]interface{}{
		"conversion_factor": req.ConversionFactor,
	})
	
	// Convert to response
	response := h.unitToResponse(unit)
	
	return SuccessResponse(c, response, "Conversion factor updated successfully")
}

// DeleteUnit deletes a unit of measure
func (h *UnitOfMeasureHandler) DeleteUnit(c *fiber.Ctx) error {
	unitIDStr := c.Params("unitId")
	unitID, err := uuid.Parse(unitIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid unit ID"))
	}
	
	// Get unit to log details
	unit, err := h.unitRepo.GetByID(unitID)
	if err != nil {
		h.logger.LogError(err, "Failed to get unit for deletion", map[string]interface{}{
			"unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Check if unit has derived units (for base units)
	if unit.IsBaseUnit {
		derivedUnits, err := h.unitRepo.GetDerivedUnits(unitID)
		if err != nil {
			h.logger.LogError(err, "Failed to check derived units", map[string]interface{}{
				"unit_id": unitIDStr,
			})
			return ErrorResponse(c, errors.ErrInternalError)
		}
		
		if len(derivedUnits) > 0 {
			return ErrorResponse(c, errors.New(errors.ErrCodeConflict, "Cannot delete base unit with derived units"))
		}
	}
	
	// Delete unit
	if err := h.unitRepo.Delete(unitID); err != nil {
		h.logger.LogError(err, "Failed to delete unit", map[string]interface{}{
			"unit_id": unitIDStr,
		})
		return ErrorResponse(c, err)
	}
	
	// Log business event
	h.logger.LogBusinessEvent("unit_of_measure.deleted", unitIDStr, map[string]interface{}{
		"name":   unit.Name,
		"symbol": unit.Symbol,
	})
	
	return SuccessResponse(c, nil, "Unit of measure deleted successfully")
}

// Helper methods

func (h *UnitOfMeasureHandler) unitToResponse(unit *domain.UnitOfMeasure) domain.UnitOfMeasureResponse {
	return domain.UnitOfMeasureResponse{
		ID:               unit.ID,
		Name:             unit.Name,
		Symbol:           unit.Symbol,
		Type:             unit.Type,
		Description:      unit.Description,
		IsBaseUnit:       unit.IsBaseUnit,
		BaseUnitID:       unit.BaseUnitID,
		ConversionFactor: unit.ConversionFactor,
		CreatedAt:        unit.CreatedAt.Format(time.RFC3339),
		UpdatedAt:        unit.UpdatedAt.Format(time.RFC3339),
	}
}

func (h *UnitOfMeasureHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)
	
	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "gt":
				validationErrors[field] = "Value must be greater than 0"
			case "oneof":
				validationErrors[field] = "Invalid value"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}
	
	return validationErrors
}
