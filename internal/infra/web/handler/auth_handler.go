package handler

import (
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	authService domain.AuthService
	userRepo    domain.UserRepository
	logger      *logger.Logger
	validator   *validator.Validate
}

// NewAuthHandler creates a new authentication handler
func <PERSON>uthHandler(authService domain.AuthService, userRepo domain.UserRepository, log *logger.Logger) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		userRepo:    userRepo,
		logger:      log,
		validator:   validator.New(),
	}
}

// Register handles user registration
//
//	@Summary		Register a new user
//	@Description	Create a new user account with email, username, and password
//	@Tags			Authentication
//	@Accept			json
//	@Produce		json
//	@Param			request	body		domain.RegisterCredentials	true	"Registration credentials"
//	@Success		201		{object}	APIResponse				"User registered successfully"
//	@Failure		400		{object}	APIResponse				"Invalid input or validation error"
//	@Failure		409		{object}	APIResponse				"Email or username already exists"
//	@Failure		500		{object}	APIResponse				"Internal server error"
//	@Router			/auth/register [post]
func (h *AuthHandler) Register(c *fiber.Ctx) error {
	var req domain.RegisterCredentials

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Check if user already exists
	if exists, err := h.userRepo.ExistsByEmail(req.Email); err != nil {
		h.logger.LogError(err, "Failed to check email existence", nil)
		return ErrorResponse(c, errors.ErrInternalError)
	} else if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Email already registered"))
	}

	if exists, err := h.userRepo.ExistsByUsername(req.Username); err != nil {
		h.logger.LogError(err, "Failed to check username existence", nil)
		return ErrorResponse(c, errors.ErrInternalError)
	} else if exists {
		return ErrorResponse(c, errors.New(errors.ErrCodeAlreadyExists, "Username already taken"))
	}

	// Hash password
	passwordHash, err := h.authService.HashPassword(req.Password)
	if err != nil {
		h.logger.LogError(err, "Failed to hash password", nil)
		return ErrorResponse(c, errors.ErrInternalError)
	}

	// Create user
	user := domain.NewUser(req.Username, req.Email, passwordHash)
	if req.FirstName != nil {
		user.FirstName = req.FirstName
	}
	if req.LastName != nil {
		user.LastName = req.LastName
	}

	if err := h.userRepo.Create(user); err != nil {
		h.logger.LogError(err, "Failed to create user", map[string]interface{}{
			"email":    req.Email,
			"username": req.Username,
		})
		return ErrorResponse(c, err)
	}

	// Generate tokens
	tokens, err := h.authService.GenerateTokens(user.ID)
	if err != nil {
		h.logger.LogError(err, "Failed to generate tokens", map[string]interface{}{
			"user_id": user.ID.String(),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	// Set refresh token cookie
	h.setRefreshTokenCookie(c, tokens.RefreshToken, tokens.RefreshTokenExpiresAt)

	// Log successful registration
	h.logger.LogBusinessEvent("user.registered", user.ID.String(), map[string]interface{}{
		"email":    user.Email,
		"username": user.Username,
	})

	// Return response
	response := map[string]interface{}{
		"user": map[string]interface{}{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"first_name": user.FirstName,
			"last_name":  user.LastName,
		},
		"access_token": tokens.AccessToken,
		"token_type":   tokens.TokenType,
		"expires_at":   tokens.AccessTokenExpiresAt,
	}

	return CreatedResponse(c, response, "User registered successfully")
}

// Login handles user login
//
//	@Summary		User login
//	@Description	Authenticate user with email and password
//	@Tags			Authentication
//	@Accept			json
//	@Produce		json
//	@Param			request	body		domain.LoginCredentials	true	"Login credentials"
//	@Success		200		{object}	APIResponse			"Login successful"
//	@Failure		400		{object}	APIResponse			"Invalid input or validation error"
//	@Failure		401		{object}	APIResponse			"Invalid credentials"
//	@Failure		500		{object}	APIResponse			"Internal server error"
//	@Router			/auth/login [post]
func (h *AuthHandler) Login(c *fiber.Ctx) error {
	var req domain.LoginCredentials

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get user by email
	user, err := h.userRepo.GetByEmail(req.Email)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return ErrorResponse(c, errors.ErrInvalidCredentials)
		}
		h.logger.LogError(err, "Failed to get user by email", map[string]interface{}{
			"email": req.Email,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	// Verify password
	if err := h.authService.VerifyPassword(req.Password, user.PasswordHash); err != nil {
		h.logger.LogSecurityEvent("invalid_login_attempt", user.ID.String(), map[string]interface{}{
			"email":     req.Email,
			"client_ip": c.IP(),
		})
		return ErrorResponse(c, errors.ErrInvalidCredentials)
	}

	// Generate tokens
	tokens, err := h.authService.GenerateTokens(user.ID)
	if err != nil {
		h.logger.LogError(err, "Failed to generate tokens", map[string]interface{}{
			"user_id": user.ID.String(),
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	// Set refresh token cookie
	h.setRefreshTokenCookie(c, tokens.RefreshToken, tokens.RefreshTokenExpiresAt)

	// Log successful login
	h.logger.LogBusinessEvent("user.logged_in", user.ID.String(), map[string]interface{}{
		"email":      user.Email,
		"client_ip":  c.IP(),
		"user_agent": c.Get("User-Agent"),
	})

	// Return response
	response := map[string]interface{}{
		"user": map[string]interface{}{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"first_name": user.FirstName,
			"last_name":  user.LastName,
		},
		"access_token": tokens.AccessToken,
		"token_type":   tokens.TokenType,
		"expires_at":   tokens.AccessTokenExpiresAt,
	}

	return SuccessResponse(c, response, "Login successful")
}

// RefreshToken handles token refresh
//
//	@Summary		Refresh access token
//	@Description	Generate new access token using refresh token from cookie
//	@Tags			Authentication
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	APIResponse	"Token refreshed successfully"
//	@Failure		401	{object}	APIResponse	"Refresh token not found or invalid"
//	@Failure		500	{object}	APIResponse	"Internal server error"
//	@Router			/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *fiber.Ctx) error {
	// Get refresh token from cookie
	refreshToken := c.Cookies("refresh_token")
	if refreshToken == "" {
		return ErrorResponse(c, errors.New(errors.ErrCodeUnauthorized, "Refresh token not found"))
	}

	// Refresh tokens
	tokens, err := h.authService.RefreshTokens(refreshToken)
	if err != nil {
		h.logger.LogError(err, "Failed to refresh tokens", nil)
		return ErrorResponse(c, err)
	}

	// Set new refresh token cookie
	h.setRefreshTokenCookie(c, tokens.RefreshToken, tokens.RefreshTokenExpiresAt)

	// Return response
	response := map[string]interface{}{
		"access_token": tokens.AccessToken,
		"token_type":   tokens.TokenType,
		"expires_at":   tokens.AccessTokenExpiresAt,
	}

	return SuccessResponse(c, response, "Token refreshed successfully")
}

// Logout handles user logout
//
//	@Summary		User logout
//	@Description	Logout user and revoke refresh token
//	@Tags			Authentication
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	APIResponse	"Logout successful"
//	@Failure		500	{object}	APIResponse	"Internal server error"
//	@Router			/auth/logout [post]
func (h *AuthHandler) Logout(c *fiber.Ctx) error {
	// Get refresh token from cookie
	refreshToken := c.Cookies("refresh_token")
	if refreshToken != "" {
		// Revoke refresh token
		if err := h.authService.RevokeRefreshToken(refreshToken); err != nil {
			h.logger.LogError(err, "Failed to revoke refresh token", nil)
		}
	}

	// Clear refresh token cookie
	h.clearRefreshTokenCookie(c)

	// Log logout
	if userID := c.Locals("user_id"); userID != nil {
		if userIDStr, ok := userID.(string); ok {
			h.logger.LogBusinessEvent("user.logged_out", userIDStr, nil)
		}
	}

	return SuccessResponse(c, nil, "Logout successful")
}

// Helper methods

func (h *AuthHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "email":
				validationErrors[field] = "Invalid email format"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "alphanum":
				validationErrors[field] = "Only alphanumeric characters are allowed"
			case "eqfield":
				validationErrors[field] = "Values do not match"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}

	return validationErrors
}

func (h *AuthHandler) setRefreshTokenCookie(c *fiber.Ctx, token string, expiresAt interface{}) {
	c.Cookie(&fiber.Cookie{
		Name:     "refresh_token",
		Value:    token,
		HTTPOnly: true,
		Secure:   true, // Set to true in production with HTTPS
		SameSite: "Strict",
		Path:     "/",
		// Expires:  expiresAt, // Would need type assertion for time.Time
	})
}

func (h *AuthHandler) clearRefreshTokenCookie(c *fiber.Ctx) {
	c.Cookie(&fiber.Cookie{
		Name:     "refresh_token",
		Value:    "",
		HTTPOnly: true,
		Secure:   true,
		SameSite: "Strict",
		Path:     "/",
		MaxAge:   -1,
	})
}
