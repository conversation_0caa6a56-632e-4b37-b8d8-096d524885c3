package middleware

import (
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// JWTAuthConfig defines the config for JWT authentication middleware
type JWTAuthConfig struct {
	// AuthService for token validation
	AuthService domain.AuthService

	// SkipPaths defines paths to skip authentication
	SkipPaths []string

	// TokenLookup defines where to look for the token
	// Format: "header:<name>" or "cookie:<name>" or "query:<name>"
	TokenLookup string

	// AuthScheme defines the auth scheme (e.g., "Bearer")
	AuthScheme string

	// ContextKey defines the key used to store user ID in context
	ContextKey string
}

// DefaultJWTAuthConfig is the default JWT authentication middleware config
var DefaultJWTAuthConfig = JWTAuthConfig{
	TokenLookup: "header:Authorization",
	AuthScheme:  "Bearer",
	ContextKey:  "user_id",
}

// JWTAuth middleware validates JWT tokens and sets user context
func JWTAuth(config JWTAuthConfig) fiber.Handler {
	// Set defaults
	if config.TokenLookup == "" {
		config.TokenLookup = DefaultJWTAuthConfig.TokenLookup
	}
	if config.AuthScheme == "" {
		config.AuthScheme = DefaultJWTAuthConfig.AuthScheme
	}
	if config.ContextKey == "" {
		config.ContextKey = DefaultJWTAuthConfig.ContextKey
	}

	return func(c *fiber.Ctx) error {
		// Skip if path is in skip list
		path := c.Path()
		for _, skipPath := range config.SkipPaths {
			if path == skipPath {
				return c.Next()
			}
		}

		// Extract token from request
		token, err := extractToken(c, config.TokenLookup, config.AuthScheme)
		if err != nil {
			return sendErrorResponse(c, err)
		}

		// Validate token
		userID, err := config.AuthService.ValidateAccessToken(token)
		if err != nil {
			return sendErrorResponse(c, err)
		}

		// Store user ID in context
		c.Locals(config.ContextKey, userID.String())

		return c.Next()
	}
}

// OptionalJWTAuth middleware validates JWT tokens if present but doesn't require them
func OptionalJWTAuth(config JWTAuthConfig) fiber.Handler {
	// Set defaults
	if config.TokenLookup == "" {
		config.TokenLookup = DefaultJWTAuthConfig.TokenLookup
	}
	if config.AuthScheme == "" {
		config.AuthScheme = DefaultJWTAuthConfig.AuthScheme
	}
	if config.ContextKey == "" {
		config.ContextKey = DefaultJWTAuthConfig.ContextKey
	}

	return func(c *fiber.Ctx) error {
		// Try to extract token from request
		token, err := extractToken(c, config.TokenLookup, config.AuthScheme)
		if err != nil {
			// Token not found or invalid format, continue without authentication
			return c.Next()
		}

		// Validate token
		userID, err := config.AuthService.ValidateAccessToken(token)
		if err != nil {
			// Invalid token, continue without authentication
			return c.Next()
		}

		// Store user ID in context
		c.Locals(config.ContextKey, userID.String())

		return c.Next()
	}
}

// extractToken extracts the JWT token from the request based on the lookup configuration
func extractToken(c *fiber.Ctx, tokenLookup, authScheme string) (string, error) {
	parts := strings.Split(tokenLookup, ":")
	if len(parts) != 2 {
		return "", errors.New(errors.ErrCodeInternalError, "invalid token lookup configuration")
	}

	lookupType := parts[0]
	lookupKey := parts[1]

	var tokenString string

	switch lookupType {
	case "header":
		authHeader := c.Get(lookupKey)
		if authHeader == "" {
			return "", errors.New(errors.ErrCodeUnauthorized, "missing authorization header")
		}

		// Extract token from "Bearer <token>" format
		if authScheme != "" {
			prefix := authScheme + " "
			if !strings.HasPrefix(authHeader, prefix) {
				return "", errors.New(errors.ErrCodeUnauthorized, "invalid authorization header format")
			}
			tokenString = strings.TrimPrefix(authHeader, prefix)
		} else {
			tokenString = authHeader
		}

	case "cookie":
		cookie := c.Cookies(lookupKey)
		if cookie == "" {
			return "", errors.New(errors.ErrCodeUnauthorized, "missing authentication cookie")
		}
		tokenString = cookie

	case "query":
		query := c.Query(lookupKey)
		if query == "" {
			return "", errors.New(errors.ErrCodeUnauthorized, "missing authentication query parameter")
		}
		tokenString = query

	default:
		return "", errors.New(errors.ErrCodeInternalError, "unsupported token lookup type")
	}

	if tokenString == "" {
		return "", errors.New(errors.ErrCodeUnauthorized, "empty authentication token")
	}

	return tokenString, nil
}

// GetUserIDFromContext extracts user ID from fiber context
func GetUserIDFromContext(c *fiber.Ctx) (string, bool) {
	userID := c.Locals("user_id")
	if userID == nil {
		return "", false
	}

	userIDStr, ok := userID.(string)
	return userIDStr, ok
}

// sendErrorResponse sends an error response (simplified version to avoid import cycle)
func sendErrorResponse(c *fiber.Ctx, err error) error {
	var statusCode int
	var errorCode string
	var message string

	if appErr := errors.GetAppError(err); appErr != nil {
		statusCode = appErr.HTTPStatus
		errorCode = string(appErr.Code)
		message = appErr.Message
	} else {
		statusCode = fiber.StatusInternalServerError
		errorCode = string(errors.ErrCodeInternalError)
		message = "An unexpected error occurred"
	}

	response := map[string]interface{}{
		"success": false,
		"error": map[string]interface{}{
			"code":    errorCode,
			"message": message,
		},
	}

	return c.Status(statusCode).JSON(response)
}
