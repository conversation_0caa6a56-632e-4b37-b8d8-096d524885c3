package middleware

import (
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// RequestIDConfig defines the config for RequestID middleware
type RequestIDConfig struct {
	// Header is the header key where to get/set the request ID
	Header string
	
	// Generator defines a function to generate the request ID
	Generator func() string
	
	// Con<PERSON><PERSON><PERSON> defines the key used to store the request ID in context
	ContextKey string
}

// DefaultRequestIDConfig is the default RequestID middleware config
var DefaultRequestIDConfig = RequestIDConfig{
	Header:     "X-Request-ID",
	Generator:  func() string { return uuid.New().String() },
	ContextKey: "request_id",
}

// RequestID middleware generates a unique request ID for each request
func RequestID(config ...RequestIDConfig) fiber.Handler {
	cfg := DefaultRequestIDConfig
	if len(config) > 0 {
		cfg = config[0]
	}
	
	// Set defaults
	if cfg.Header == "" {
		cfg.Header = DefaultRequestIDConfig.Header
	}
	if cfg.Generator == nil {
		cfg.Generator = DefaultRequestIDConfig.Generator
	}
	if cfg.ContextKey == "" {
		cfg.ContextKey = DefaultRequestIDConfig.ContextKey
	}
	
	return func(c *fiber.Ctx) error {
		// Get request ID from header or generate new one
		requestID := c.Get(cfg.Header)
		if requestID == "" {
			requestID = cfg.Generator()
		}
		
		// Set request ID in response header
		c.Set(cfg.Header, requestID)
		
		// Store request ID in context
		c.Locals(cfg.ContextKey, requestID)
		
		return c.Next()
	}
}
