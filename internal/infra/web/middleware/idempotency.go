package middleware

import (
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// IdempotencyMiddleware creates a middleware for handling idempotent requests
func IdempotencyMiddleware(service domain.IdempotencyService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Skip if service is disabled
		if service == nil || !service.GetConfig().Enabled {
			return c.Next()
		}

		config := service.GetConfig()
		method := c.Method()
		path := c.Path()

		// Skip if method or path should be excluded
		if !service.IsIdempotentMethod(method) || service.ShouldSkipPath(path) {
			return c.Next()
		}

		// Get idempotency key from header
		idempotencyKey := c.Get(config.KeyHeader)
		if idempotencyKey == "" {
			// No idempotency key provided, continue normally
			return c.Next()
		}

		// Validate idempotency key format (should be UUID)
		if _, err := uuid.Parse(idempotencyKey); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid idempotency key format. Must be a valid UUID.",
			})
		}

		// Get request body
		requestBody := ""
		if config.IncludeRequestBody {
			bodyBytes := c.Body()
			if int64(len(bodyBytes)) > config.MaxRequestSize {
				return c.Status(fiber.StatusRequestEntityTooLarge).JSON(fiber.Map{
					"error": "Request body too large for idempotency processing",
				})
			}
			requestBody = string(bodyBytes)
		}

		// Get user ID from context if available
		var userID *uuid.UUID
		if config.IncludeUserID {
			if userIDStr, ok := getUserIDFromContext(c); ok {
				if parsedUserID, err := uuid.Parse(userIDStr); err == nil {
					userID = &parsedUserID
				}
			}
		}

		// Create request hash
		requestHash := service.CreateRequestHash(method, path, userID, requestBody)

		// Check idempotency
		existingKey, err := service.CheckIdempotency(idempotencyKey, requestHash, method, path, userID)
		if err != nil {
			if appErr, ok := err.(*errors.AppError); ok {
				switch appErr.Code {
				case errors.ErrCodeConflict:
					return c.Status(fiber.StatusConflict).JSON(fiber.Map{
						"error": "Idempotency key conflict: " + appErr.Message,
					})
				default:
					return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
						"error": "Idempotency check failed",
					})
				}
			}
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Internal server error",
			})
		}

		// If no existing key, this is a new request
		if existingKey == nil {
			return c.Next()
		}

		// Handle existing key based on status
		switch existingKey.Status {
		case domain.IdempotencyStatusCompleted:
			// Return cached response
			return returnCachedResponse(c, existingKey)

		case domain.IdempotencyStatusPending:
			// Request is still being processed, return 409 Conflict
			return c.Status(fiber.StatusConflict).JSON(fiber.Map{
				"error":       "Request is already being processed",
				"retry_after": "1", // Suggest retry after 1 second
			})

		case domain.IdempotencyStatusFailed:
			// Previous request failed, allow retry by continuing
			return c.Next()

		case domain.IdempotencyStatusExpired:
			// Key expired, treat as new request
			return c.Next()

		default:
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Unknown idempotency status",
			})
		}
	}
}

// returnCachedResponse returns a cached response from an idempotency key
func returnCachedResponse(c *fiber.Ctx, key *domain.IdempotencyKey) error {
	// Set status code
	c.Status(key.ResponseCode)

	// Set response headers
	if key.ResponseHeaders != nil {
		for headerName, headerValue := range key.ResponseHeaders {
			// Skip certain headers that shouldn't be replayed
			if shouldSkipHeader(headerName) {
				continue
			}
			c.Set(headerName, headerValue)
		}
	}

	// Add idempotency headers
	c.Set("X-Idempotent-Replayed", "true")
	c.Set("X-Idempotent-Original-Time", key.CreatedAt.Format("2006-01-02T15:04:05Z07:00"))

	// Return response body
	if key.ResponseBody != "" {
		return c.SendString(key.ResponseBody)
	}

	return c.SendStatus(key.ResponseCode)
}

// shouldSkipHeader determines if a header should be skipped when replaying response
func shouldSkipHeader(headerName string) bool {
	skipHeaders := []string{
		"date",
		"server",
		"x-request-id",
		"x-trace-id",
		"x-span-id",
		"content-length", // Fiber will set this automatically
	}

	headerLower := strings.ToLower(headerName)
	for _, skipHeader := range skipHeaders {
		if headerLower == skipHeader {
			return true
		}
	}

	return false
}

// getUserIDFromContext extracts user ID from Fiber context
func getUserIDFromContext(c *fiber.Ctx) (string, bool) {
	// Try to get from locals (set by auth middleware)
	if userID := c.Locals("user_id"); userID != nil {
		if userIDStr, ok := userID.(string); ok {
			return userIDStr, true
		}
	}

	// Try to get from custom header
	if userID := c.Get("X-User-ID"); userID != "" {
		return userID, true
	}

	return "", false
}

// IdempotencyStatsHandler returns idempotency statistics (for monitoring)
func IdempotencyStatsHandler(service domain.IdempotencyService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if service == nil {
			return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
				"error": "Idempotency service not available",
			})
		}

		stats, err := service.GetStats()
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to get idempotency stats",
			})
		}

		return c.JSON(fiber.Map{
			"success": true,
			"data":    stats,
		})
	}
}

// IdempotencyCleanupHandler triggers cleanup of expired keys (for maintenance)
func IdempotencyCleanupHandler(service domain.IdempotencyService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if service == nil {
			return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
				"error": "Idempotency service not available",
			})
		}

		err := service.CleanupExpired()
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to cleanup expired keys",
			})
		}

		return c.JSON(fiber.Map{
			"success": true,
			"message": "Cleanup completed successfully",
		})
	}
}
