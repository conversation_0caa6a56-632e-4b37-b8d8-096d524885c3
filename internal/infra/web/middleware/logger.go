package middleware

import (
	"time"

	"github.com/gofiber/fiber/v2"

	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// LoggerConfig defines the config for Logger middleware
type LoggerConfig struct {
	// Logger instance to use
	Logger *logger.Logger

	// SkipPaths defines paths to skip logging
	SkipPaths []string

	// SkipSuccessfulRequests skips logging for successful requests (2xx status codes)
	SkipSuccessfulRequests bool
}

// Logger middleware logs HTTP requests
func Logger(config LoggerConfig) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Skip if path is in skip list
		path := c.Path()
		for _, skipPath := range config.SkipPaths {
			if path == skipPath {
				return c.Next()
			}
		}

		start := time.Now()

		// Process request
		err := c.Next()

		// Calculate duration
		duration := time.Since(start)

		// Get request details
		method := c.Method()
		statusCode := c.Response().StatusCode()
		userAgent := c.Get("User-Agent")
		clientIP := c.IP()

		// Get request ID from context
		requestID := ""
		if id := c.Locals("request_id"); id != nil {
			if idStr, ok := id.(string); ok {
				requestID = idStr
			}
		}

		// Get user ID from context if available
		userID := ""
		if id := c.Locals("user_id"); id != nil {
			if idStr, ok := id.(string); ok {
				userID = idStr
			}
		}

		// Create logger with context
		contextLogger := config.Logger
		if requestID != "" {
			contextLogger = contextLogger.WithRequestID(requestID)
		}
		if userID != "" {
			contextLogger = contextLogger.WithUserID(userID)
		}

		// Skip logging successful requests if configured
		if config.SkipSuccessfulRequests && statusCode >= 200 && statusCode < 300 {
			return err
		}

		// Log the request
		switch {
		case statusCode >= 500:
			contextLogger.Logger.Error().
				Str("method", method).
				Str("path", path).
				Str("user_agent", userAgent).
				Str("client_ip", clientIP).
				Int("status_code", statusCode).
				Dur("duration", duration).
				Int("response_size", len(c.Response().Body())).
				Msg("HTTP request processed")
		case statusCode >= 400:
			contextLogger.Logger.Warn().
				Str("method", method).
				Str("path", path).
				Str("user_agent", userAgent).
				Str("client_ip", clientIP).
				Int("status_code", statusCode).
				Dur("duration", duration).
				Int("response_size", len(c.Response().Body())).
				Msg("HTTP request processed")
		default:
			contextLogger.Logger.Info().
				Str("method", method).
				Str("path", path).
				Str("user_agent", userAgent).
				Str("client_ip", clientIP).
				Int("status_code", statusCode).
				Dur("duration", duration).
				Int("response_size", len(c.Response().Body())).
				Msg("HTTP request processed")
		}

		// Log error if present
		if err != nil {
			contextLogger.Logger.Error().
				Err(err).
				Str("method", method).
				Str("path", path).
				Msg("Request processing error")
		}

		return err
	}
}
