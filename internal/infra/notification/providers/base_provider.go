package providers

import (
	"bytes"
	"context"
	"text/template"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// BaseProvider provides common functionality for notification providers
type BaseProvider struct {
	channel domain.NotificationChannel
	enabled bool
	logger  Logger
}

// Logger interface for providers
type Logger interface {
	Info(msg string, fields map[string]interface{})
	Error(msg string, err error, fields map[string]interface{})
	Debug(msg string, fields map[string]interface{})
}

// NewBaseProvider creates a new base provider
func NewBaseProvider(channel domain.NotificationChannel, enabled bool, logger Logger) *BaseProvider {
	return &BaseProvider{
		channel: channel,
		enabled: enabled,
		logger:  logger,
	}
}

// GetChannel returns the notification channel
func (bp *BaseProvider) GetChannel() domain.NotificationChannel {
	return bp.channel
}

// IsEnabled returns whether the provider is enabled
func (bp *BaseProvider) IsEnabled() bool {
	return bp.enabled
}

// SetEnabled sets the enabled status
func (bp *BaseProvider) SetEnabled(enabled bool) {
	bp.enabled = enabled
}

// RenderTemplate renders a notification template with data
func (bp *BaseProvider) RenderTemplate(templateStr string, data map[string]interface{}) (string, error) {
	tmpl, err := template.New("notification").Parse(templateStr)
	if err != nil {
		return "", errors.New(errors.ErrCodeInvalidInput, "Invalid template: "+err.Error())
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", errors.New(errors.ErrCodeInternalError, "Template execution failed: "+err.Error())
	}

	return buf.String(), nil
}

// ValidateNotification validates a notification before sending
func (bp *BaseProvider) ValidateNotification(notification *domain.Notification) error {
	if notification == nil {
		return errors.New(errors.ErrCodeInvalidInput, "Notification cannot be nil")
	}

	if notification.Channel != bp.channel {
		return errors.New(errors.ErrCodeInvalidInput, "Notification channel mismatch")
	}

	if notification.Title == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Notification title cannot be empty")
	}

	if notification.Message == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Notification message cannot be empty")
	}

	return nil
}

// LogSendAttempt logs a notification send attempt
func (bp *BaseProvider) LogSendAttempt(notification *domain.Notification, success bool, err error) {
	fields := map[string]interface{}{
		"notification_id": notification.ID,
		"channel":         string(bp.channel),
		"user_id":         notification.UserID,
		"type":            string(notification.Type),
		"priority":        string(notification.Priority),
		"success":         success,
	}

	if notification.PantryID != nil {
		fields["pantry_id"] = *notification.PantryID
	}

	if success {
		bp.logger.Info("Notification sent successfully", fields)
	} else {
		bp.logger.Error("Failed to send notification", err, fields)
	}
}

// EmailProvider implements email notifications
type EmailProvider struct {
	*BaseProvider
	smtpHost     string
	smtpPort     int
	smtpUsername string
	smtpPassword string
	fromEmail    string
	fromName     string
}

// NewEmailProvider creates a new email provider
func NewEmailProvider(
	smtpHost string,
	smtpPort int,
	smtpUsername, smtpPassword string,
	fromEmail, fromName string,
	enabled bool,
	logger Logger,
) *EmailProvider {
	return &EmailProvider{
		BaseProvider: NewBaseProvider(domain.NotificationChannelEmail, enabled, logger),
		smtpHost:     smtpHost,
		smtpPort:     smtpPort,
		smtpUsername: smtpUsername,
		smtpPassword: smtpPassword,
		fromEmail:    fromEmail,
		fromName:     fromName,
	}
}

// Send sends an email notification
func (ep *EmailProvider) Send(ctx context.Context, notification *domain.Notification) error {
	if err := ep.ValidateNotification(notification); err != nil {
		return err
	}

	// TODO: Implement actual email sending logic
	// This would typically use a library like gomail or the standard net/smtp
	ep.logger.Info("Email notification would be sent", map[string]interface{}{
		"notification_id": notification.ID,
		"to_user":         notification.UserID,
		"subject":         notification.Title,
		"smtp_host":       ep.smtpHost,
	})

	ep.LogSendAttempt(notification, true, nil)
	return nil
}

// ValidateConfig validates the email provider configuration
func (ep *EmailProvider) ValidateConfig() error {
	if ep.smtpHost == "" {
		return errors.New(errors.ErrCodeInvalidInput, "SMTP host is required")
	}
	if ep.smtpPort <= 0 {
		return errors.New(errors.ErrCodeInvalidInput, "SMTP port must be positive")
	}
	if ep.fromEmail == "" {
		return errors.New(errors.ErrCodeInvalidInput, "From email is required")
	}
	return nil
}

// TelegramProvider implements Telegram notifications
type TelegramProvider struct {
	*BaseProvider
	botToken string
	apiURL   string
}

// NewTelegramProvider creates a new Telegram provider
func NewTelegramProvider(botToken string, enabled bool, logger Logger) *TelegramProvider {
	return &TelegramProvider{
		BaseProvider: NewBaseProvider(domain.NotificationChannelTelegram, enabled, logger),
		botToken:     botToken,
		apiURL:       "https://api.telegram.org/bot",
	}
}

// Send sends a Telegram notification
func (tp *TelegramProvider) Send(ctx context.Context, notification *domain.Notification) error {
	if err := tp.ValidateNotification(notification); err != nil {
		return err
	}

	// TODO: Implement actual Telegram sending logic
	// This would typically use the Telegram Bot API
	tp.logger.Info("Telegram notification would be sent", map[string]interface{}{
		"notification_id": notification.ID,
		"to_user":         notification.UserID,
		"message":         notification.Message,
	})

	tp.LogSendAttempt(notification, true, nil)
	return nil
}

// ValidateConfig validates the Telegram provider configuration
func (tp *TelegramProvider) ValidateConfig() error {
	if tp.botToken == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Telegram bot token is required")
	}
	return nil
}

// SupabaseProvider implements Supabase notifications
type SupabaseProvider struct {
	*BaseProvider
	projectURL string
	apiKey     string
}

// NewSupabaseProvider creates a new Supabase provider
func NewSupabaseProvider(projectURL, apiKey string, enabled bool, logger Logger) *SupabaseProvider {
	return &SupabaseProvider{
		BaseProvider: NewBaseProvider(domain.NotificationChannelSupabase, enabled, logger),
		projectURL:   projectURL,
		apiKey:       apiKey,
	}
}

// Send sends a Supabase notification
func (sp *SupabaseProvider) Send(ctx context.Context, notification *domain.Notification) error {
	if err := sp.ValidateNotification(notification); err != nil {
		return err
	}

	// TODO: Implement actual Supabase notification logic
	// This would typically use Supabase's real-time features or functions
	sp.logger.Info("Supabase notification would be sent", map[string]interface{}{
		"notification_id": notification.ID,
		"to_user":         notification.UserID,
		"project_url":     sp.projectURL,
	})

	sp.LogSendAttempt(notification, true, nil)
	return nil
}

// ValidateConfig validates the Supabase provider configuration
func (sp *SupabaseProvider) ValidateConfig() error {
	if sp.projectURL == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Supabase project URL is required")
	}
	if sp.apiKey == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Supabase API key is required")
	}
	return nil
}

// WebhookProvider implements webhook notifications
type WebhookProvider struct {
	*BaseProvider
	webhookURL string
	secret     string
}

// NewWebhookProvider creates a new webhook provider
func NewWebhookProvider(webhookURL, secret string, enabled bool, logger Logger) *WebhookProvider {
	return &WebhookProvider{
		BaseProvider: NewBaseProvider(domain.NotificationChannelWebhook, enabled, logger),
		webhookURL:   webhookURL,
		secret:       secret,
	}
}

// Send sends a webhook notification
func (wp *WebhookProvider) Send(ctx context.Context, notification *domain.Notification) error {
	if err := wp.ValidateNotification(notification); err != nil {
		return err
	}

	// TODO: Implement actual webhook sending logic
	// This would typically make an HTTP POST request to the webhook URL
	wp.logger.Info("Webhook notification would be sent", map[string]interface{}{
		"notification_id": notification.ID,
		"to_user":         notification.UserID,
		"webhook_url":     wp.webhookURL,
	})

	wp.LogSendAttempt(notification, true, nil)
	return nil
}

// ValidateConfig validates the webhook provider configuration
func (wp *WebhookProvider) ValidateConfig() error {
	if wp.webhookURL == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Webhook URL is required")
	}
	return nil
}
