package logger

import (
	"io"
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"github.com/wongpinter/pantry-pal/internal/infra/config"
)

// Logger wraps zerolog.Logger with additional functionality
type Logger struct {
	*zerolog.Logger
}

// New creates a new logger instance based on configuration
func New(cfg config.LoggerConfig) *Logger {
	// Set global log level
	level := parseLogLevel(cfg.Level)
	zerolog.SetGlobalLevel(level)

	// Configure output format
	var output io.Writer = os.Stdout

	if cfg.Format == "console" {
		// Pretty console output for development
		output = zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: cfg.TimeFormat,
			NoColor:    false,
		}
	}

	// Configure time format
	if cfg.TimeFormat != "" {
		zerolog.TimeFieldFormat = cfg.TimeFormat
	}

	// Create logger
	logger := zerolog.New(output).
		Level(level).
		With().
		Timestamp().
		Caller().
		Logger()

	return &Logger{Logger: &logger}
}

// parseLogLevel converts string log level to zerolog.Level
func parseLogLevel(level string) zerolog.Level {
	switch strings.ToLower(level) {
	case "trace":
		return zerolog.TraceLevel
	case "debug":
		return zerolog.DebugLevel
	case "info":
		return zerolog.InfoLevel
	case "warn", "warning":
		return zerolog.WarnLevel
	case "error":
		return zerolog.ErrorLevel
	case "fatal":
		return zerolog.FatalLevel
	case "panic":
		return zerolog.PanicLevel
	default:
		return zerolog.InfoLevel
	}
}

// WithRequestID adds request ID to logger context
func (l *Logger) WithRequestID(requestID string) *Logger {
	logger := l.With().Str("request_id", requestID).Logger()
	return &Logger{Logger: &logger}
}

// WithUserID adds user ID to logger context
func (l *Logger) WithUserID(userID string) *Logger {
	logger := l.With().Str("user_id", userID).Logger()
	return &Logger{Logger: &logger}
}

// WithPantryID adds pantry ID to logger context
func (l *Logger) WithPantryID(pantryID string) *Logger {
	logger := l.With().Str("pantry_id", pantryID).Logger()
	return &Logger{Logger: &logger}
}

// WithComponent adds component name to logger context
func (l *Logger) WithComponent(component string) *Logger {
	logger := l.With().Str("component", component).Logger()
	return &Logger{Logger: &logger}
}

// WithFields adds multiple fields to logger context
func (l *Logger) WithFields(fields map[string]interface{}) *Logger {
	event := l.With()
	for key, value := range fields {
		event = event.Interface(key, value)
	}
	logger := event.Logger()
	return &Logger{Logger: &logger}
}

// LogHTTPRequest logs HTTP request details
func (l *Logger) LogHTTPRequest(method, path, userAgent, clientIP string, statusCode int, duration time.Duration) {
	l.Info().
		Str("method", method).
		Str("path", path).
		Str("user_agent", userAgent).
		Str("client_ip", clientIP).
		Int("status_code", statusCode).
		Dur("duration", duration).
		Msg("HTTP request processed")
}

// LogInfo logs info message with additional context
func (l *Logger) LogInfo(message string, fields map[string]interface{}) {
	event := l.Info()

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg(message)
}

// LogWarn logs warning message with additional context
func (l *Logger) LogWarn(message string, fields map[string]interface{}) {
	event := l.Warn()

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg(message)
}

// LogError logs error with additional context
func (l *Logger) LogError(err error, message string, fields map[string]interface{}) {
	event := l.Error().Err(err)

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg(message)
}

// LogDatabaseQuery logs database query execution
func (l *Logger) LogDatabaseQuery(query string, duration time.Duration, rowsAffected int64) {
	l.Debug().
		Str("query", query).
		Dur("duration", duration).
		Int64("rows_affected", rowsAffected).
		Msg("Database query executed")
}

// LogBusinessEvent logs business domain events
func (l *Logger) LogBusinessEvent(eventType string, entityID string, fields map[string]interface{}) {
	event := l.Info().
		Str("event_type", eventType).
		Str("entity_id", entityID)

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Business event occurred")
}

// LogSecurityEvent logs security-related events
func (l *Logger) LogSecurityEvent(eventType string, userID string, details map[string]interface{}) {
	event := l.Warn().
		Str("security_event", eventType).
		Str("user_id", userID)

	if details != nil {
		for key, value := range details {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Security event detected")
}

// SetGlobalLogger sets the global logger instance
func SetGlobalLogger(logger *Logger) {
	log.Logger = *logger.Logger
}
