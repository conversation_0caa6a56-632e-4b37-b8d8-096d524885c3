package auth

import (
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// PantryAuthorizationService implements domain.PantryAuthorizationService
type PantryAuthorizationService struct {
	pantryRepo     domain.PantryRepository
	membershipRepo domain.PantryMembershipRepository
}

// NewPantryAuthorizationService creates a new pantry authorization service
func NewPantryAuthorizationService(
	pantryRepo domain.PantryRepository,
	membershipRepo domain.PantryMembershipRepository,
) *PantryAuthorizationService {
	return &PantryAuthorizationService{
		pantryRepo:     pantryRepo,
		membershipRepo: membershipRepo,
	}
}

// CheckPermission checks if a user has a specific permission for a pantry
func (s *PantryAuthorizationService) CheckPermission(userID, pantryID uuid.UUID, permission domain.PantryPermission) (bool, error) {
	// Check if user is the owner
	isOwner, err := s.IsOwner(userID, pantryID)
	if err != nil {
		return false, err
	}
	
	if isOwner {
		// Owners have all permissions
		return true, nil
	}
	
	// Get user's role in the pantry
	role, err := s.GetUserRole(userID, pantryID)
	if err != nil {
		return false, err
	}
	
	if role == nil {
		// User is not a member
		return false, nil
	}
	
	// Check if the role has the permission
	return domain.HasPermission(*role, permission), nil
}

// GetUserRole gets the user's role in a pantry
func (s *PantryAuthorizationService) GetUserRole(userID, pantryID uuid.UUID) (*domain.PantryRole, error) {
	// First check if user is the owner
	isOwner, err := s.IsOwner(userID, pantryID)
	if err != nil {
		return nil, err
	}
	
	if isOwner {
		role := domain.PantryRoleOwner
		return &role, nil
	}
	
	// Check membership
	membership, err := s.membershipRepo.GetByPantryAndUser(pantryID, userID)
	if err != nil {
		if errors.GetAppError(err) != nil && errors.GetAppError(err).Code == errors.ErrCodeNotFound {
			return nil, nil // User is not a member
		}
		return nil, err
	}
	
	if !membership.IsActive() {
		return nil, nil // Membership is not active
	}
	
	return &membership.Role, nil
}

// IsOwner checks if a user is the owner of a pantry
func (s *PantryAuthorizationService) IsOwner(userID, pantryID uuid.UUID) (bool, error) {
	pantry, err := s.pantryRepo.GetByID(pantryID)
	if err != nil {
		return false, err
	}
	
	return pantry.OwnerUserID == userID, nil
}

// IsMember checks if a user is an active member of a pantry
func (s *PantryAuthorizationService) IsMember(userID, pantryID uuid.UUID) (bool, error) {
	// Check if user is the owner
	isOwner, err := s.IsOwner(userID, pantryID)
	if err != nil {
		return false, err
	}
	
	if isOwner {
		return true, nil
	}
	
	// Check active membership
	return s.membershipRepo.ExistsActiveMembership(pantryID, userID)
}

// CanInviteWithRole checks if a user can invite someone with a specific role
func (s *PantryAuthorizationService) CanInviteWithRole(userID, pantryID uuid.UUID, targetRole domain.PantryRole) (bool, error) {
	// Check if user has permission to invite members
	canInvite, err := s.CheckPermission(userID, pantryID, domain.PermissionInviteMembers)
	if err != nil {
		return false, err
	}
	
	if !canInvite {
		return false, nil
	}
	
	// Get user's role
	userRole, err := s.GetUserRole(userID, pantryID)
	if err != nil {
		return false, err
	}
	
	if userRole == nil {
		return false, nil
	}
	
	// Check if user can manage the target role
	return domain.CanManageRole(*userRole, targetRole), nil
}

// CanUpdateMemberRole checks if a user can update another member's role
func (s *PantryAuthorizationService) CanUpdateMemberRole(userID, pantryID, targetUserID uuid.UUID, newRole domain.PantryRole) (bool, error) {
	// Users cannot update their own role
	if userID == targetUserID {
		return false, nil
	}
	
	// Check if user has permission to update member roles
	canUpdate, err := s.CheckPermission(userID, pantryID, domain.PermissionUpdateMemberRoles)
	if err != nil {
		return false, err
	}
	
	if !canUpdate {
		return false, nil
	}
	
	// Get user's role
	userRole, err := s.GetUserRole(userID, pantryID)
	if err != nil {
		return false, err
	}
	
	if userRole == nil {
		return false, nil
	}
	
	// Get target user's current role
	targetRole, err := s.GetUserRole(targetUserID, pantryID)
	if err != nil {
		return false, err
	}
	
	if targetRole == nil {
		return false, nil // Target user is not a member
	}
	
	// Check if user can manage both the current and new roles
	canManageCurrent := domain.CanManageRole(*userRole, *targetRole)
	canManageNew := domain.CanManageRole(*userRole, newRole)
	
	return canManageCurrent && canManageNew, nil
}

// CanRemoveMember checks if a user can remove another member
func (s *PantryAuthorizationService) CanRemoveMember(userID, pantryID, targetUserID uuid.UUID) (bool, error) {
	// Users cannot remove themselves (they should leave instead)
	if userID == targetUserID {
		return false, nil
	}
	
	// Check if user has permission to remove members
	canRemove, err := s.CheckPermission(userID, pantryID, domain.PermissionRemoveMembers)
	if err != nil {
		return false, err
	}
	
	if !canRemove {
		return false, nil
	}
	
	// Get user's role
	userRole, err := s.GetUserRole(userID, pantryID)
	if err != nil {
		return false, err
	}
	
	if userRole == nil {
		return false, nil
	}
	
	// Get target user's role
	targetRole, err := s.GetUserRole(targetUserID, pantryID)
	if err != nil {
		return false, err
	}
	
	if targetRole == nil {
		return false, nil // Target user is not a member
	}
	
	// Check if user can manage the target role
	return domain.CanManageRole(*userRole, *targetRole), nil
}
