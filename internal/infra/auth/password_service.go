package auth

import (
	"golang.org/x/crypto/bcrypt"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// PasswordService implements domain.HashService interface for password hashing
type PasswordService struct {
	hashCost int
}

// NewPasswordService creates a new PasswordService instance
func NewPasswordService(hashCost int) *PasswordService {
	if hashCost < bcrypt.MinCost || hashCost > bcrypt.MaxCost {
		hashCost = bcrypt.DefaultCost
	}
	return &PasswordService{
		hashCost: hashCost,
	}
}

// Hash creates a bcrypt hash of the password
func (s *PasswordService) Hash(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), s.hashCost)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// Verify compares a bcrypt hashed password with its possible plaintext equivalent
func (s *PasswordService) Verify(password, hash string) error {
	return bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
}

var _ domain.HashService = (*PasswordService)(nil) // Verify interface implementation
