package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// RefreshTokenRepository implements domain.RefreshTokenRepository using Redis
type RefreshTokenRepository struct {
	client    *redis.Client
	logger    *logger.Logger
	keyPrefix string
}

// NewRefreshTokenRepository creates a new Redis refresh token repository
func NewRefreshTokenRepository(client *redis.Client, log *logger.Logger, keyPrefix string) *RefreshTokenRepository {
	if keyPrefix == "" {
		keyPrefix = "refresh_token"
	}
	return &RefreshTokenRepository{
		client:    client,
		logger:    log,
		keyPrefix: keyPrefix,
	}
}

// Create stores a new refresh token in Redis
func (r *RefreshTokenRepository) Create(ctx context.Context, token *domain.RefreshToken) error {
	key := r.getKey(token.TokenHash)
	tokenJSON, err := json.Marshal(token)
	if err != nil {
		r.logger.LogError(err, "Failed to marshal refresh token", map[string]interface{}{
			"token_id": token.ID.String(),
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "failed to marshal refresh token")
	}

	// Store with expiration
	err = r.client.Set(ctx, key, tokenJSON, time.Until(token.ExpiresAt)).Err()
	if err != nil {
		r.logger.LogError(err, "Failed to store refresh token in Redis", map[string]interface{}{
			"key":      key,
			"user_id":  token.UserID.String(),
			"expires_at": token.ExpiresAt.String(),
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "failed to store refresh token")
	}

	r.logger.LogInfo("Refresh token stored in Redis", map[string]interface{}{
		"key":      key,
		"user_id":  token.UserID.String(),
		"expires_at": token.ExpiresAt.String(),
	})
	return nil
}

// GetByTokenHash retrieves a refresh token by its hash from Redis
func (r *RefreshTokenRepository) GetByTokenHash(ctx context.Context, tokenHash string) (*domain.RefreshToken, error) {
	key := r.getKey(tokenHash)
	tokenJSON, err := r.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return nil, errors.New(errors.ErrCodeNotFound, "refresh token not found")
	}
	if err != nil {
		r.logger.LogError(err, "Failed to get refresh token from Redis", map[string]interface{}{
			"key": key,
		})
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "failed to get refresh token")
	}

	var token domain.RefreshToken
	if err := json.Unmarshal([]byte(tokenJSON), &token); err != nil {
		r.logger.LogError(err, "Failed to unmarshal refresh token from Redis", map[string]interface{}{
			"key": key,
		})
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "failed to unmarshal refresh token")
	}

	return &token, nil
}

// RevokeByTokenHash removes a refresh token by its hash from Redis
func (r *RefreshTokenRepository) RevokeByTokenHash(ctx context.Context, tokenHash string) error {
	key := r.getKey(tokenHash)
	cmd := r.client.Del(ctx, key)
	if cmd.Err() != nil {
		r.logger.LogError(cmd.Err(), "Failed to delete refresh token from Redis", map[string]interface{}{
			"key": key,
		})
		return errors.Wrap(cmd.Err(), errors.ErrCodeInternalError, "failed to revoke refresh token")
	}

	if cmd.Val() == 0 {
		r.logger.LogWarn("Attempted to revoke non-existent refresh token", map[string]interface{}{
			"key": key,
		})
		return errors.New(errors.ErrCodeNotFound, "refresh token not found for revocation")
	}

	r.logger.LogInfo("Refresh token revoked from Redis", map[string]interface{}{
		"key": key,
	})
	return nil
}

// RevokeByUserID removes all refresh tokens for a given user from Redis
// This implementation is less efficient for Redis as it requires scanning keys.
// For large numbers of tokens per user, consider a different Redis data structure (e.g., a set of token hashes per user).
func (r *RefreshTokenRepository) RevokeByUserID(ctx context.Context, userID uuid.UUID) error {
	// Find all keys associated with the user. This can be inefficient for large datasets.
	// A better approach for production would be to store user's refresh token hashes in a Redis Set.
	pattern := fmt.Sprintf("%s:*", r.keyPrefix) // This pattern is too broad if not careful
	
	// To make this more efficient, we would need to change how tokens are stored.
	// For example, store them in a HASH where the field is tokenHash and value is tokenJSON,
	// and have a SET for each user containing their token hashes.
	// For now, we'll iterate through all keys and check the UserID.
	
	var keysToDelete []string
	iter := r.client.Scan(ctx, 0, pattern, 0).Iterator()
	for iter.Next(ctx) {
		key := iter.Val()
		tokenJSON, err := r.client.Get(ctx, key).Result()
		if err != nil {
			r.logger.LogError(err, "Failed to get token during user revocation scan", map[string]interface{}{
				"key": key,
			})
			continue
		}

		var token domain.RefreshToken
		if err := json.Unmarshal([]byte(tokenJSON), &token); err != nil {
			r.logger.LogError(err, "Failed to unmarshal token during user revocation scan", map[string]interface{}{
				"key": key,
			})
			continue
		}

		if token.UserID == userID {
			keysToDelete = append(keysToDelete, key)
		}
	}
	if err := iter.Err(); err != nil {
		r.logger.LogError(err, "Failed to scan keys for user token revocation", map[string]interface{}{
			"user_id": userID.String(),
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "failed to scan tokens for user revocation")
	}

	if len(keysToDelete) > 0 {
		cmd := r.client.Del(ctx, keysToDelete...)
		if cmd.Err() != nil {
			r.logger.LogError(cmd.Err(), "Failed to delete user refresh tokens from Redis", map[string]interface{}{
				"user_id": userID.String(),
				"keys":    keysToDelete,
			})
			return errors.Wrap(cmd.Err(), errors.ErrCodeInternalError, "failed to revoke user refresh tokens")
		}
		r.logger.LogInfo("All refresh tokens revoked for user from Redis", map[string]interface{}{
			"user_id": userID.String(),
			"count":   len(keysToDelete),
		})
	} else {
		r.logger.LogInfo("No refresh tokens found for user to revoke", map[string]interface{}{
			"user_id": userID.String(),
		})
	}

	return nil
}

// GetByUserID retrieves all refresh tokens for a given user from Redis
// This implementation is inefficient as it scans all keys.
// For production, consider storing user's refresh token hashes in a Redis Set.
func (r *RefreshTokenRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.RefreshToken, error) {
	var userTokens []*domain.RefreshToken
	pattern := fmt.Sprintf("%s:*", r.keyPrefix) // This pattern is too broad if not careful

	iter := r.client.Scan(ctx, 0, pattern, 0).Iterator()
	for iter.Next(ctx) {
		key := iter.Val()
		tokenJSON, err := r.client.Get(ctx, key).Result()
		if err != nil {
			r.logger.LogError(err, "Failed to get token during user ID scan", map[string]interface{}{
				"key": key,
			})
			continue
		}

		var token domain.RefreshToken
		if err := json.Unmarshal([]byte(tokenJSON), &token); err != nil {
			r.logger.LogError(err, "Failed to unmarshal token during user ID scan", map[string]interface{}{
				"key": key,
			})
			continue
		}

		if token.UserID == userID {
			userTokens = append(userTokens, &token)
		}
	}
	if err := iter.Err(); err != nil {
		r.logger.LogError(err, "Failed to scan keys for GetByUserID", map[string]interface{}{
			"user_id": userID.String(),
		})
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "failed to scan tokens for user ID")
	}

	return userTokens, nil
}

// Update updates an existing refresh token in Redis
func (r *RefreshTokenRepository) Update(ctx context.Context, token *domain.RefreshToken) error {
	key := r.getKey(token.TokenHash)
	tokenJSON, err := json.Marshal(token)
	if err != nil {
		r.logger.LogError(err, "Failed to marshal refresh token for update", map[string]interface{}{
			"token_id": token.ID.String(),
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "failed to marshal refresh token for update")
	}

	// Use Set with XX (only if key already exists) and update TTL
	// Note: This assumes the tokenHash remains the same for updates.
	// If the token itself changes (e.g., new JTI), it's effectively a new token.
	err = r.client.SetXX(ctx, key, tokenJSON, time.Until(token.ExpiresAt)).Err()
	if err == redis.Nil {
		r.logger.LogWarn("Attempted to update non-existent refresh token", map[string]interface{}{
			"key": key,
			"token_id": token.ID.String(),
		})
		return errors.New(errors.ErrCodeNotFound, "refresh token not found for update")
	}
	if err != nil {
		r.logger.LogError(err, "Failed to update refresh token in Redis", map[string]interface{}{
			"key":      key,
			"token_id": token.ID.String(),
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "failed to update refresh token")
	}

	r.logger.LogInfo("Refresh token updated in Redis", map[string]interface{}{
		"key":      key,
		"token_id": token.ID.String(),
	})
	return nil
}

// DeleteExpired deletes all expired refresh tokens from Redis
// This is an expensive operation as it scans all keys.
// In a real-world scenario, Redis's built-in TTL mechanism handles expiration automatically.
// This method is primarily for explicit cleanup or to satisfy the interface.
func (r *RefreshTokenRepository) DeleteExpired(ctx context.Context) error {
	pattern := fmt.Sprintf("%s:*", r.keyPrefix)
	var keysToDelete []string

	iter := r.client.Scan(ctx, 0, pattern, 0).Iterator()
	for iter.Next(ctx) {
		key := iter.Val()
		tokenJSON, err := r.client.Get(ctx, key).Result()
		if err != nil {
			r.logger.LogError(err, "Failed to get token during expired scan", map[string]interface{}{
				"key": key,
			})
			continue
		}

		var token domain.RefreshToken
		if err := json.Unmarshal([]byte(tokenJSON), &token); err != nil {
			r.logger.LogError(err, "Failed to unmarshal token during expired scan", map[string]interface{}{
				"key": key,
			})
			continue
		}

		if token.IsExpired() {
			keysToDelete = append(keysToDelete, key)
		}
	}
	if err := iter.Err(); err != nil {
		r.logger.LogError(err, "Failed to scan keys for expired token deletion", nil)
		return errors.Wrap(err, errors.ErrCodeInternalError, "failed to scan tokens for expiration")
	}

	if len(keysToDelete) > 0 {
		cmd := r.client.Del(ctx, keysToDelete...)
		if cmd.Err() != nil {
			r.logger.LogError(cmd.Err(), "Failed to delete expired refresh tokens from Redis", map[string]interface{}{
				"keys": keysToDelete,
			})
			return errors.Wrap(cmd.Err(), errors.ErrCodeInternalError, "failed to delete expired refresh tokens")
		}
		r.logger.LogInfo("Deleted expired refresh tokens from Redis", map[string]interface{}{
			"count": len(keysToDelete),
		})
	} else {
		r.logger.LogInfo("No expired refresh tokens found to delete", nil)
	}

	return nil
}

// getKey generates a Redis key for a refresh token
func (r *RefreshTokenRepository) getKey(tokenHash string) string {
	return fmt.Sprintf("%s:%s", r.keyPrefix, tokenHash)
}