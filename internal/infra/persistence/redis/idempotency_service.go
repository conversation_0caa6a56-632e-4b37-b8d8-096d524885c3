package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// IdempotencyResponse represents a cached response for idempotency
type IdempotencyResponse struct {
	StatusCode int               `json:"status_code"`
	Body       interface{}       `json:"body"`
	Headers    map[string]string `json:"headers,omitempty"`
	CachedAt   time.Time         `json:"cached_at"`
}

// IdempotencyService handles idempotency operations using Redis
type IdempotencyService struct {
	client        *redis.Client
	logger        *logger.Logger
	processingTTL time.Duration // TTL for "processing" placeholder
	responseTTL   time.Duration // TTL for cached responses
	keyPrefix     string        // Prefix for Redis keys
}

// IdempotencyConfig holds configuration for the idempotency service
type IdempotencyConfig struct {
	ProcessingTTL time.Duration `koanf:"processing_ttl"`
	ResponseTTL   time.Duration `koanf:"response_ttl"`
	KeyPrefix     string        `koanf:"key_prefix"`
}

// NewIdempotencyService creates a new idempotency service
func NewIdempotencyService(client *redis.Client, config IdempotencyConfig, log *logger.Logger) *IdempotencyService {
	// Set defaults if not provided
	if config.ProcessingTTL == 0 {
		config.ProcessingTTL = 30 * time.Second // 30 seconds for processing
	}
	if config.ResponseTTL == 0 {
		config.ResponseTTL = 24 * time.Hour // 24 hours for cached responses
	}
	if config.KeyPrefix == "" {
		config.KeyPrefix = "idempotency"
	}

	return &IdempotencyService{
		client:        client,
		logger:        log,
		processingTTL: config.ProcessingTTL,
		responseTTL:   config.ResponseTTL,
		keyPrefix:     config.KeyPrefix,
	}
}

// IdempotencyResult represents the result of an idempotency check
type IdempotencyResult struct {
	IsProcessing bool                 // True if request is currently being processed
	HasResponse  bool                 // True if cached response exists
	Response     *IdempotencyResponse // Cached response if available
}

// CheckIdempotency checks if a request with the given key is already processed or being processed
func (s *IdempotencyService) CheckIdempotency(ctx context.Context, idempotencyKey string) (*IdempotencyResult, error) {
	if idempotencyKey == "" {
		return nil, errors.New(errors.ErrCodeInvalidInput, "Idempotency key cannot be empty")
	}

	// Validate UUID format
	if _, err := uuid.Parse(idempotencyKey); err != nil {
		return nil, errors.New(errors.ErrCodeInvalidInput, "Idempotency key must be a valid UUID")
	}

	processingKey := s.getProcessingKey(idempotencyKey)
	responseKey := s.getResponseKey(idempotencyKey)

	// Check if response is already cached
	cachedResponse, err := s.client.Get(ctx, responseKey).Result()
	if err == nil {
		// Response exists, parse and return it
		var response IdempotencyResponse
		if err := json.Unmarshal([]byte(cachedResponse), &response); err != nil {
			s.logger.LogError(err, "Failed to unmarshal cached idempotency response", map[string]interface{}{
				"idempotency_key": idempotencyKey,
				"response_key":    responseKey,
			})
			// Continue to check processing status instead of failing
		} else {
			return &IdempotencyResult{
				IsProcessing: false,
				HasResponse:  true,
				Response:     &response,
			}, nil
		}
	} else if err != redis.Nil {
		s.logger.LogError(err, "Failed to check cached idempotency response", map[string]interface{}{
			"idempotency_key": idempotencyKey,
			"response_key":    responseKey,
		})
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "Failed to check idempotency cache")
	}

	// Check if request is currently being processed
	isProcessing, err := s.client.Exists(ctx, processingKey).Result()
	if err != nil {
		s.logger.LogError(err, "Failed to check processing status", map[string]interface{}{
			"idempotency_key": idempotencyKey,
			"processing_key":  processingKey,
		})
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "Failed to check processing status")
	}

	return &IdempotencyResult{
		IsProcessing: isProcessing > 0,
		HasResponse:  false,
		Response:     nil,
	}, nil
}

// StartProcessing marks a request as being processed
func (s *IdempotencyService) StartProcessing(ctx context.Context, idempotencyKey string) error {
	if idempotencyKey == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Idempotency key cannot be empty")
	}

	processingKey := s.getProcessingKey(idempotencyKey)

	// Set processing placeholder with TTL
	err := s.client.SetNX(ctx, processingKey, "processing", s.processingTTL).Err()
	if err != nil {
		s.logger.LogError(err, "Failed to set processing placeholder", map[string]interface{}{
			"idempotency_key": idempotencyKey,
			"processing_key":  processingKey,
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "Failed to mark request as processing")
	}

	s.logger.LogInfo("Started processing idempotent request", map[string]interface{}{
		"idempotency_key": idempotencyKey,
		"processing_ttl":  s.processingTTL.String(),
	})

	return nil
}

// CacheResponse caches the response for successful requests (2xx status codes)
func (s *IdempotencyService) CacheResponse(ctx context.Context, idempotencyKey string, statusCode int, body interface{}, headers map[string]string) error {
	if idempotencyKey == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Idempotency key cannot be empty")
	}

	// Only cache successful responses (2xx status codes)
	if statusCode < 200 || statusCode >= 300 {
		s.logger.LogInfo("Not caching non-2xx response", map[string]interface{}{
			"idempotency_key": idempotencyKey,
			"status_code":     statusCode,
		})
		return s.clearProcessing(ctx, idempotencyKey)
	}

	responseKey := s.getResponseKey(idempotencyKey)
	processingKey := s.getProcessingKey(idempotencyKey)

	response := IdempotencyResponse{
		StatusCode: statusCode,
		Body:       body,
		Headers:    headers,
		CachedAt:   time.Now(),
	}

	responseData, err := json.Marshal(response)
	if err != nil {
		s.logger.LogError(err, "Failed to marshal idempotency response", map[string]interface{}{
			"idempotency_key": idempotencyKey,
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "Failed to serialize response for caching")
	}

	// Use a pipeline to atomically cache response and clear processing
	pipe := s.client.Pipeline()
	pipe.Set(ctx, responseKey, responseData, s.responseTTL)
	pipe.Del(ctx, processingKey)

	_, err = pipe.Exec(ctx)
	if err != nil {
		s.logger.LogError(err, "Failed to cache idempotency response", map[string]interface{}{
			"idempotency_key": idempotencyKey,
			"response_key":    responseKey,
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "Failed to cache response")
	}

	s.logger.LogInfo("Cached idempotent response", map[string]interface{}{
		"idempotency_key": idempotencyKey,
		"status_code":     statusCode,
		"response_ttl":    s.responseTTL.String(),
	})

	return nil
}

// ClearProcessing removes the processing placeholder (for failed requests)
func (s *IdempotencyService) ClearProcessing(ctx context.Context, idempotencyKey string) error {
	return s.clearProcessing(ctx, idempotencyKey)
}

// clearProcessing is the internal implementation for clearing processing status
func (s *IdempotencyService) clearProcessing(ctx context.Context, idempotencyKey string) error {
	if idempotencyKey == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Idempotency key cannot be empty")
	}

	processingKey := s.getProcessingKey(idempotencyKey)

	err := s.client.Del(ctx, processingKey).Err()
	if err != nil {
		s.logger.LogError(err, "Failed to clear processing placeholder", map[string]interface{}{
			"idempotency_key": idempotencyKey,
			"processing_key":  processingKey,
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "Failed to clear processing status")
	}

	s.logger.LogInfo("Cleared processing status", map[string]interface{}{
		"idempotency_key": idempotencyKey,
	})

	return nil
}

// InvalidateResponse removes a cached response (useful for testing or manual invalidation)
func (s *IdempotencyService) InvalidateResponse(ctx context.Context, idempotencyKey string) error {
	if idempotencyKey == "" {
		return errors.New(errors.ErrCodeInvalidInput, "Idempotency key cannot be empty")
	}

	responseKey := s.getResponseKey(idempotencyKey)
	processingKey := s.getProcessingKey(idempotencyKey)

	// Remove both response and processing keys
	pipe := s.client.Pipeline()
	pipe.Del(ctx, responseKey)
	pipe.Del(ctx, processingKey)

	_, err := pipe.Exec(ctx)
	if err != nil {
		s.logger.LogError(err, "Failed to invalidate idempotency cache", map[string]interface{}{
			"idempotency_key": idempotencyKey,
		})
		return errors.Wrap(err, errors.ErrCodeInternalError, "Failed to invalidate idempotency cache")
	}

	s.logger.LogInfo("Invalidated idempotency cache", map[string]interface{}{
		"idempotency_key": idempotencyKey,
	})

	return nil
}

// GetStats returns statistics about idempotency cache usage
func (s *IdempotencyService) GetStats(ctx context.Context) (map[string]interface{}, error) {
	// Count processing and response keys
	processingPattern := s.getProcessingKey("*")
	responsePattern := s.getResponseKey("*")

	processingKeys, err := s.client.Keys(ctx, processingPattern).Result()
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "Failed to get processing keys")
	}

	responseKeys, err := s.client.Keys(ctx, responsePattern).Result()
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInternalError, "Failed to get response keys")
	}

	stats := map[string]interface{}{
		"processing_requests": len(processingKeys),
		"cached_responses":    len(responseKeys),
		"processing_ttl":      s.processingTTL.String(),
		"response_ttl":        s.responseTTL.String(),
		"key_prefix":          s.keyPrefix,
	}

	return stats, nil
}

// Helper methods

func (s *IdempotencyService) getProcessingKey(idempotencyKey string) string {
	return fmt.Sprintf("%s:processing:%s", s.keyPrefix, idempotencyKey)
}

func (s *IdempotencyService) getResponseKey(idempotencyKey string) string {
	return fmt.Sprintf("%s:response:%s", s.keyPrefix, idempotencyKey)
}
