package postgres

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// ShoppingListRepository implements the shopping list repository using GORM
type ShoppingListRepository struct {
	db *gorm.DB
}

// NewShoppingListRepository creates a new shopping list repository
func NewShoppingListRepository(db *gorm.DB) domain.ShoppingListRepository {
	return &ShoppingListRepository{db: db}
}

// Create creates a new shopping list
func (r *ShoppingListRepository) Create(ctx context.Context, shoppingList *domain.ShoppingList) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create the shopping list
		if err := tx.Create(shoppingList).Error; err != nil {
			return err
		}

		// Create items if any
		for i := range shoppingList.Items {
			shoppingList.Items[i].ShoppingListID = shoppingList.ID
			if err := tx.Create(&shoppingList.Items[i]).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// GetByID retrieves a shopping list by ID
func (r *ShoppingListRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.ShoppingList, error) {
	var shoppingList domain.ShoppingList
	err := r.db.WithContext(ctx).
		Preload("Items").
		Where("id = ?", id).
		First(&shoppingList).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("shopping list not found")
		}
		return nil, err
	}

	return &shoppingList, nil
}

// GetByPantryID retrieves shopping lists for a pantry with filtering
func (r *ShoppingListRepository) GetByPantryID(ctx context.Context, pantryID uuid.UUID, filters domain.ShoppingListFilters) ([]*domain.ShoppingList, error) {
	var shoppingLists []*domain.ShoppingList

	query := r.db.WithContext(ctx).Model(&domain.ShoppingList{}).Where("pantry_id = ?", pantryID)

	// Apply filters
	query = r.applyFilters(query, filters)

	// Apply sorting
	query = r.applySorting(query, filters)

	// Apply pagination
	query = r.applyPagination(query, filters)

	// Load with associations
	err := query.Preload("Items").Find(&shoppingLists).Error
	if err != nil {
		return nil, err
	}

	return shoppingLists, nil
}

// Update updates a shopping list
func (r *ShoppingListRepository) Update(ctx context.Context, shoppingList *domain.ShoppingList) error {
	return r.db.WithContext(ctx).Save(shoppingList).Error
}

// Delete deletes a shopping list
func (r *ShoppingListRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Delete associated items first
		if err := tx.Where("shopping_list_id = ?", id).Delete(&domain.ShoppingListItemEntity{}).Error; err != nil {
			return err
		}

		// Delete the shopping list
		return tx.Delete(&domain.ShoppingList{}, id).Error
	})
}

// AddItem adds an item to a shopping list
func (r *ShoppingListRepository) AddItem(ctx context.Context, item *domain.ShoppingListItemEntity) error {
	return r.db.WithContext(ctx).Create(item).Error
}

// UpdateItem updates a shopping list item
func (r *ShoppingListRepository) UpdateItem(ctx context.Context, item *domain.ShoppingListItemEntity) error {
	return r.db.WithContext(ctx).Save(item).Error
}

// RemoveItem removes an item from a shopping list
func (r *ShoppingListRepository) RemoveItem(ctx context.Context, itemID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&domain.ShoppingListItemEntity{}, itemID).Error
}

// GetItemByID retrieves a shopping list item by ID
func (r *ShoppingListRepository) GetItemByID(ctx context.Context, itemID uuid.UUID) (*domain.ShoppingListItemEntity, error) {
	var item domain.ShoppingListItemEntity
	err := r.db.WithContext(ctx).Where("id = ?", itemID).First(&item).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("shopping list item not found")
		}
		return nil, err
	}

	return &item, nil
}

// GetItemsByShoppingListID retrieves all items for a shopping list
func (r *ShoppingListRepository) GetItemsByShoppingListID(ctx context.Context, shoppingListID uuid.UUID) ([]*domain.ShoppingListItemEntity, error) {
	var items []*domain.ShoppingListItemEntity
	err := r.db.WithContext(ctx).Where("shopping_list_id = ?", shoppingListID).Find(&items).Error
	if err != nil {
		return nil, err
	}

	return items, nil
}

// MarkItemsAsPurchased marks multiple items as purchased
func (r *ShoppingListRepository) MarkItemsAsPurchased(ctx context.Context, itemIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&domain.ShoppingListItemEntity{}).
		Where("id IN ?", itemIDs).
		Updates(map[string]any{
			"is_purchased": true,
			"purchased_at": gorm.Expr("NOW()"),
		}).Error
}

// MarkItemsAsNotPurchased marks multiple items as not purchased
func (r *ShoppingListRepository) MarkItemsAsNotPurchased(ctx context.Context, itemIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&domain.ShoppingListItemEntity{}).
		Where("id IN ?", itemIDs).
		Updates(map[string]any{
			"is_purchased": false,
			"purchased_at": nil,
		}).Error
}

// GetShoppingListStats retrieves statistics for a shopping list
func (r *ShoppingListRepository) GetShoppingListStats(ctx context.Context, shoppingListID uuid.UUID) (*domain.ShoppingListStats, error) {
	var stats domain.ShoppingListStats
	stats.ShoppingListID = shoppingListID

	// Count total items
	var totalCount int64
	if err := r.db.WithContext(ctx).Model(&domain.ShoppingListItemEntity{}).
		Where("shopping_list_id = ?", shoppingListID).
		Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats.TotalItems = int(totalCount)

	// Count purchased items
	var purchasedCount int64
	if err := r.db.WithContext(ctx).Model(&domain.ShoppingListItemEntity{}).
		Where("shopping_list_id = ? AND is_purchased = ?", shoppingListID, true).
		Count(&purchasedCount).Error; err != nil {
		return nil, err
	}

	stats.PurchasedItems = int(purchasedCount)
	stats.RemainingItems = stats.TotalItems - stats.PurchasedItems

	// Calculate completion percentage
	if stats.TotalItems > 0 {
		stats.CompletionPercentage = float64(stats.PurchasedItems) / float64(stats.TotalItems) * 100.0
	}

	return &stats, nil
}

// GetPantryShoppingListStats retrieves statistics for all shopping lists in a pantry
func (r *ShoppingListRepository) GetPantryShoppingListStats(ctx context.Context, pantryID uuid.UUID) (*domain.PantryShoppingListStats, error) {
	var stats domain.PantryShoppingListStats
	stats.PantryID = pantryID

	// Count total shopping lists
	var totalShoppingListsCount int64
	if err := r.db.WithContext(ctx).Model(&domain.ShoppingList{}).
		Where("pantry_id = ?", pantryID).
		Count(&totalShoppingListsCount).Error; err != nil {
		return nil, err
	}
	stats.TotalShoppingLists = int(totalShoppingListsCount)

	// Count by status
	var activeCount, completedCount, archivedCount int64

	if err := r.db.WithContext(ctx).Model(&domain.ShoppingList{}).
		Where("pantry_id = ? AND status = ?", pantryID, domain.ShoppingListStatusActive).
		Count(&activeCount).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&domain.ShoppingList{}).
		Where("pantry_id = ? AND status = ?", pantryID, domain.ShoppingListStatusCompleted).
		Count(&completedCount).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&domain.ShoppingList{}).
		Where("pantry_id = ? AND status = ?", pantryID, domain.ShoppingListStatusArchived).
		Count(&archivedCount).Error; err != nil {
		return nil, err
	}

	stats.ActiveShoppingLists = int(activeCount)
	stats.CompletedShoppingLists = int(completedCount)
	stats.ArchivedShoppingLists = int(archivedCount)

	// Count total items and purchased items across all shopping lists
	var totalItems, totalPurchasedItems int64

	if err := r.db.WithContext(ctx).Model(&domain.ShoppingListItemEntity{}).
		Joins("JOIN shopping_lists ON shopping_list_items.shopping_list_id = shopping_lists.id").
		Where("shopping_lists.pantry_id = ?", pantryID).
		Count(&totalItems).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&domain.ShoppingListItemEntity{}).
		Joins("JOIN shopping_lists ON shopping_list_items.shopping_list_id = shopping_lists.id").
		Where("shopping_lists.pantry_id = ? AND shopping_list_items.is_purchased = ?", pantryID, true).
		Count(&totalPurchasedItems).Error; err != nil {
		return nil, err
	}

	stats.TotalItems = int(totalItems)
	stats.TotalPurchasedItems = int(totalPurchasedItems)

	// Calculate overall completion rate
	if stats.TotalItems > 0 {
		stats.OverallCompletionRate = float64(stats.TotalPurchasedItems) / float64(stats.TotalItems) * 100.0
	}

	return &stats, nil
}

// Helper methods

func (r *ShoppingListRepository) applyFilters(query *gorm.DB, filters domain.ShoppingListFilters) *gorm.DB {
	if filters.Status != nil {
		query = query.Where("status = ?", *filters.Status)
	}

	if filters.CreatedBy != nil {
		query = query.Where("created_by = ?", *filters.CreatedBy)
	}

	return query
}

func (r *ShoppingListRepository) applySorting(query *gorm.DB, filters domain.ShoppingListFilters) *gorm.DB {
	sortBy := "created_at"
	if filters.SortBy != nil {
		sortBy = *filters.SortBy
	}

	sortOrder := "DESC"
	if filters.SortOrder != nil {
		sortOrder = strings.ToUpper(*filters.SortOrder)
		if sortOrder != "ASC" && sortOrder != "DESC" {
			sortOrder = "DESC"
		}
	}

	// Validate sort field
	validSortFields := map[string]bool{
		"created_at": true,
		"updated_at": true,
		"name":       true,
	}

	if !validSortFields[sortBy] {
		sortBy = "created_at"
	}

	return query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
}

func (r *ShoppingListRepository) applyPagination(query *gorm.DB, filters domain.ShoppingListFilters) *gorm.DB {
	if filters.Limit != nil && *filters.Limit > 0 {
		query = query.Limit(*filters.Limit)
	}

	if filters.Offset != nil && *filters.Offset > 0 {
		query = query.Offset(*filters.Offset)
	}

	return query
}
