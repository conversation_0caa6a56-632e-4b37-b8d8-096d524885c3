package postgres

import (
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// PantryLocationRepository implements domain.PantryLocationRepository using GORM
type PantryLocationRepository struct {
	db *gorm.DB
}

// NewPantryLocationRepository creates a new pantry location repository
func NewPantryLocationRepository(db *gorm.DB) *PantryLocationRepository {
	return &PantryLocationRepository{db: db}
}

// Create creates a new pantry location
func (r *PantryLocationRepository) Create(location *domain.PantryLocation) error {
	model := &PantryLocationModel{}
	model.FromDomain(location)
	
	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "Location with this name already exists in the pantry")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create pantry location")
	}
	
	// Update the domain object with generated values
	*location = *model.ToDomain()
	
	return nil
}

// GetByID retrieves a location by ID
func (r *PantryLocationRepository) GetByID(id uuid.UUID) (*domain.PantryLocation, error) {
	var model PantryLocationModel
	
	if err := r.db.Where("pantry_location_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "pantry location not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pantry location by ID")
	}
	
	return model.ToDomain(), nil
}

// GetByPantry retrieves all locations for a pantry
func (r *PantryLocationRepository) GetByPantry(pantryID uuid.UUID) ([]*domain.PantryLocation, error) {
	var models []PantryLocationModel
	
	if err := r.db.Where("pantry_id = ?", pantryID).
		Order("name ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pantry locations")
	}
	
	locations := make([]*domain.PantryLocation, len(models))
	for i, model := range models {
		locations[i] = model.ToDomain()
	}
	
	return locations, nil
}

// Update updates an existing location
func (r *PantryLocationRepository) Update(location *domain.PantryLocation) error {
	model := &PantryLocationModel{}
	model.FromDomain(location)
	
	result := r.db.Model(&PantryLocationModel{}).
		Where("pantry_location_id = ?", location.ID).
		Updates(model)
	
	if result.Error != nil {
		if isDuplicateKeyError(result.Error) {
			return errors.New(errors.ErrCodeAlreadyExists, "Location with this name already exists in the pantry")
		}
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update pantry location")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "pantry location not found")
	}
	
	return nil
}

// Delete soft deletes a location
func (r *PantryLocationRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&PantryLocationModel{}, "pantry_location_id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete pantry location")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "pantry location not found")
	}
	
	return nil
}

// ExistsByName checks if a location with the given name exists in a pantry
func (r *PantryLocationRepository) ExistsByName(pantryID uuid.UUID, name string) (bool, error) {
	var count int64
	
	if err := r.db.Model(&PantryLocationModel{}).
		Where("pantry_id = ? AND name = ?", pantryID, name).
		Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check location name existence")
	}
	
	return count > 0, nil
}
