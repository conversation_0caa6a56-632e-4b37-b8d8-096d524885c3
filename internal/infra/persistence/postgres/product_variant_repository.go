package postgres

import (
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// ProductVariantRepository implements domain.ProductVariantRepository using GORM
type ProductVariantRepository struct {
	db *gorm.DB
}

// NewProductVariantRepository creates a new product variant repository
func NewProductVariantRepository(db *gorm.DB) *ProductVariantRepository {
	return &ProductVariantRepository{db: db}
}

// Create creates a new product variant
func (r *ProductVariantRepository) Create(variant *domain.ProductVariant) error {
	model := &ProductVariantModel{}
	model.FromDomain(variant)
	
	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "Product variant with this name or barcode already exists")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create product variant")
	}
	
	// Update the domain object with generated values
	*variant = *model.ToDomain()
	
	return nil
}

// GetByID retrieves a product variant by ID
func (r *ProductVariantRepository) GetByID(id uuid.UUID) (*domain.ProductVariant, error) {
	var model ProductVariantModel
	
	if err := r.db.Where("variant_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "product variant not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get product variant by ID")
	}
	
	return model.ToDomain(), nil
}

// GetByProductID retrieves all variants for a product
func (r *ProductVariantRepository) GetByProductID(productID uuid.UUID) ([]*domain.ProductVariant, error) {
	var models []ProductVariantModel
	
	if err := r.db.Where("product_id = ?", productID).
		Order("name ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get product variants by product ID")
	}
	
	variants := make([]*domain.ProductVariant, len(models))
	for i, model := range models {
		variants[i] = model.ToDomain()
	}
	
	return variants, nil
}

// GetByBarcode retrieves a product variant by barcode
func (r *ProductVariantRepository) GetByBarcode(barcodeGTIN string) (*domain.ProductVariant, error) {
	var model ProductVariantModel
	
	if err := r.db.Where("barcode_gtin = ?", barcodeGTIN).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "product variant not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get product variant by barcode")
	}
	
	return model.ToDomain(), nil
}

// SearchVariants searches for product variants by query with optional product filter
func (r *ProductVariantRepository) SearchVariants(query string, productID *uuid.UUID, page, limit int) ([]*domain.ProductVariant, int64, error) {
	var models []ProductVariantModel
	var total int64
	
	// Build search query
	searchQuery := r.db.Model(&ProductVariantModel{})
	
	if query != "" {
		searchTerm := "%" + strings.ToLower(query) + "%"
		searchQuery = searchQuery.Where(
			"LOWER(name) LIKE ? OR LOWER(description) LIKE ? OR barcode_gtin LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}
	
	if productID != nil {
		searchQuery = searchQuery.Where("product_id = ?", *productID)
	}
	
	// Count total records
	if err := searchQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count search results")
	}
	
	// Get paginated records
	offset := (page - 1) * limit
	if err := searchQuery.Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to search product variants")
	}
	
	variants := make([]*domain.ProductVariant, len(models))
	for i, model := range models {
		variants[i] = model.ToDomain()
	}
	
	return variants, total, nil
}

// Update updates an existing product variant
func (r *ProductVariantRepository) Update(variant *domain.ProductVariant) error {
	model := &ProductVariantModel{}
	model.FromDomain(variant)
	
	result := r.db.Model(&ProductVariantModel{}).
		Where("variant_id = ?", variant.ID).
		Updates(model)
	
	if result.Error != nil {
		if isDuplicateKeyError(result.Error) {
			return errors.New(errors.ErrCodeAlreadyExists, "Product variant with this name or barcode already exists")
		}
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update product variant")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "product variant not found")
	}
	
	return nil
}

// Delete soft deletes a product variant
func (r *ProductVariantRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&ProductVariantModel{}, "variant_id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete product variant")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "product variant not found")
	}
	
	return nil
}

// ExistsByProductAndName checks if a variant with the given name exists for a product
func (r *ProductVariantRepository) ExistsByProductAndName(productID uuid.UUID, name string) (bool, error) {
	var count int64
	
	if err := r.db.Model(&ProductVariantModel{}).
		Where("product_id = ? AND name = ?", productID, name).
		Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check variant name existence")
	}
	
	return count > 0, nil
}

// ExistsByBarcode checks if a variant with the given barcode exists
func (r *ProductVariantRepository) ExistsByBarcode(barcodeGTIN string) (bool, error) {
	var count int64
	
	if err := r.db.Model(&ProductVariantModel{}).
		Where("barcode_gtin = ?", barcodeGTIN).
		Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check barcode existence")
	}
	
	return count > 0, nil
}
