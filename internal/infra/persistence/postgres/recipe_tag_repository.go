package postgres

import (
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// RecipeTagRepository implements the recipe tag repository using GORM
type RecipeTagRepository struct {
	db *gorm.DB
}

// NewRecipeTagRepository creates a new recipe tag repository
func NewRecipeTagRepository(db *gorm.DB) domain.RecipeTagRepository {
	return &RecipeTagRepository{db: db}
}

// Create creates a new recipe tag
func (r *RecipeTagRepository) Create(tag *domain.RecipeTag) error {
	return r.db.Create(tag).Error
}

// GetByID retrieves a recipe tag by ID
func (r *RecipeTagRepository) GetByID(id uuid.UUID) (*domain.RecipeTag, error) {
	var tag domain.RecipeTag
	err := r.db.Where("id = ?", id).First(&tag).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("recipe tag not found")
		}
		return nil, err
	}
	return &tag, nil
}

// GetByName retrieves a recipe tag by name
func (r *RecipeTagRepository) GetByName(name string) (*domain.RecipeTag, error) {
	var tag domain.RecipeTag
	err := r.db.Where("name = ?", name).First(&tag).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("recipe tag not found")
		}
		return nil, err
	}
	return &tag, nil
}

// GetAll retrieves all recipe tags
func (r *RecipeTagRepository) GetAll() ([]*domain.RecipeTag, error) {
	var tags []*domain.RecipeTag
	err := r.db.Order("name ASC").Find(&tags).Error
	if err != nil {
		return nil, err
	}
	return tags, nil
}

// GetPopular retrieves popular recipe tags
func (r *RecipeTagRepository) GetPopular(limit int) ([]*domain.RecipeTag, error) {
	var tags []*domain.RecipeTag
	err := r.db.Order("usage_count DESC, name ASC").Limit(limit).Find(&tags).Error
	if err != nil {
		return nil, err
	}
	return tags, nil
}

// Update updates a recipe tag
func (r *RecipeTagRepository) Update(tag *domain.RecipeTag) error {
	return r.db.Save(tag).Error
}

// Delete deletes a recipe tag
func (r *RecipeTagRepository) Delete(id uuid.UUID) error {
	return r.db.Delete(&domain.RecipeTag{}, id).Error
}

// IncrementUsage increments the usage count for a tag
func (r *RecipeTagRepository) IncrementUsage(id uuid.UUID) error {
	return r.db.Model(&domain.RecipeTag{}).Where("id = ?", id).
		UpdateColumn("usage_count", gorm.Expr("usage_count + 1")).Error
}

// DecrementUsage decrements the usage count for a tag
func (r *RecipeTagRepository) DecrementUsage(id uuid.UUID) error {
	return r.db.Model(&domain.RecipeTag{}).Where("id = ?", id).
		UpdateColumn("usage_count", gorm.Expr("GREATEST(usage_count - 1, 0)")).Error
}
