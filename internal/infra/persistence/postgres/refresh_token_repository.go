package postgres

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// RefreshTokenRepository implements domain.RefreshTokenRepository using GORM
type RefreshTokenRepository struct {
	db *gorm.DB
}

// NewRefreshTokenRepository creates a new refresh token repository
func NewRefreshTokenRepository(db *gorm.DB) *RefreshTokenRepository {
	return &RefreshTokenRepository{db: db}
}

// Create creates a new refresh token
func (r *RefreshTokenRepository) Create(token *domain.RefreshToken) error {
	model := &RefreshTokenModel{}
	model.FromDomain(token)
	
	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "refresh token already exists")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create refresh token")
	}
	
	// Update the domain object with generated values
	*token = *model.ToDomain()
	
	return nil
}

// GetByTokenHash retrieves a refresh token by its hash
func (r *RefreshTokenRepository) GetByTokenHash(tokenHash string) (*domain.RefreshToken, error) {
	var model RefreshTokenModel
	
	if err := r.db.Where("token_hash = ?", tokenHash).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "refresh token not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get refresh token by hash")
	}
	
	return model.ToDomain(), nil
}

// GetByUserID retrieves all refresh tokens for a user
func (r *RefreshTokenRepository) GetByUserID(userID uuid.UUID) ([]*domain.RefreshToken, error) {
	var models []RefreshTokenModel
	
	if err := r.db.Where("user_id = ?", userID).Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get refresh tokens by user ID")
	}
	
	tokens := make([]*domain.RefreshToken, len(models))
	for i, model := range models {
		tokens[i] = model.ToDomain()
	}
	
	return tokens, nil
}

// Update updates an existing refresh token
func (r *RefreshTokenRepository) Update(token *domain.RefreshToken) error {
	model := &RefreshTokenModel{}
	model.FromDomain(token)
	
	result := r.db.Model(&RefreshTokenModel{}).Where("id = ?", token.ID).Updates(model)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update refresh token")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "refresh token not found")
	}
	
	return nil
}

// RevokeByUserID revokes all refresh tokens for a user
func (r *RefreshTokenRepository) RevokeByUserID(userID uuid.UUID) error {
	result := r.db.Model(&RefreshTokenModel{}).
		Where("user_id = ? AND is_revoked = false", userID).
		Update("is_revoked", true)
	
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to revoke refresh tokens by user ID")
	}
	
	return nil
}

// RevokeByTokenHash revokes a specific refresh token
func (r *RefreshTokenRepository) RevokeByTokenHash(tokenHash string) error {
	result := r.db.Model(&RefreshTokenModel{}).
		Where("token_hash = ? AND is_revoked = false", tokenHash).
		Update("is_revoked", true)
	
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to revoke refresh token by hash")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "refresh token not found or already revoked")
	}
	
	return nil
}

// DeleteExpired deletes all expired refresh tokens
func (r *RefreshTokenRepository) DeleteExpired() error {
	result := r.db.Where("expires_at < ?", time.Now()).Delete(&RefreshTokenModel{})
	
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete expired refresh tokens")
	}
	
	return nil
}
