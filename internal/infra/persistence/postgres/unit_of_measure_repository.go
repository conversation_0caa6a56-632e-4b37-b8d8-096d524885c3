package postgres

import (
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// UnitOfMeasureRepository implements domain.UnitOfMeasureRepository using GORM
type UnitOfMeasureRepository struct {
	db *gorm.DB
}

// NewUnitOfMeasureRepository creates a new unit of measure repository
func NewUnitOfMeasureRepository(db *gorm.DB) *UnitOfMeasureRepository {
	return &UnitOfMeasureRepository{db: db}
}

// Create creates a new unit of measure
func (r *UnitOfMeasureRepository) Create(unit *domain.UnitOfMeasure) error {
	model := &UnitOfMeasureModel{}
	model.FromDomain(unit)
	
	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "Unit with this name or symbol already exists")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create unit of measure")
	}
	
	// Update the domain object with generated values
	*unit = *model.ToDomain()
	
	return nil
}

// GetByID retrieves a unit by ID
func (r *UnitOfMeasureRepository) GetByID(id uuid.UUID) (*domain.UnitOfMeasure, error) {
	var model UnitOfMeasureModel
	
	if err := r.db.Where("unit_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "unit of measure not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get unit of measure by ID")
	}
	
	return model.ToDomain(), nil
}

// GetByName retrieves a unit by name
func (r *UnitOfMeasureRepository) GetByName(name string) (*domain.UnitOfMeasure, error) {
	var model UnitOfMeasureModel
	
	if err := r.db.Where("name = ?", name).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "unit of measure not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get unit of measure by name")
	}
	
	return model.ToDomain(), nil
}

// GetBySymbol retrieves a unit by symbol
func (r *UnitOfMeasureRepository) GetBySymbol(symbol string) (*domain.UnitOfMeasure, error) {
	var model UnitOfMeasureModel
	
	if err := r.db.Where("symbol = ?", symbol).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "unit of measure not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get unit of measure by symbol")
	}
	
	return model.ToDomain(), nil
}

// GetByType retrieves all units of a specific type
func (r *UnitOfMeasureRepository) GetByType(unitType domain.UnitOfMeasureType) ([]*domain.UnitOfMeasure, error) {
	var models []UnitOfMeasureModel
	
	if err := r.db.Where("type = ?", string(unitType)).
		Order("name ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get units by type")
	}
	
	units := make([]*domain.UnitOfMeasure, len(models))
	for i, model := range models {
		units[i] = model.ToDomain()
	}
	
	return units, nil
}

// GetBaseUnits retrieves all base units
func (r *UnitOfMeasureRepository) GetBaseUnits() ([]*domain.UnitOfMeasure, error) {
	var models []UnitOfMeasureModel
	
	if err := r.db.Where("is_base_unit = ?", true).
		Order("type ASC, name ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get base units")
	}
	
	units := make([]*domain.UnitOfMeasure, len(models))
	for i, model := range models {
		units[i] = model.ToDomain()
	}
	
	return units, nil
}

// GetDerivedUnits retrieves all derived units for a base unit
func (r *UnitOfMeasureRepository) GetDerivedUnits(baseUnitID uuid.UUID) ([]*domain.UnitOfMeasure, error) {
	var models []UnitOfMeasureModel
	
	if err := r.db.Where("base_unit_id = ?", baseUnitID).
		Order("name ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get derived units")
	}
	
	units := make([]*domain.UnitOfMeasure, len(models))
	for i, model := range models {
		units[i] = model.ToDomain()
	}
	
	return units, nil
}

// GetAllUnits retrieves all units
func (r *UnitOfMeasureRepository) GetAllUnits() ([]*domain.UnitOfMeasure, error) {
	var models []UnitOfMeasureModel
	
	if err := r.db.Order("type ASC, name ASC").Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get all units")
	}
	
	units := make([]*domain.UnitOfMeasure, len(models))
	for i, model := range models {
		units[i] = model.ToDomain()
	}
	
	return units, nil
}

// Update updates an existing unit
func (r *UnitOfMeasureRepository) Update(unit *domain.UnitOfMeasure) error {
	model := &UnitOfMeasureModel{}
	model.FromDomain(unit)
	
	result := r.db.Model(&UnitOfMeasureModel{}).
		Where("unit_id = ?", unit.ID).
		Updates(model)
	
	if result.Error != nil {
		if isDuplicateKeyError(result.Error) {
			return errors.New(errors.ErrCodeAlreadyExists, "Unit with this name or symbol already exists")
		}
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update unit of measure")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "unit of measure not found")
	}
	
	return nil
}

// Delete soft deletes a unit
func (r *UnitOfMeasureRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&UnitOfMeasureModel{}, "unit_id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete unit of measure")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "unit of measure not found")
	}
	
	return nil
}

// ExistsByName checks if a unit with the given name exists
func (r *UnitOfMeasureRepository) ExistsByName(name string) (bool, error) {
	var count int64
	
	if err := r.db.Model(&UnitOfMeasureModel{}).
		Where("name = ?", name).
		Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check unit name existence")
	}
	
	return count > 0, nil
}

// ExistsBySymbol checks if a unit with the given symbol exists
func (r *UnitOfMeasureRepository) ExistsBySymbol(symbol string) (bool, error) {
	var count int64
	
	if err := r.db.Model(&UnitOfMeasureModel{}).
		Where("symbol = ?", symbol).
		Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check unit symbol existence")
	}
	
	return count > 0, nil
}
