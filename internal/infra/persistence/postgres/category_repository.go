package postgres

import (
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// CategoryRepository implements domain.CategoryRepository using GORM
type CategoryRepository struct {
	db *gorm.DB
}

// NewCategoryRepository creates a new category repository
func NewCategoryRepository(db *gorm.DB) *CategoryRepository {
	return &CategoryRepository{db: db}
}

// Create creates a new category
func (r *CategoryRepository) Create(category *domain.Category) error {
	model := &CategoryModel{}
	model.FromDomain(category)
	
	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "Category with this name already exists")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create category")
	}
	
	// Update the domain object with generated values
	*category = *model.ToDomain()
	
	return nil
}

// GetByID retrieves a category by ID
func (r *CategoryRepository) GetByID(id uuid.UUID) (*domain.Category, error) {
	var model CategoryModel
	
	if err := r.db.Where("category_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "category not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get category by ID")
	}
	
	return model.ToDomain(), nil
}

// GetByName retrieves a category by name
func (r *CategoryRepository) GetByName(name string) (*domain.Category, error) {
	var model CategoryModel
	
	if err := r.db.Where("name = ?", name).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "category not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get category by name")
	}
	
	return model.ToDomain(), nil
}

// GetTopLevelCategories retrieves all top-level categories (no parent)
func (r *CategoryRepository) GetTopLevelCategories() ([]*domain.Category, error) {
	var models []CategoryModel
	
	if err := r.db.Where("parent_category_id IS NULL").
		Order("name ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get top-level categories")
	}
	
	categories := make([]*domain.Category, len(models))
	for i, model := range models {
		categories[i] = model.ToDomain()
	}
	
	return categories, nil
}

// GetSubCategories retrieves all subcategories of a parent category
func (r *CategoryRepository) GetSubCategories(parentID uuid.UUID) ([]*domain.Category, error) {
	var models []CategoryModel
	
	if err := r.db.Where("parent_category_id = ?", parentID).
		Order("name ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get subcategories")
	}
	
	categories := make([]*domain.Category, len(models))
	for i, model := range models {
		categories[i] = model.ToDomain()
	}
	
	return categories, nil
}

// GetAllCategories retrieves all categories
func (r *CategoryRepository) GetAllCategories() ([]*domain.Category, error) {
	var models []CategoryModel
	
	if err := r.db.Order("name ASC").Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get all categories")
	}
	
	categories := make([]*domain.Category, len(models))
	for i, model := range models {
		categories[i] = model.ToDomain()
	}
	
	return categories, nil
}

// Update updates an existing category
func (r *CategoryRepository) Update(category *domain.Category) error {
	model := &CategoryModel{}
	model.FromDomain(category)
	
	result := r.db.Model(&CategoryModel{}).
		Where("category_id = ?", category.ID).
		Updates(model)
	
	if result.Error != nil {
		if isDuplicateKeyError(result.Error) {
			return errors.New(errors.ErrCodeAlreadyExists, "Category with this name already exists")
		}
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update category")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "category not found")
	}
	
	return nil
}

// Delete soft deletes a category
func (r *CategoryRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&CategoryModel{}, "category_id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete category")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "category not found")
	}
	
	return nil
}

// ExistsByName checks if a category with the given name exists
func (r *CategoryRepository) ExistsByName(name string, parentID *uuid.UUID) (bool, error) {
	var count int64
	
	query := r.db.Model(&CategoryModel{}).Where("name = ?", name)
	
	if parentID != nil {
		query = query.Where("parent_category_id = ?", *parentID)
	} else {
		query = query.Where("parent_category_id IS NULL")
	}
	
	if err := query.Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check category name existence")
	}
	
	return count > 0, nil
}

// GetCategoryPath returns the path from root to the specified category
func (r *CategoryRepository) GetCategoryPath(categoryID uuid.UUID) ([]*domain.Category, error) {
	var path []*domain.Category
	currentID := &categoryID
	
	// Traverse up the hierarchy
	for currentID != nil {
		var model CategoryModel
		
		if err := r.db.Where("category_id = ?", *currentID).First(&model).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, errors.New(errors.ErrCodeNotFound, "category not found in path")
			}
			return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get category in path")
		}
		
		// Prepend to path (so root comes first)
		category := model.ToDomain()
		path = append([]*domain.Category{category}, path...)
		
		// Move to parent
		currentID = model.ParentCategoryID
	}
	
	return path, nil
}
