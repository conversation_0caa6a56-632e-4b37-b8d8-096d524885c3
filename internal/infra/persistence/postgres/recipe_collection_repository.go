package postgres

import (
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// RecipeCollectionRepository implements the recipe collection repository using GORM
type RecipeCollectionRepository struct {
	db *gorm.DB
}

// NewRecipeCollectionRepository creates a new recipe collection repository
func NewRecipeCollectionRepository(db *gorm.DB) domain.RecipeCollectionRepository {
	return &RecipeCollectionRepository{db: db}
}

// Create creates a new recipe collection
func (r *RecipeCollectionRepository) Create(collection *domain.RecipeCollection) error {
	return r.db.Create(collection).Error
}

// GetByID retrieves a recipe collection by ID
func (r *RecipeCollectionRepository) GetByID(id uuid.UUID) (*domain.RecipeCollection, error) {
	var collection domain.RecipeCollection
	err := r.db.Preload("User").Preload("Recipes").Where("id = ?", id).First(&collection).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("recipe collection not found")
		}
		return nil, err
	}
	return &collection, nil
}

// GetByUserID retrieves collections for a user with pagination
func (r *RecipeCollectionRepository) GetByUserID(userID uuid.UUID, page, limit int) ([]*domain.RecipeCollection, int64, error) {
	var collections []*domain.RecipeCollection
	var total int64

	// Count total
	if err := r.db.Model(&domain.RecipeCollection{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	err := r.db.Where("user_id = ?", userID).
		Preload("Recipes").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&collections).Error

	if err != nil {
		return nil, 0, err
	}

	return collections, total, nil
}

// GetPublicCollections retrieves public collections with pagination
func (r *RecipeCollectionRepository) GetPublicCollections(page, limit int) ([]*domain.RecipeCollection, int64, error) {
	var collections []*domain.RecipeCollection
	var total int64

	// Count total
	if err := r.db.Model(&domain.RecipeCollection{}).Where("is_public = ?", true).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	err := r.db.Where("is_public = ?", true).
		Preload("User").
		Preload("Recipes").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&collections).Error

	if err != nil {
		return nil, 0, err
	}

	return collections, total, nil
}

// Update updates a recipe collection
func (r *RecipeCollectionRepository) Update(collection *domain.RecipeCollection) error {
	return r.db.Save(collection).Error
}

// Delete deletes a recipe collection
func (r *RecipeCollectionRepository) Delete(id uuid.UUID) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Delete collection items first
		if err := tx.Where("collection_id = ?", id).Delete(&domain.RecipeCollectionItem{}).Error; err != nil {
			return err
		}

		// Delete the collection
		return tx.Delete(&domain.RecipeCollection{}, id).Error
	})
}

// AddRecipe adds a recipe to a collection
func (r *RecipeCollectionRepository) AddRecipe(collectionID, recipeID uuid.UUID, order int, notes *string) error {
	item := &domain.RecipeCollectionItem{
		ID:           uuid.New(),
		CollectionID: collectionID,
		RecipeID:     recipeID,
		Order:        order,
		Notes:        notes,
	}

	return r.db.Create(item).Error
}

// RemoveRecipe removes a recipe from a collection
func (r *RecipeCollectionRepository) RemoveRecipe(collectionID, recipeID uuid.UUID) error {
	return r.db.Where("collection_id = ? AND recipe_id = ?", collectionID, recipeID).
		Delete(&domain.RecipeCollectionItem{}).Error
}

// GetCollectionRecipes retrieves recipes in a collection with pagination
func (r *RecipeCollectionRepository) GetCollectionRecipes(collectionID uuid.UUID, page, limit int) ([]*domain.Recipe, int64, error) {
	var recipes []*domain.Recipe
	var total int64

	// Count total
	if err := r.db.Model(&domain.Recipe{}).
		Joins("JOIN recipe_collection_items ON recipes.id = recipe_collection_items.recipe_id").
		Where("recipe_collection_items.collection_id = ?", collectionID).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	err := r.db.Joins("JOIN recipe_collection_items ON recipes.id = recipe_collection_items.recipe_id").
		Where("recipe_collection_items.collection_id = ?", collectionID).
		Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Order("recipe_collection_items.order ASC, recipe_collection_items.added_at ASC").
		Offset(offset).
		Limit(limit).
		Find(&recipes).Error

	if err != nil {
		return nil, 0, err
	}

	return recipes, total, nil
}
