package postgres

import (
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// StoreRepository implements domain.StoreRepository
type StoreRepository struct {
	db *gorm.DB
}

// NewStoreRepository creates a new StoreRepository
func NewStoreRepository(db *gorm.DB) *StoreRepository {
	return &StoreRepository{db: db}
}

// Create saves a new store to the database
func (r *StoreRepository) Create(store *domain.Store) error {
	model := &StoreModel{}
	model.FromDomain(store)

	if err := r.db.Create(model).Error; err != nil {
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to create store")
	}

	return nil
}

// GetByID retrieves a store by ID
func (r *StoreRepository) GetByID(id uuid.UUID) (*domain.Store, error) {
	var model StoreModel
	if err := r.db.First(&model, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "Store not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to get store")
	}

	return model.ToDomain(), nil
}

// GetAll retrieves all stores with pagination
func (r *StoreRepository) GetAll(page, limit int) ([]*domain.Store, int64, error) {
	var models []StoreModel
	var total int64

	// Get total count
	if err := r.db.Model(&StoreModel{}).Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to count stores")
	}

	// Get paginated results
	offset := (page - 1) * limit
	if err := r.db.Offset(offset).Limit(limit).Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to get stores")
	}

	stores := make([]*domain.Store, len(models))
	for i, model := range models {
		stores[i] = model.ToDomain()
	}

	return stores, total, nil
}

// Update updates an existing store
func (r *StoreRepository) Update(store *domain.Store) error {
	model := &StoreModel{}
	model.FromDomain(store)

	result := r.db.Save(model)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "Failed to update store")
	}
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "Store not found")
	}

	return nil
}

// Delete soft deletes a store
func (r *StoreRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&StoreModel{}, "id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "Failed to delete store")
	}
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "Store not found")
	}

	return nil
}

// SearchByName searches for stores by name
func (r *StoreRepository) SearchByName(name string) ([]*domain.Store, error) {
	var models []StoreModel
	if err := r.db.Where("LOWER(name) LIKE LOWER(?)", "%"+name+"%").Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to search stores")
	}

	stores := make([]*domain.Store, len(models))
	for i, model := range models {
		stores[i] = model.ToDomain()
	}

	return stores, nil
}
