package postgres

import (
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// PantryMembershipRepository implements domain.PantryMembershipRepository using GORM
type PantryMembershipRepository struct {
	db *gorm.DB
}

// NewPantryMembershipRepository creates a new pantry membership repository
func NewPantryMembershipRepository(db *gorm.DB) *PantryMembershipRepository {
	return &PantryMembershipRepository{db: db}
}

// Create creates a new pantry membership
func (r *PantryMembershipRepository) Create(membership *domain.PantryMembership) error {
	model := &PantryMembershipModel{}
	model.FromDomain(membership)
	
	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "User is already a member of this pantry")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create pantry membership")
	}
	
	// Update the domain object with generated values
	*membership = *model.ToDomain()
	
	return nil
}

// GetByID retrieves a membership by ID
func (r *PantryMembershipRepository) GetByID(id uuid.UUID) (*domain.PantryMembership, error) {
	var model PantryMembershipModel
	
	if err := r.db.Where("pantry_membership_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "pantry membership not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pantry membership by ID")
	}
	
	return model.ToDomain(), nil
}

// GetByPantryAndUser retrieves a membership by pantry and user
func (r *PantryMembershipRepository) GetByPantryAndUser(pantryID, userID uuid.UUID) (*domain.PantryMembership, error) {
	var model PantryMembershipModel
	
	if err := r.db.Where("pantry_id = ? AND user_id = ?", pantryID, userID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "pantry membership not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pantry membership")
	}
	
	return model.ToDomain(), nil
}

// GetPantryMembers retrieves all members of a pantry
func (r *PantryMembershipRepository) GetPantryMembers(pantryID uuid.UUID, page, limit int) ([]*domain.PantryMembership, int64, error) {
	var models []PantryMembershipModel
	var total int64
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Count total
	if err := r.db.Model(&PantryMembershipModel{}).
		Where("pantry_id = ? AND status != 'removed'", pantryID).
		Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count pantry members")
	}
	
	// Get paginated results
	if err := r.db.Where("pantry_id = ? AND status != 'removed'", pantryID).
		Offset(offset).Limit(limit).
		Order("joined_at ASC").
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pantry members")
	}
	
	memberships := make([]*domain.PantryMembership, len(models))
	for i, model := range models {
		memberships[i] = model.ToDomain()
	}
	
	return memberships, total, nil
}

// GetUserMemberships retrieves all memberships for a user
func (r *PantryMembershipRepository) GetUserMemberships(userID uuid.UUID, page, limit int) ([]*domain.PantryMembership, int64, error) {
	var models []PantryMembershipModel
	var total int64
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Count total
	if err := r.db.Model(&PantryMembershipModel{}).
		Where("user_id = ? AND status != 'removed'", userID).
		Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count user memberships")
	}
	
	// Get paginated results
	if err := r.db.Where("user_id = ? AND status != 'removed'", userID).
		Offset(offset).Limit(limit).
		Order("joined_at DESC").
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get user memberships")
	}
	
	memberships := make([]*domain.PantryMembership, len(models))
	for i, model := range models {
		memberships[i] = model.ToDomain()
	}
	
	return memberships, total, nil
}

// GetPendingInvitations retrieves pending invitations for a user
func (r *PantryMembershipRepository) GetPendingInvitations(userID uuid.UUID) ([]*domain.PantryMembership, error) {
	var models []PantryMembershipModel
	
	if err := r.db.Where("user_id = ? AND status = 'pending_invitation'", userID).
		Order("joined_at DESC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pending invitations")
	}
	
	memberships := make([]*domain.PantryMembership, len(models))
	for i, model := range models {
		memberships[i] = model.ToDomain()
	}
	
	return memberships, nil
}

// Update updates an existing membership
func (r *PantryMembershipRepository) Update(membership *domain.PantryMembership) error {
	model := &PantryMembershipModel{}
	model.FromDomain(membership)
	
	result := r.db.Model(&PantryMembershipModel{}).
		Where("pantry_membership_id = ?", membership.ID).
		Updates(model)
	
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update pantry membership")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "pantry membership not found")
	}
	
	return nil
}

// Delete removes a membership
func (r *PantryMembershipRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&PantryMembershipModel{}, "pantry_membership_id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete pantry membership")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "pantry membership not found")
	}
	
	return nil
}

// ExistsActiveMembership checks if an active membership exists
func (r *PantryMembershipRepository) ExistsActiveMembership(pantryID, userID uuid.UUID) (bool, error) {
	var count int64
	
	if err := r.db.Model(&PantryMembershipModel{}).
		Where("pantry_id = ? AND user_id = ? AND status = 'active'", pantryID, userID).
		Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check active membership existence")
	}
	
	return count > 0, nil
}
