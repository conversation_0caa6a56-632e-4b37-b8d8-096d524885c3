package postgres

import (
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// InventoryItemRepository implements domain.InventoryItemRepository using GORM
type InventoryItemRepository struct {
	db *gorm.DB
}

// NewInventoryItemRepository creates a new inventory item repository
func NewInventoryItemRepository(db *gorm.DB) *InventoryItemRepository {
	return &InventoryItemRepository{db: db}
}

// Create creates a new inventory item
func (r *InventoryItemRepository) Create(item *domain.InventoryItem) error {
	model := &InventoryItemModel{}
	model.FromDomain(item)

	if err := r.db.Create(model).Error; err != nil {
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create inventory item")
	}

	// Update the domain object with generated values
	*item = *model.ToDomain()

	return nil
}

// GetByID retrieves an inventory item by ID
func (r *InventoryItemRepository) GetByID(id uuid.UUID) (*domain.InventoryItem, error) {
	var model InventoryItemModel

	if err := r.db.Where("item_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "inventory item not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory item by ID")
	}

	return model.ToDomain(), nil
}

// GetByPantryID retrieves inventory items by pantry ID with pagination
func (r *InventoryItemRepository) GetByPantryID(pantryID uuid.UUID, page, limit int) ([]*domain.InventoryItem, int64, error) {
	var models []InventoryItemModel
	var total int64

	// Count total records
	if err := r.db.Model(&InventoryItemModel{}).
		Where("pantry_id = ?", pantryID).
		Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count inventory items by pantry")
	}

	// Get paginated records
	offset := (page - 1) * limit
	if err := r.db.Where("pantry_id = ?", pantryID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory items by pantry")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, total, nil
}

// GetByLocationID retrieves inventory items by location ID
func (r *InventoryItemRepository) GetByLocationID(locationID uuid.UUID) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	if err := r.db.Where("location_id = ?", locationID).
		Order("created_at DESC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory items by location")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// GetByProductVariantID retrieves inventory items by product variant ID
func (r *InventoryItemRepository) GetByProductVariantID(productVariantID uuid.UUID) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	if err := r.db.Where("product_variant_id = ?", productVariantID).
		Order("created_at DESC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get inventory items by product variant")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// GetExpiringItems retrieves items expiring within the specified number of days
func (r *InventoryItemRepository) GetExpiringItems(pantryID uuid.UUID, days int) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	thresholdDate := time.Now().AddDate(0, 0, days)

	if err := r.db.Where("pantry_id = ? AND expiration_date IS NOT NULL AND expiration_date <= ? AND quantity > 0",
		pantryID, thresholdDate).
		Order("expiration_date ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get expiring items")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// GetLowStockItems retrieves items with low stock (quantity < 1.0)
func (r *InventoryItemRepository) GetLowStockItems(pantryID uuid.UUID) ([]*domain.InventoryItem, error) {
	var models []InventoryItemModel

	if err := r.db.Where("pantry_id = ? AND quantity < ? AND quantity > 0", pantryID, 1.0).
		Order("quantity ASC").
		Find(&models).Error; err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get low stock items")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, nil
}

// SearchItems searches for inventory items by query with pagination
func (r *InventoryItemRepository) SearchItems(pantryID uuid.UUID, query string, page, limit int) ([]*domain.InventoryItem, int64, error) {
	var models []InventoryItemModel
	var total int64

	// Build search query
	searchQuery := r.db.Model(&InventoryItemModel{}).
		Joins("LEFT JOIN product_variants ON inventory_items.product_variant_id = product_variants.variant_id").
		Joins("LEFT JOIN products ON product_variants.product_id = products.product_id").
		Where("inventory_items.pantry_id = ?", pantryID)

	if query != "" {
		searchTerm := "%" + strings.ToLower(query) + "%"
		searchQuery = searchQuery.Where(
			"LOWER(products.name) LIKE ? OR LOWER(product_variants.name) LIKE ? OR LOWER(inventory_items.notes) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := searchQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count search results")
	}

	// Get paginated records
	offset := (page - 1) * limit
	if err := searchQuery.Select("inventory_items.*").
		Order("inventory_items.created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to search inventory items")
	}

	items := make([]*domain.InventoryItem, len(models))
	for i, model := range models {
		items[i] = model.ToDomain()
	}

	return items, total, nil
}

// Update updates an existing inventory item
func (r *InventoryItemRepository) Update(item *domain.InventoryItem) error {
	model := &InventoryItemModel{}
	model.FromDomain(item)

	result := r.db.Model(&InventoryItemModel{}).
		Where("item_id = ?", item.ID).
		Updates(model)

	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update inventory item")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "inventory item not found")
	}

	return nil
}

// Delete soft deletes an inventory item
func (r *InventoryItemRepository) Delete(id uuid.UUID) error {
	result := r.db.Where("item_id = ?", id).Delete(&InventoryItemModel{})

	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete inventory item")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "inventory item not found")
	}

	return nil
}

// GetTotalQuantityByProductVariant gets the total quantity of a product variant in a pantry
func (r *InventoryItemRepository) GetTotalQuantityByProductVariant(pantryID uuid.UUID, productVariantID uuid.UUID) (float64, error) {
	var totalQuantity float64

	if err := r.db.Model(&InventoryItemModel{}).
		Where("pantry_id = ? AND product_variant_id = ? AND quantity > 0", pantryID, productVariantID).
		Select("COALESCE(SUM(quantity), 0)").
		Scan(&totalQuantity).Error; err != nil {
		return 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get total quantity by product variant")
	}

	return totalQuantity, nil
}
