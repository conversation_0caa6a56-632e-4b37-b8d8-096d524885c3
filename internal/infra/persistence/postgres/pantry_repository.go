package postgres

import (
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// PantryRepository implements domain.PantryRepository using GORM
type PantryRepository struct {
	db *gorm.DB
}

// NewPantryRepository creates a new pantry repository
func NewPantryRepository(db *gorm.DB) *PantryRepository {
	return &PantryRepository{db: db}
}

// Create creates a new pantry
func (r *PantryRepository) Create(pantry *domain.Pantry) error {
	model := &PantryModel{}
	model.FromDomain(pantry)
	
	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "Pantry with this name already exists")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create pantry")
	}
	
	// Update the domain object with generated values
	*pantry = *model.ToDomain()
	
	return nil
}

// GetByID retrieves a pantry by ID
func (r *PantryRepository) GetByID(id uuid.UUID) (*domain.Pantry, error) {
	var model PantryModel
	
	if err := r.db.Where("pantry_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "pantry not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pantry by ID")
	}
	
	return model.ToDomain(), nil
}

// GetUserPantries retrieves all pantries for a user (owned + member)
func (r *PantryRepository) GetUserPantries(userID uuid.UUID, page, limit int) ([]*domain.Pantry, int64, error) {
	var models []PantryModel
	var total int64
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Query for pantries where user is owner or member
	query := r.db.Model(&PantryModel{}).
		Joins("LEFT JOIN pantry_memberships pm ON pantries.pantry_id = pm.pantry_id").
		Where("pantries.owner_user_id = ? OR (pm.user_id = ? AND pm.status = 'active' AND pm.deleted_at IS NULL)", userID, userID).
		Group("pantries.pantry_id")
	
	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count user pantries")
	}
	
	// Get paginated results
	if err := query.Offset(offset).Limit(limit).Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get user pantries")
	}
	
	pantries := make([]*domain.Pantry, len(models))
	for i, model := range models {
		pantries[i] = model.ToDomain()
	}
	
	return pantries, total, nil
}

// GetOwnedPantries retrieves pantries owned by a user
func (r *PantryRepository) GetOwnedPantries(userID uuid.UUID, page, limit int) ([]*domain.Pantry, int64, error) {
	var models []PantryModel
	var total int64
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Count total
	if err := r.db.Model(&PantryModel{}).Where("owner_user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count owned pantries")
	}
	
	// Get paginated results
	if err := r.db.Where("owner_user_id = ?", userID).
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get owned pantries")
	}
	
	pantries := make([]*domain.Pantry, len(models))
	for i, model := range models {
		pantries[i] = model.ToDomain()
	}
	
	return pantries, total, nil
}

// Update updates an existing pantry
func (r *PantryRepository) Update(pantry *domain.Pantry) error {
	model := &PantryModel{}
	model.FromDomain(pantry)
	
	result := r.db.Model(&PantryModel{}).Where("pantry_id = ?", pantry.ID).Updates(model)
	if result.Error != nil {
		if isDuplicateKeyError(result.Error) {
			return errors.New(errors.ErrCodeAlreadyExists, "Pantry with this name already exists")
		}
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update pantry")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "pantry not found")
	}
	
	return nil
}

// Delete soft deletes a pantry
func (r *PantryRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&PantryModel{}, "pantry_id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete pantry")
	}
	
	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "pantry not found")
	}
	
	return nil
}

// ExistsByName checks if a pantry with the given name exists for a user
func (r *PantryRepository) ExistsByName(ownerUserID uuid.UUID, name string) (bool, error) {
	var count int64
	
	if err := r.db.Model(&PantryModel{}).
		Where("owner_user_id = ? AND name = ?", ownerUserID, name).
		Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check pantry name existence")
	}
	
	return count > 0, nil
}
