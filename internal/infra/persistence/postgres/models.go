package postgres

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// UserModel represents the GORM model for users
type UserModel struct {
	ID                uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	Username          string         `gorm:"type:varchar(50);uniqueIndex;not null" json:"username"`
	Email             string         `gorm:"type:varchar(255);uniqueIndex;not null" json:"email"`
	PasswordHash      string         `gorm:"type:varchar(255);not null" json:"-"`
	FirstName         *string        `gorm:"type:varchar(100)" json:"first_name"`
	LastName          *string        `gorm:"type:varchar(100)" json:"last_name"`
	ProfilePictureURL *string        `gorm:"type:varchar(255)" json:"profile_picture_url"`
	CreatedAt         time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName returns the table name for UserModel
func (UserModel) TableName() string {
	return "users"
}

// ToDomain converts UserModel to domain.User
func (u *UserModel) ToDomain() *domain.User {
	user := &domain.User{
		ID:                u.ID,
		Username:          u.Username,
		Email:             u.Email,
		PasswordHash:      u.PasswordHash,
		FirstName:         u.FirstName,
		LastName:          u.LastName,
		ProfilePictureURL: u.ProfilePictureURL,
		CreatedAt:         u.CreatedAt,
		UpdatedAt:         u.UpdatedAt,
	}

	if u.DeletedAt.Valid {
		user.DeletedAt = &u.DeletedAt.Time
	}

	return user
}

// FromDomain converts domain.User to UserModel
func (u *UserModel) FromDomain(user *domain.User) {
	u.ID = user.ID
	u.Username = user.Username
	u.Email = user.Email
	u.PasswordHash = user.PasswordHash
	u.FirstName = user.FirstName
	u.LastName = user.LastName
	u.ProfilePictureURL = user.ProfilePictureURL
	u.CreatedAt = user.CreatedAt
	u.UpdatedAt = user.UpdatedAt

	if user.DeletedAt != nil {
		u.DeletedAt = gorm.DeletedAt{
			Time:  *user.DeletedAt,
			Valid: true,
		}
	}
}

// RefreshTokenModel represents the GORM model for refresh tokens
type RefreshTokenModel struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID    uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	TokenHash string    `gorm:"type:varchar(255);uniqueIndex;not null" json:"-"`
	ExpiresAt time.Time `gorm:"not null;index" json:"expires_at"`
	IsRevoked bool      `gorm:"not null;default:false" json:"is_revoked"`
	CreatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`

	// Foreign key relationship
	User UserModel `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for RefreshTokenModel
func (RefreshTokenModel) TableName() string {
	return "refresh_tokens"
}

// ToDomain converts RefreshTokenModel to domain.RefreshToken
func (rt *RefreshTokenModel) ToDomain() *domain.RefreshToken {
	return &domain.RefreshToken{
		ID:        rt.ID,
		UserID:    rt.UserID,
		TokenHash: rt.TokenHash,
		ExpiresAt: rt.ExpiresAt,
		IsRevoked: rt.IsRevoked,
		CreatedAt: rt.CreatedAt,
	}
}

// FromDomain converts domain.RefreshToken to RefreshTokenModel
func (rt *RefreshTokenModel) FromDomain(token *domain.RefreshToken) {
	rt.ID = token.ID
	rt.UserID = token.UserID
	rt.TokenHash = token.TokenHash
	rt.ExpiresAt = token.ExpiresAt
	rt.IsRevoked = token.IsRevoked
	rt.CreatedAt = token.CreatedAt
}

// BeforeCreate is a GORM hook that runs before creating a record
func (u *UserModel) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	now := time.Now()
	u.CreatedAt = now
	u.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a record
func (u *UserModel) BeforeUpdate(tx *gorm.DB) error {
	u.UpdatedAt = time.Now()
	return nil
}

// BeforeCreate is a GORM hook that runs before creating a refresh token record
func (rt *RefreshTokenModel) BeforeCreate(tx *gorm.DB) error {
	if rt.ID == uuid.Nil {
		rt.ID = uuid.New()
	}
	rt.CreatedAt = time.Now()
	return nil
}

// PantryModel represents the GORM model for pantries
type PantryModel struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:pantry_id" json:"id"`
	Name        string         `gorm:"type:varchar(100);not null" json:"name"`
	Description *string        `gorm:"type:text" json:"description"`
	OwnerUserID uuid.UUID      `gorm:"type:uuid;not null;column:owner_user_id;index" json:"owner_user_id"`
	CreatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Foreign key relationship
	OwnerUser UserModel `gorm:"foreignKey:OwnerUserID;constraint:OnDelete:RESTRICT" json:"-"`
}

// TableName returns the table name for PantryModel
func (PantryModel) TableName() string {
	return "pantries"
}

// ToDomain converts PantryModel to domain.Pantry
func (p *PantryModel) ToDomain() *domain.Pantry {
	pantry := &domain.Pantry{
		ID:          p.ID,
		Name:        p.Name,
		Description: p.Description,
		OwnerUserID: p.OwnerUserID,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}

	if p.DeletedAt.Valid {
		pantry.DeletedAt = &p.DeletedAt.Time
	}

	return pantry
}

// FromDomain converts domain.Pantry to PantryModel
func (p *PantryModel) FromDomain(pantry *domain.Pantry) {
	p.ID = pantry.ID
	p.Name = pantry.Name
	p.Description = pantry.Description
	p.OwnerUserID = pantry.OwnerUserID
	p.CreatedAt = pantry.CreatedAt
	p.UpdatedAt = pantry.UpdatedAt

	if pantry.DeletedAt != nil {
		p.DeletedAt = gorm.DeletedAt{
			Time:  *pantry.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating a pantry record
func (p *PantryModel) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	now := time.Now()
	p.CreatedAt = now
	p.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a pantry record
func (p *PantryModel) BeforeUpdate(tx *gorm.DB) error {
	p.UpdatedAt = time.Now()
	return nil
}

// PantryMembershipModel represents the GORM model for pantry memberships
type PantryMembershipModel struct {
	ID              uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:pantry_membership_id" json:"id"`
	PantryID        uuid.UUID      `gorm:"type:uuid;not null;index" json:"pantry_id"`
	UserID          uuid.UUID      `gorm:"type:uuid;not null;index" json:"user_id"`
	Role            string         `gorm:"type:varchar(20);not null;default:'editor'" json:"role"`
	Status          string         `gorm:"type:varchar(30);not null;default:'pending_invitation'" json:"status"`
	JoinedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"joined_at"`
	InvitedByUserID *uuid.UUID     `gorm:"type:uuid" json:"invited_by_user_id"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Foreign key relationships
	Pantry        PantryModel `gorm:"foreignKey:PantryID;constraint:OnDelete:CASCADE" json:"-"`
	User          UserModel   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	InvitedByUser *UserModel  `gorm:"foreignKey:InvitedByUserID;constraint:OnDelete:SET NULL" json:"-"`
}

// TableName returns the table name for PantryMembershipModel
func (PantryMembershipModel) TableName() string {
	return "pantry_memberships"
}

// ToDomain converts PantryMembershipModel to domain.PantryMembership
func (pm *PantryMembershipModel) ToDomain() *domain.PantryMembership {
	membership := &domain.PantryMembership{
		ID:              pm.ID,
		PantryID:        pm.PantryID,
		UserID:          pm.UserID,
		Role:            domain.PantryRole(pm.Role),
		Status:          domain.PantryMembershipStatus(pm.Status),
		JoinedAt:        pm.JoinedAt,
		InvitedByUserID: pm.InvitedByUserID,
	}

	if pm.DeletedAt.Valid {
		membership.DeletedAt = &pm.DeletedAt.Time
	}

	return membership
}

// FromDomain converts domain.PantryMembership to PantryMembershipModel
func (pm *PantryMembershipModel) FromDomain(membership *domain.PantryMembership) {
	pm.ID = membership.ID
	pm.PantryID = membership.PantryID
	pm.UserID = membership.UserID
	pm.Role = string(membership.Role)
	pm.Status = string(membership.Status)
	pm.JoinedAt = membership.JoinedAt
	pm.InvitedByUserID = membership.InvitedByUserID

	if membership.DeletedAt != nil {
		pm.DeletedAt = gorm.DeletedAt{
			Time:  *membership.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating a membership record
func (pm *PantryMembershipModel) BeforeCreate(tx *gorm.DB) error {
	if pm.ID == uuid.Nil {
		pm.ID = uuid.New()
	}
	pm.JoinedAt = time.Now()
	return nil
}

// PantryLocationModel represents the GORM model for pantry locations
type PantryLocationModel struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:pantry_location_id" json:"id"`
	PantryID    uuid.UUID      `gorm:"type:uuid;not null;index" json:"pantry_id"`
	Name        string         `gorm:"type:varchar(100);not null" json:"name"`
	Description *string        `gorm:"type:text" json:"description"`
	CreatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Foreign key relationship
	Pantry PantryModel `gorm:"foreignKey:PantryID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for PantryLocationModel
func (PantryLocationModel) TableName() string {
	return "pantry_locations"
}

// ToDomain converts PantryLocationModel to domain.PantryLocation
func (pl *PantryLocationModel) ToDomain() *domain.PantryLocation {
	location := &domain.PantryLocation{
		ID:          pl.ID,
		PantryID:    pl.PantryID,
		Name:        pl.Name,
		Description: pl.Description,
		CreatedAt:   pl.CreatedAt,
		UpdatedAt:   pl.UpdatedAt,
	}

	if pl.DeletedAt.Valid {
		location.DeletedAt = &pl.DeletedAt.Time
	}

	return location
}

// FromDomain converts domain.PantryLocation to PantryLocationModel
func (pl *PantryLocationModel) FromDomain(location *domain.PantryLocation) {
	pl.ID = location.ID
	pl.PantryID = location.PantryID
	pl.Name = location.Name
	pl.Description = location.Description
	pl.CreatedAt = location.CreatedAt
	pl.UpdatedAt = location.UpdatedAt

	if location.DeletedAt != nil {
		pl.DeletedAt = gorm.DeletedAt{
			Time:  *location.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating a location record
func (pl *PantryLocationModel) BeforeCreate(tx *gorm.DB) error {
	if pl.ID == uuid.Nil {
		pl.ID = uuid.New()
	}
	now := time.Now()
	pl.CreatedAt = now
	pl.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a location record
func (pl *PantryLocationModel) BeforeUpdate(tx *gorm.DB) error {
	pl.UpdatedAt = time.Now()
	return nil
}

// CategoryModel represents the GORM model for categories
type CategoryModel struct {
	ID               uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:category_id" json:"id"`
	Name             string         `gorm:"type:varchar(100);not null" json:"name"`
	Description      *string        `gorm:"type:text" json:"description"`
	ParentCategoryID *uuid.UUID     `gorm:"type:uuid;index" json:"parent_category_id"`
	CreatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Relationships
	ParentCategory *CategoryModel  `gorm:"foreignKey:ParentCategoryID;references:ID" json:"parent_category,omitempty"`
	SubCategories  []CategoryModel `gorm:"foreignKey:ParentCategoryID;references:ID" json:"sub_categories,omitempty"`
	Products       []ProductModel  `gorm:"foreignKey:CategoryID;references:ID" json:"products,omitempty"`
}

// TableName returns the table name for CategoryModel
func (CategoryModel) TableName() string {
	return "categories"
}

// ToDomain converts CategoryModel to domain.Category
func (c *CategoryModel) ToDomain() *domain.Category {
	category := &domain.Category{
		ID:               c.ID,
		Name:             c.Name,
		Description:      c.Description,
		ParentCategoryID: c.ParentCategoryID,
		CreatedAt:        c.CreatedAt,
		UpdatedAt:        c.UpdatedAt,
	}

	if c.DeletedAt.Valid {
		category.DeletedAt = &c.DeletedAt.Time
	}

	// Clear events since this is from persistence
	category.ClearEvents()
	return category
}

// FromDomain converts domain.Category to CategoryModel
func (c *CategoryModel) FromDomain(category *domain.Category) {
	c.ID = category.ID
	c.Name = category.Name
	c.Description = category.Description
	c.ParentCategoryID = category.ParentCategoryID
	c.CreatedAt = category.CreatedAt
	c.UpdatedAt = category.UpdatedAt

	if category.DeletedAt != nil {
		c.DeletedAt = gorm.DeletedAt{
			Time:  *category.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating a category record
func (c *CategoryModel) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	now := time.Now()
	c.CreatedAt = now
	c.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a category record
func (c *CategoryModel) BeforeUpdate(tx *gorm.DB) error {
	c.UpdatedAt = time.Now()
	return nil
}

// UnitOfMeasureModel represents the GORM model for units of measure
type UnitOfMeasureModel struct {
	ID               uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:unit_id" json:"id"`
	Name             string         `gorm:"type:varchar(100);not null" json:"name"`
	Symbol           string         `gorm:"type:varchar(20);not null" json:"symbol"`
	Type             string         `gorm:"type:varchar(20);not null" json:"type"`
	Description      *string        `gorm:"type:text" json:"description"`
	IsBaseUnit       bool           `gorm:"not null;default:true" json:"is_base_unit"`
	BaseUnitID       *uuid.UUID     `gorm:"type:uuid;index" json:"base_unit_id"`
	ConversionFactor *float64       `gorm:"type:decimal(20,10)" json:"conversion_factor"`
	CreatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Relationships
	BaseUnit        *UnitOfMeasureModel   `gorm:"foreignKey:BaseUnitID;references:ID" json:"base_unit,omitempty"`
	DerivedUnits    []UnitOfMeasureModel  `gorm:"foreignKey:BaseUnitID;references:ID" json:"derived_units,omitempty"`
	ProductVariants []ProductVariantModel `gorm:"foreignKey:DefaultUnitOfMeasureID;references:ID" json:"product_variants,omitempty"`
}

// TableName returns the table name for UnitOfMeasureModel
func (UnitOfMeasureModel) TableName() string {
	return "units_of_measure"
}

// ToDomain converts UnitOfMeasureModel to domain.UnitOfMeasure
func (u *UnitOfMeasureModel) ToDomain() *domain.UnitOfMeasure {
	unit := &domain.UnitOfMeasure{
		ID:               u.ID,
		Name:             u.Name,
		Symbol:           u.Symbol,
		Type:             domain.UnitOfMeasureType(u.Type),
		Description:      u.Description,
		IsBaseUnit:       u.IsBaseUnit,
		BaseUnitID:       u.BaseUnitID,
		ConversionFactor: u.ConversionFactor,
		CreatedAt:        u.CreatedAt,
		UpdatedAt:        u.UpdatedAt,
	}

	if u.DeletedAt.Valid {
		unit.DeletedAt = &u.DeletedAt.Time
	}

	// Clear events since this is from persistence
	unit.ClearEvents()
	return unit
}

// FromDomain converts domain.UnitOfMeasure to UnitOfMeasureModel
func (u *UnitOfMeasureModel) FromDomain(unit *domain.UnitOfMeasure) {
	u.ID = unit.ID
	u.Name = unit.Name
	u.Symbol = unit.Symbol
	u.Type = string(unit.Type)
	u.Description = unit.Description
	u.IsBaseUnit = unit.IsBaseUnit
	u.BaseUnitID = unit.BaseUnitID
	u.ConversionFactor = unit.ConversionFactor
	u.CreatedAt = unit.CreatedAt
	u.UpdatedAt = unit.UpdatedAt

	if unit.DeletedAt != nil {
		u.DeletedAt = gorm.DeletedAt{
			Time:  *unit.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating a unit record
func (u *UnitOfMeasureModel) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	now := time.Now()
	u.CreatedAt = now
	u.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a unit record
func (u *UnitOfMeasureModel) BeforeUpdate(tx *gorm.DB) error {
	u.UpdatedAt = time.Now()
	return nil
}

// ProductModel represents the GORM model for products
type ProductModel struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:product_id" json:"id"`
	Name        string         `gorm:"type:varchar(255);not null" json:"name"`
	Description *string        `gorm:"type:text" json:"description"`
	CategoryID  uuid.UUID      `gorm:"type:uuid;not null;index" json:"category_id"`
	Brand       *string        `gorm:"type:varchar(100)" json:"brand"`
	CreatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Relationships
	Category CategoryModel         `gorm:"foreignKey:CategoryID;references:ID" json:"category,omitempty"`
	Variants []ProductVariantModel `gorm:"foreignKey:ProductID;references:ID" json:"variants,omitempty"`
}

// TableName returns the table name for ProductModel
func (ProductModel) TableName() string {
	return "products"
}

// ToDomain converts ProductModel to domain.Product
func (p *ProductModel) ToDomain() *domain.Product {
	product := &domain.Product{
		ID:          p.ID,
		Name:        p.Name,
		Description: p.Description,
		CategoryID:  p.CategoryID,
		Brand:       p.Brand,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}

	if p.DeletedAt.Valid {
		product.DeletedAt = &p.DeletedAt.Time
	}

	// Clear events since this is from persistence
	product.ClearEvents()
	return product
}

// FromDomain converts domain.Product to ProductModel
func (p *ProductModel) FromDomain(product *domain.Product) {
	p.ID = product.ID
	p.Name = product.Name
	p.Description = product.Description
	p.CategoryID = product.CategoryID
	p.Brand = product.Brand
	p.CreatedAt = product.CreatedAt
	p.UpdatedAt = product.UpdatedAt

	if product.DeletedAt != nil {
		p.DeletedAt = gorm.DeletedAt{
			Time:  *product.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating a product record
func (p *ProductModel) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	now := time.Now()
	p.CreatedAt = now
	p.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a product record
func (p *ProductModel) BeforeUpdate(tx *gorm.DB) error {
	p.UpdatedAt = time.Now()
	return nil
}

// ProductVariantModel represents the GORM model for product variants
type ProductVariantModel struct {
	ID                     uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:variant_id" json:"id"`
	ProductID              uuid.UUID      `gorm:"type:uuid;not null;index" json:"product_id"`
	Name                   string         `gorm:"type:varchar(255);not null" json:"name"`
	Description            *string        `gorm:"type:text" json:"description"`
	BarcodeGTIN            *string        `gorm:"type:varchar(14);uniqueIndex" json:"barcode_gtin"`
	ImageURL               *string        `gorm:"type:varchar(500)" json:"image_url"`
	PackagingType          string         `gorm:"type:varchar(20);not null;default:'single'" json:"packaging_type"`
	DefaultUnitOfMeasureID *uuid.UUID     `gorm:"type:uuid;index" json:"default_unit_of_measure_id"`
	CreatedAt              time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt              time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt              gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Relationships
	Product              ProductModel        `gorm:"foreignKey:ProductID;references:ID" json:"product,omitempty"`
	DefaultUnitOfMeasure *UnitOfMeasureModel `gorm:"foreignKey:DefaultUnitOfMeasureID;references:ID" json:"default_unit_of_measure,omitempty"`
}

// TableName returns the table name for ProductVariantModel
func (ProductVariantModel) TableName() string {
	return "product_variants"
}

// ToDomain converts ProductVariantModel to domain.ProductVariant
func (pv *ProductVariantModel) ToDomain() *domain.ProductVariant {
	variant := &domain.ProductVariant{
		ID:                     pv.ID,
		ProductID:              pv.ProductID,
		Name:                   pv.Name,
		Description:            pv.Description,
		BarcodeGTIN:            pv.BarcodeGTIN,
		ImageURL:               pv.ImageURL,
		PackagingType:          domain.ProductVariantPackagingType(pv.PackagingType),
		DefaultUnitOfMeasureID: pv.DefaultUnitOfMeasureID,
		CreatedAt:              pv.CreatedAt,
		UpdatedAt:              pv.UpdatedAt,
	}

	if pv.DeletedAt.Valid {
		variant.DeletedAt = &pv.DeletedAt.Time
	}

	// Clear events since this is from persistence
	variant.ClearEvents()
	return variant
}

// FromDomain converts domain.ProductVariant to ProductVariantModel
func (pv *ProductVariantModel) FromDomain(variant *domain.ProductVariant) {
	pv.ID = variant.ID
	pv.ProductID = variant.ProductID
	pv.Name = variant.Name
	pv.Description = variant.Description
	pv.BarcodeGTIN = variant.BarcodeGTIN
	pv.ImageURL = variant.ImageURL
	pv.PackagingType = string(variant.PackagingType)
	pv.DefaultUnitOfMeasureID = variant.DefaultUnitOfMeasureID
	pv.CreatedAt = variant.CreatedAt
	pv.UpdatedAt = variant.UpdatedAt

	if variant.DeletedAt != nil {
		pv.DeletedAt = gorm.DeletedAt{
			Time:  *variant.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating a variant record
func (pv *ProductVariantModel) BeforeCreate(tx *gorm.DB) error {
	if pv.ID == uuid.Nil {
		pv.ID = uuid.New()
	}
	now := time.Now()
	pv.CreatedAt = now
	pv.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a variant record
func (pv *ProductVariantModel) BeforeUpdate(tx *gorm.DB) error {
	pv.UpdatedAt = time.Now()
	return nil
}

// InventoryItemModel represents the GORM model for inventory items
type InventoryItemModel struct {
	ID               uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:item_id" json:"id"`
	PantryID         uuid.UUID      `gorm:"type:uuid;not null;index" json:"pantry_id"`
	LocationID       *uuid.UUID     `gorm:"type:uuid;index" json:"location_id"`
	ProductVariantID uuid.UUID      `gorm:"type:uuid;not null;index" json:"product_variant_id"`
	Quantity         float64        `gorm:"type:decimal(20,6);not null" json:"quantity"`
	UnitOfMeasureID  uuid.UUID      `gorm:"type:uuid;not null;index" json:"unit_of_measure_id"`
	PurchaseDate     *time.Time     `gorm:"type:date" json:"purchase_date"`
	ExpirationDate   *time.Time     `gorm:"type:date;index" json:"expiration_date"`
	PurchasePrice    *float64       `gorm:"type:decimal(10,2)" json:"purchase_price"`
	Notes            *string        `gorm:"type:text" json:"notes"`
	CreatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Relationships
	Pantry         PantryModel          `gorm:"foreignKey:PantryID;references:ID" json:"pantry,omitempty"`
	Location       *PantryLocationModel `gorm:"foreignKey:LocationID;references:ID" json:"location,omitempty"`
	ProductVariant ProductVariantModel  `gorm:"foreignKey:ProductVariantID;references:ID" json:"product_variant,omitempty"`
	UnitOfMeasure  UnitOfMeasureModel   `gorm:"foreignKey:UnitOfMeasureID;references:ID" json:"unit_of_measure,omitempty"`
}

// TableName returns the table name for InventoryItemModel
func (InventoryItemModel) TableName() string {
	return "inventory_items"
}

// ToDomain converts InventoryItemModel to domain.InventoryItem
func (i *InventoryItemModel) ToDomain() *domain.InventoryItem {
	item := &domain.InventoryItem{
		ID:               i.ID,
		PantryID:         i.PantryID,
		LocationID:       i.LocationID,
		ProductVariantID: i.ProductVariantID,
		Quantity:         i.Quantity,
		UnitOfMeasureID:  i.UnitOfMeasureID,
		PurchaseDate:     i.PurchaseDate,
		ExpirationDate:   i.ExpirationDate,
		PurchasePrice:    i.PurchasePrice,
		Notes:            i.Notes,
		CreatedAt:        i.CreatedAt,
		UpdatedAt:        i.UpdatedAt,
	}

	if i.DeletedAt.Valid {
		item.DeletedAt = &i.DeletedAt.Time
	}

	// Clear events since this is from persistence
	item.ClearEvents()
	return item
}

// FromDomain converts domain.InventoryItem to InventoryItemModel
func (i *InventoryItemModel) FromDomain(item *domain.InventoryItem) {
	i.ID = item.ID
	i.PantryID = item.PantryID
	i.LocationID = item.LocationID
	i.ProductVariantID = item.ProductVariantID
	i.Quantity = item.Quantity
	i.UnitOfMeasureID = item.UnitOfMeasureID
	i.PurchaseDate = item.PurchaseDate
	i.ExpirationDate = item.ExpirationDate
	i.PurchasePrice = item.PurchasePrice
	i.Notes = item.Notes
	i.CreatedAt = item.CreatedAt
	i.UpdatedAt = item.UpdatedAt

	if item.DeletedAt != nil {
		i.DeletedAt = gorm.DeletedAt{
			Time:  *item.DeletedAt,
			Valid: true,
		}
	}
}

// BeforeCreate is a GORM hook that runs before creating an inventory item record
func (i *InventoryItemModel) BeforeCreate(tx *gorm.DB) error {
	if i.ID == uuid.Nil {
		i.ID = uuid.New()
	}
	now := time.Now()
	i.CreatedAt = now
	i.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating an inventory item record
func (i *InventoryItemModel) BeforeUpdate(tx *gorm.DB) error {
	i.UpdatedAt = time.Now()
	return nil
}
