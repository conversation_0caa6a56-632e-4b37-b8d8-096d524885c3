package postgres

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// parseDate parses a date string in YYYY-MM-DD format
func parseDate(dateStr string) time.Time {
	t, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return time.Time{}
	}
	return t
}

// PurchaseRepository implements domain.PurchaseRepository
type PurchaseRepository struct {
	db *gorm.DB
}

// NewPurchaseRepository creates a new PurchaseRepository
func NewPurchaseRepository(db *gorm.DB) *PurchaseRepository {
	return &PurchaseRepository{db: db}
}

// Create saves a new purchase to the database
func (r *PurchaseRepository) Create(purchase *domain.Purchase) error {
	model := &PurchaseModel{}
	model.FromDomain(purchase)

	if err := r.db.Create(model).Error; err != nil {
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to create purchase")
	}

	return nil
}

// GetByID retrieves a purchase by ID
func (r *PurchaseRepository) GetByID(id uuid.UUID) (*domain.Purchase, error) {
	var model PurchaseModel
	if err := r.db.Preload("Items").First(&model, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "Purchase not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to get purchase")
	}

	return model.ToDomain(), nil
}

// GetByPantryID retrieves purchases for a pantry with pagination
func (r *PurchaseRepository) GetByPantryID(pantryID uuid.UUID, page, limit int) ([]*domain.Purchase, int64, error) {
	var models []PurchaseModel
	var total int64

	offset := (page - 1) * limit

	if err := r.db.Model(&PurchaseModel{}).Where("pantry_id = ?", pantryID).Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to count purchases")
	}

	if err := r.db.Preload("Items").Where("pantry_id = ?", pantryID).
		Offset(offset).Limit(limit).Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to get purchases")
	}

	purchases := make([]*domain.Purchase, len(models))
	for i, model := range models {
		purchases[i] = model.ToDomain()
	}

	return purchases, total, nil
}

// GetByUserID retrieves purchases made by a user with pagination
func (r *PurchaseRepository) GetByUserID(userID uuid.UUID, page, limit int) ([]*domain.Purchase, int64, error) {
	var models []PurchaseModel
	var total int64

	offset := (page - 1) * limit

	if err := r.db.Model(&PurchaseModel{}).Where("purchased_by_user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to count purchases")
	}

	if err := r.db.Preload("Items").Where("purchased_by_user_id = ?", userID).
		Offset(offset).Limit(limit).Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to get purchases")
	}

	purchases := make([]*domain.Purchase, len(models))
	for i, model := range models {
		purchases[i] = model.ToDomain()
	}

	return purchases, total, nil
}

// Update updates an existing purchase
func (r *PurchaseRepository) Update(purchase *domain.Purchase) error {
	model := &PurchaseModel{}
	model.FromDomain(purchase)

	// Start a transaction
	tx := r.db.Begin()
	if tx.Error != nil {
		return errors.Wrap(tx.Error, errors.ErrCodeDatabaseError, "Failed to start transaction")
	}

	// Delete existing items
	if err := tx.Where("purchase_id = ?", purchase.ID).Delete(&PurchaseItemModel{}).Error; err != nil {
		tx.Rollback()
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to delete old purchase items")
	}

	// Save updated purchase and items
	if err := tx.Save(model).Error; err != nil {
		tx.Rollback()
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to update purchase")
	}

	if err := tx.Commit().Error; err != nil {
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to commit transaction")
	}

	return nil
}

// Delete soft deletes a purchase
func (r *PurchaseRepository) Delete(id uuid.UUID) error {
	if err := r.db.Delete(&PurchaseModel{}, "id = ?", id).Error; err != nil {
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "Failed to delete purchase")
	}
	return nil
}