package postgres

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// StoreModel represents the stores table in the database
type StoreModel struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:store_id" json:"id"`
	Name        string         `gorm:"type:varchar(100);not null" json:"name"`
	Address     *string        `gorm:"type:text" json:"address"`
	City        *string        `gorm:"type:varchar(100)" json:"city"`
	Country     *string        `gorm:"type:varchar(100)" json:"country"`
	PhoneNumber *string        `gorm:"type:varchar(50)" json:"phone_number"`
	Website     *string        `gorm:"type:varchar(255)" json:"website"`
	CreatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName returns the table name for StoreModel
func (StoreModel) TableName() string {
	return "stores"
}

// PurchaseModel represents the purchases table in the database
type PurchaseModel struct {
	ID                uuid.UUID         `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:purchase_id" json:"id"`
	PantryID          uuid.UUID         `gorm:"type:uuid;not null;index" json:"pantry_id"`
	PurchaseDate      string            `gorm:"type:date;not null" json:"purchase_date"`
	TotalAmount       float64           `gorm:"type:decimal(10,2);not null" json:"total_amount"`
	Currency          string            `gorm:"type:varchar(3);not null" json:"currency"`
	StoreID           *uuid.UUID        `gorm:"type:uuid;index" json:"store_id"`
	StoreName         *string           `gorm:"type:varchar(100)" json:"store_name"`
	ReceiptImageURL   *string           `gorm:"type:varchar(255)" json:"receipt_image_url"`
	PurchasedByUserID uuid.UUID         `gorm:"type:uuid;not null;index" json:"purchased_by_user_id"`
	Notes             *string           `gorm:"type:text" json:"notes"`
	CreatedAt         time.Time         `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time         `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt         gorm.DeletedAt    `gorm:"index" json:"deleted_at,omitempty"`
	Items             []PurchaseItemModel `gorm:"foreignKey:PurchaseID" json:"items"`

	// Foreign key relationships
	Pantry          PantryModel `gorm:"foreignKey:PantryID;constraint:OnDelete:CASCADE" json:"-"`
	Store           *StoreModel  `gorm:"foreignKey:StoreID" json:"-"`
	PurchasedByUser UserModel   `gorm:"foreignKey:PurchasedByUserID;constraint:OnDelete:RESTRICT" json:"-"`
}

// TableName returns the table name for PurchaseModel
func (PurchaseModel) TableName() string {
	return "purchases"
}

// PurchaseItemModel represents the purchase_items table in the database
type PurchaseItemModel struct {
	ID               uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();column:purchase_item_id" json:"id"`
	PurchaseID       uuid.UUID      `gorm:"type:uuid;not null;index" json:"purchase_id"`
	ProductVariantID uuid.UUID      `gorm:"type:uuid;not null;index" json:"product_variant_id"`
	QuantityBought   float64        `gorm:"type:decimal(10,3);not null" json:"quantity_bought"`
	UnitOfMeasureID  uuid.UUID      `gorm:"type:uuid;not null" json:"unit_of_measure_id"`
	PricePerUnit     float64        `gorm:"type:decimal(10,2);not null" json:"price_per_unit"`
	TotalPrice       float64        `gorm:"type:decimal(10,2);not null" json:"total_price"`
	Notes            *string        `gorm:"type:text" json:"notes"`
	CreatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Foreign key relationships
	Purchase       PurchaseModel       `gorm:"foreignKey:PurchaseID;constraint:OnDelete:CASCADE" json:"-"`
	ProductVariant ProductVariantModel `gorm:"foreignKey:ProductVariantID;constraint:OnDelete:RESTRICT" json:"-"`
	UnitOfMeasure  UnitOfMeasureModel  `gorm:"foreignKey:UnitOfMeasureID;constraint:OnDelete:RESTRICT" json:"-"`
}

// TableName returns the table name for PurchaseItemModel
func (PurchaseItemModel) TableName() string {
	return "purchase_items"
}

// BeforeCreate hook for StoreModel
func (s *StoreModel) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	now := time.Now()
	s.CreatedAt = now
	s.UpdatedAt = now
	return nil
}

// BeforeUpdate hook for StoreModel
func (s *StoreModel) BeforeUpdate(tx *gorm.DB) error {
	s.UpdatedAt = time.Now()
	return nil
}

// BeforeCreate hook for PurchaseModel
func (p *PurchaseModel) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	now := time.Now()
	p.CreatedAt = now
	p.UpdatedAt = now
	return nil
}

// BeforeUpdate hook for PurchaseModel
func (p *PurchaseModel) BeforeUpdate(tx *gorm.DB) error {
	p.UpdatedAt = time.Now()
	return nil
}

// BeforeCreate hook for PurchaseItemModel
func (pi *PurchaseItemModel) BeforeCreate(tx *gorm.DB) error {
	if pi.ID == uuid.Nil {
		pi.ID = uuid.New()
	}
	now := time.Now()
	pi.CreatedAt = now
	pi.UpdatedAt = now
	return nil
}

// BeforeUpdate hook for PurchaseItemModel
func (pi *PurchaseItemModel) BeforeUpdate(tx *gorm.DB) error {
	pi.UpdatedAt = time.Now()
	return nil
}

// ToDomain converts StoreModel to domain.Store
func (s *StoreModel) ToDomain() *domain.Store {
	store := &domain.Store{
		ID:          s.ID,
		Name:        s.Name,
		Address:     s.Address,
		City:        s.City,
		Country:     s.Country,
		PhoneNumber: s.PhoneNumber,
		Website:     s.Website,
		CreatedAt:   s.CreatedAt,
		UpdatedAt:   s.UpdatedAt,
	}

	if s.DeletedAt.Valid {
		store.DeletedAt = &s.DeletedAt.Time
	}

	return store
}

// FromDomain converts domain.Store to StoreModel
func (s *StoreModel) FromDomain(store *domain.Store) {
	s.ID = store.ID
	s.Name = store.Name
	s.Address = store.Address
	s.City = store.City
	s.Country = store.Country
	s.PhoneNumber = store.PhoneNumber
	s.Website = store.Website
	s.CreatedAt = store.CreatedAt
	s.UpdatedAt = store.UpdatedAt

	if store.DeletedAt != nil {
		s.DeletedAt = gorm.DeletedAt{
			Time:  *store.DeletedAt,
			Valid: true,
		}
	}
}

// ToDomain converts PurchaseModel to domain.Purchase
func (p *PurchaseModel) ToDomain() *domain.Purchase {
	purchase := &domain.Purchase{
		ID:                p.ID,
		PantryID:          p.PantryID,
		PurchaseDate:      parseDate(p.PurchaseDate),
		TotalAmount:       p.TotalAmount,
		Currency:          p.Currency,
		StoreID:           p.StoreID,
		StoreName:         p.StoreName,
		ReceiptImageURL:   p.ReceiptImageURL,
		PurchasedByUserID: p.PurchasedByUserID,
		Notes:             p.Notes,
		CreatedAt:         p.CreatedAt,
		UpdatedAt:         p.UpdatedAt,
		Items:             make([]domain.PurchaseItem, len(p.Items)),
	}

	if p.DeletedAt.Valid {
		purchase.DeletedAt = &p.DeletedAt.Time
	}

	for i, item := range p.Items {
		purchase.Items[i] = *item.ToDomain()
	}

	return purchase
}

// FromDomain converts domain.Purchase to PurchaseModel
func (p *PurchaseModel) FromDomain(purchase *domain.Purchase) {
	p.ID = purchase.ID
	p.PantryID = purchase.PantryID
	p.PurchaseDate = purchase.PurchaseDate.Format("2006-01-02")
	p.TotalAmount = purchase.TotalAmount
	p.Currency = purchase.Currency
	p.StoreID = purchase.StoreID
	p.StoreName = purchase.StoreName
	p.ReceiptImageURL = purchase.ReceiptImageURL
	p.PurchasedByUserID = purchase.PurchasedByUserID
	p.Notes = purchase.Notes
	p.CreatedAt = purchase.CreatedAt
	p.UpdatedAt = purchase.UpdatedAt

	p.Items = make([]PurchaseItemModel, len(purchase.Items))
	for i, item := range purchase.Items {
		var model PurchaseItemModel
		model.FromDomain(&item)
		p.Items[i] = model
	}

	if purchase.DeletedAt != nil {
		p.DeletedAt = gorm.DeletedAt{
			Time:  *purchase.DeletedAt,
			Valid: true,
		}
	}
}

// ToDomain converts PurchaseItemModel to domain.PurchaseItem
func (pi *PurchaseItemModel) ToDomain() *domain.PurchaseItem {
	item := &domain.PurchaseItem{
		ID:               pi.ID,
		PurchaseID:       pi.PurchaseID,
		ProductVariantID: pi.ProductVariantID,
		QuantityBought:   pi.QuantityBought,
		UnitOfMeasureID:  pi.UnitOfMeasureID,
		PricePerUnit:     pi.PricePerUnit,
		TotalPrice:       pi.TotalPrice,
		Notes:            pi.Notes,
		CreatedAt:        pi.CreatedAt,
		UpdatedAt:        pi.UpdatedAt,
	}

	if pi.DeletedAt.Valid {
		item.DeletedAt = &pi.DeletedAt.Time
	}

	return item
}

// FromDomain converts domain.PurchaseItem to PurchaseItemModel
func (pi *PurchaseItemModel) FromDomain(item *domain.PurchaseItem) {
	pi.ID = item.ID
	pi.PurchaseID = item.PurchaseID
	pi.ProductVariantID = item.ProductVariantID
	pi.QuantityBought = item.QuantityBought
	pi.UnitOfMeasureID = item.UnitOfMeasureID
	pi.PricePerUnit = item.PricePerUnit
	pi.TotalPrice = item.TotalPrice
	pi.Notes = item.Notes
	pi.CreatedAt = item.CreatedAt
	pi.UpdatedAt = item.UpdatedAt

	if item.DeletedAt != nil {
		pi.DeletedAt = gorm.DeletedAt{
			Time:  *item.DeletedAt,
			Valid: true,
		}
	}
}