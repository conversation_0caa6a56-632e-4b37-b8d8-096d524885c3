package postgres

import (
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// ProductRepository implements domain.ProductRepository using GORM
type ProductRepository struct {
	db *gorm.DB
}

// NewProductRepository creates a new product repository
func NewProductRepository(db *gorm.DB) *ProductRepository {
	return &ProductRepository{db: db}
}

// Create creates a new product
func (r *ProductRepository) Create(product *domain.Product) error {
	model := &ProductModel{}
	model.FromDomain(product)

	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "Product with this name, brand, and category already exists")
		}
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create product")
	}

	// Update the domain object with generated values
	*product = *model.ToDomain()

	return nil
}

// GetByID retrieves a product by ID
func (r *ProductRepository) GetByID(id uuid.UUID) (*domain.Product, error) {
	var model ProductModel

	if err := r.db.Where("product_id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "product not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get product by ID")
	}

	return model.ToDomain(), nil
}

// GetByNameAndBrandAndCategory retrieves a product by name, brand, and category
func (r *ProductRepository) GetByNameAndBrandAndCategory(name string, brand *string, categoryID uuid.UUID) (*domain.Product, error) {
	var model ProductModel

	query := r.db.Where("name = ? AND category_id = ?", name, categoryID)

	if brand != nil {
		query = query.Where("brand = ?", *brand)
	} else {
		query = query.Where("brand IS NULL")
	}

	if err := query.First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "product not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get product by name, brand, and category")
	}

	return model.ToDomain(), nil
}

// GetByCategory retrieves products by category with pagination
func (r *ProductRepository) GetByCategory(categoryID uuid.UUID, page, limit int) ([]*domain.Product, int64, error) {
	var models []ProductModel
	var total int64

	// Count total records
	if err := r.db.Model(&ProductModel{}).
		Where("category_id = ?", categoryID).
		Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count products by category")
	}

	// Get paginated records
	offset := (page - 1) * limit
	if err := r.db.Where("category_id = ?", categoryID).
		Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get products by category")
	}

	products := make([]*domain.Product, len(models))
	for i, model := range models {
		products[i] = model.ToDomain()
	}

	return products, total, nil
}

// SearchProducts searches for products by query with optional category filter
func (r *ProductRepository) SearchProducts(query string, categoryID *uuid.UUID, page, limit int) ([]*domain.Product, int64, error) {
	var models []ProductModel
	var total int64

	// Build search query
	searchQuery := r.db.Model(&ProductModel{})

	if query != "" {
		searchTerm := "%" + strings.ToLower(query) + "%"
		searchQuery = searchQuery.Where(
			"LOWER(name) LIKE ? OR LOWER(brand) LIKE ? OR LOWER(description) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	if categoryID != nil {
		searchQuery = searchQuery.Where("category_id = ?", *categoryID)
	}

	// Count total records
	if err := searchQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count search results")
	}

	// Get paginated records
	offset := (page - 1) * limit
	if err := searchQuery.Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to search products")
	}

	products := make([]*domain.Product, len(models))
	for i, model := range models {
		products[i] = model.ToDomain()
	}

	return products, total, nil
}

// Update updates an existing product
func (r *ProductRepository) Update(product *domain.Product) error {
	model := &ProductModel{}
	model.FromDomain(product)

	result := r.db.Model(&ProductModel{}).
		Where("product_id = ?", product.ID).
		Updates(model)

	if result.Error != nil {
		if isDuplicateKeyError(result.Error) {
			return errors.New(errors.ErrCodeAlreadyExists, "Product with this name, brand, and category already exists")
		}
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update product")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "product not found")
	}

	return nil
}

// Delete soft deletes a product
func (r *ProductRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&ProductModel{}, "product_id = ?", id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete product")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "product not found")
	}

	return nil
}

// ExistsByNameAndBrandAndCategory checks if a product with the given name, brand, and category exists
func (r *ProductRepository) ExistsByNameAndBrandAndCategory(name string, brand *string, categoryID uuid.UUID) (bool, error) {
	var count int64

	query := r.db.Model(&ProductModel{}).
		Where("name = ? AND category_id = ?", name, categoryID)

	if brand != nil {
		query = query.Where("brand = ?", *brand)
	} else {
		query = query.Where("brand IS NULL")
	}

	if err := query.Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check product existence")
	}

	return count > 0, nil
}
