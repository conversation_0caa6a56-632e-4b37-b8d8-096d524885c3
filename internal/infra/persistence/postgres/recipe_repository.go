package postgres

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// RecipeRepository implements the recipe repository using GORM
type RecipeRepository struct {
	db *gorm.DB
}

// NewRecipeRepository creates a new recipe repository
func NewRecipeRepository(db *gorm.DB) domain.RecipeRepository {
	return &RecipeRepository{db: db}
}

// Create creates a new recipe
func (r *RecipeRepository) Create(recipe *domain.Recipe) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Create the recipe
		if err := tx.Create(recipe).Error; err != nil {
			return err
		}

		// Create ingredients
		for i := range recipe.Ingredients {
			recipe.Ingredients[i].RecipeID = recipe.ID
			if err := tx.Create(&recipe.Ingredients[i]).Error; err != nil {
				return err
			}
		}

		// Create instructions
		for i := range recipe.Instructions {
			recipe.Instructions[i].RecipeID = recipe.ID
			if err := tx.Create(&recipe.Instructions[i]).Error; err != nil {
				return err
			}
		}

		// Create nutrition if provided
		if recipe.Nutrition != nil {
			recipe.Nutrition.RecipeID = recipe.ID
			if err := tx.Create(recipe.Nutrition).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// GetByID retrieves a recipe by ID
func (r *RecipeRepository) GetByID(id uuid.UUID) (*domain.Recipe, error) {
	var recipe domain.Recipe
	err := r.db.Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Preload("Reviews").
		Where("id = ?", id).
		First(&recipe).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("recipe not found")
		}
		return nil, err
	}

	return &recipe, nil
}

// GetByUserID retrieves recipes for a user with pagination and filtering
func (r *RecipeRepository) GetByUserID(userID uuid.UUID, params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	var recipes []*domain.Recipe
	var total int64

	query := r.db.Model(&domain.Recipe{}).Where("user_id = ?", userID)

	// Apply filters
	query = r.applyFilters(query, params)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	query = r.applySorting(query, params)
	query = r.applyPagination(query, params)

	// Load with associations
	err := query.Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Find(&recipes).Error

	if err != nil {
		return nil, 0, err
	}

	return recipes, total, nil
}

// GetPublicRecipes retrieves public recipes with pagination and filtering
func (r *RecipeRepository) GetPublicRecipes(params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	var recipes []*domain.Recipe
	var total int64

	query := r.db.Model(&domain.Recipe{}).Where("is_public = ?", true)

	// Apply filters
	query = r.applyFilters(query, params)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	query = r.applySorting(query, params)
	query = r.applyPagination(query, params)

	// Load with associations
	err := query.Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Find(&recipes).Error

	if err != nil {
		return nil, 0, err
	}

	return recipes, total, nil
}

// Update updates a recipe
func (r *RecipeRepository) Update(recipe *domain.Recipe) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Update the main recipe
		if err := tx.Save(recipe).Error; err != nil {
			return err
		}

		// Update ingredients (delete and recreate for simplicity)
		if err := tx.Where("recipe_id = ?", recipe.ID).Delete(&domain.RecipeIngredient{}).Error; err != nil {
			return err
		}
		for i := range recipe.Ingredients {
			recipe.Ingredients[i].RecipeID = recipe.ID
			if err := tx.Create(&recipe.Ingredients[i]).Error; err != nil {
				return err
			}
		}

		// Update instructions (delete and recreate for simplicity)
		if err := tx.Where("recipe_id = ?", recipe.ID).Delete(&domain.RecipeInstruction{}).Error; err != nil {
			return err
		}
		for i := range recipe.Instructions {
			recipe.Instructions[i].RecipeID = recipe.ID
			if err := tx.Create(&recipe.Instructions[i]).Error; err != nil {
				return err
			}
		}

		// Update nutrition
		if recipe.Nutrition != nil {
			recipe.Nutrition.RecipeID = recipe.ID
			if err := tx.Save(recipe.Nutrition).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// Delete deletes a recipe
func (r *RecipeRepository) Delete(id uuid.UUID) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Delete associated records first (due to foreign key constraints)
		if err := tx.Where("recipe_id = ?", id).Delete(&domain.RecipeIngredient{}).Error; err != nil {
			return err
		}
		if err := tx.Where("recipe_id = ?", id).Delete(&domain.RecipeInstruction{}).Error; err != nil {
			return err
		}
		if err := tx.Where("recipe_id = ?", id).Delete(&domain.RecipeMedia{}).Error; err != nil {
			return err
		}
		if err := tx.Where("recipe_id = ?", id).Delete(&domain.RecipeNutrition{}).Error; err != nil {
			return err
		}
		if err := tx.Where("recipe_id = ?", id).Delete(&domain.RecipeReview{}).Error; err != nil {
			return err
		}

		// Delete the recipe
		return tx.Delete(&domain.Recipe{}, id).Error
	})
}

// GetByTags retrieves recipes by tags
func (r *RecipeRepository) GetByTags(tags []string, params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	var recipes []*domain.Recipe
	var total int64

	query := r.db.Model(&domain.Recipe{}).
		Joins("JOIN recipe_tags ON recipes.id = recipe_tags.recipe_id").
		Joins("JOIN recipe_tags rt ON recipe_tags.recipe_tag_id = rt.id").
		Where("rt.name IN ?", tags)

	// Apply filters
	query = r.applyFilters(query, params)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	query = r.applySorting(query, params)
	query = r.applyPagination(query, params)

	// Load with associations
	err := query.Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Find(&recipes).Error

	if err != nil {
		return nil, 0, err
	}

	return recipes, total, nil
}

// GetFavorites retrieves favorite recipes for a user
func (r *RecipeRepository) GetFavorites(userID uuid.UUID, params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	var recipes []*domain.Recipe
	var total int64

	query := r.db.Model(&domain.Recipe{}).Where("user_id = ? AND is_favorite = ?", userID, true)

	// Apply filters
	query = r.applyFilters(query, params)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	query = r.applySorting(query, params)
	query = r.applyPagination(query, params)

	// Load with associations
	err := query.Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Find(&recipes).Error

	if err != nil {
		return nil, 0, err
	}

	return recipes, total, nil
}

// Search searches for recipes
func (r *RecipeRepository) Search(query string, params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	var recipes []*domain.Recipe
	var total int64

	searchQuery := r.db.Model(&domain.Recipe{}).Where(
		"title ILIKE ? OR description ILIKE ?",
		"%"+query+"%", "%"+query+"%",
	)

	// Apply filters
	searchQuery = r.applyFilters(searchQuery, params)

	// Count total
	if err := searchQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	searchQuery = r.applySorting(searchQuery, params)
	searchQuery = r.applyPagination(searchQuery, params)

	// Load with associations
	err := searchQuery.Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Find(&recipes).Error

	if err != nil {
		return nil, 0, err
	}

	return recipes, total, nil
}

// GetPopular retrieves popular recipes
func (r *RecipeRepository) GetPopular(limit int) ([]*domain.Recipe, error) {
	var recipes []*domain.Recipe

	err := r.db.Where("is_public = ?", true).
		Order("cook_count DESC, rating DESC").
		Limit(limit).
		Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Find(&recipes).Error

	if err != nil {
		return nil, err
	}

	return recipes, nil
}

// GetRecentlyCooked retrieves recently cooked recipes for a user
func (r *RecipeRepository) GetRecentlyCooked(userID uuid.UUID, limit int) ([]*domain.Recipe, error) {
	var recipes []*domain.Recipe

	err := r.db.Where("user_id = ? AND last_cooked_at IS NOT NULL", userID).
		Order("last_cooked_at DESC").
		Limit(limit).
		Preload("Ingredients").
		Preload("Instructions").
		Preload("Media").
		Preload("Nutrition").
		Preload("Tags").
		Find(&recipes).Error

	if err != nil {
		return nil, err
	}

	return recipes, nil
}

// IncrementCookCount increments the cook count for a recipe
func (r *RecipeRepository) IncrementCookCount(id uuid.UUID) error {
	return r.db.Model(&domain.Recipe{}).Where("id = ?", id).
		UpdateColumn("cook_count", gorm.Expr("cook_count + 1")).Error
}

// UpdateLastCookedAt updates the last cooked timestamp for a recipe
func (r *RecipeRepository) UpdateLastCookedAt(id uuid.UUID) error {
	return r.db.Model(&domain.Recipe{}).Where("id = ?", id).
		UpdateColumn("last_cooked_at", time.Now()).Error
}

// Helper methods

func (r *RecipeRepository) applyFilters(query *gorm.DB, params domain.RecipeQueryParams) *gorm.DB {
	if params.Search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+params.Search+"%", "%"+params.Search+"%")
	}

	if params.Cuisine != "" {
		query = query.Where("cuisine = ?", params.Cuisine)
	}

	if params.Category != "" {
		query = query.Where("category = ?", params.Category)
	}

	if params.Difficulty != "" {
		query = query.Where("difficulty = ?", params.Difficulty)
	}

	if params.MaxPrepTime > 0 {
		query = query.Where("prep_time <= ?", params.MaxPrepTime)
	}

	if params.MaxCookTime > 0 {
		query = query.Where("cook_time <= ?", params.MaxCookTime)
	}

	if params.MaxCalories > 0 {
		query = query.Where("calories <= ?", params.MaxCalories)
	}

	if params.IsFavorite {
		query = query.Where("is_favorite = ?", true)
	}

	if params.IsPublic {
		query = query.Where("is_public = ?", true)
	}

	return query
}

func (r *RecipeRepository) applySorting(query *gorm.DB, params domain.RecipeQueryParams) *gorm.DB {
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := strings.ToUpper(params.SortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "DESC"
	}

	// Validate sort field
	validSortFields := map[string]bool{
		"created_at": true,
		"updated_at": true,
		"title":      true,
		"rating":     true,
		"cook_count": true,
		"prep_time":  true,
		"cook_time":  true,
		"total_time": true,
		"calories":   true,
	}

	if !validSortFields[sortBy] {
		sortBy = "created_at"
	}

	return query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
}

func (r *RecipeRepository) applyPagination(query *gorm.DB, params domain.RecipeQueryParams) *gorm.DB {
	page := params.Page
	if page < 1 {
		page = 1
	}

	limit := params.Limit
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	offset := (page - 1) * limit
	return query.Offset(offset).Limit(limit)
}
