-- Drop triggers
DROP TRIGGER IF EXISTS trigger_set_shopping_list_item_purchased_at ON shopping_list_items;
DROP TRIGGER IF EXISTS trigger_update_shopping_list_items_updated_at ON shopping_list_items;
DROP TRIGGER IF EXISTS trigger_update_shopping_lists_updated_at ON shopping_lists;

-- Drop trigger functions
DROP FUNCTION IF EXISTS set_shopping_list_item_purchased_at();
DROP FUNCTION IF EXISTS update_shopping_list_items_updated_at();
DROP FUNCTION IF EXISTS update_shopping_lists_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_shopping_list_items_list_purchased;
DROP INDEX IF EXISTS idx_shopping_lists_pantry_status;
DROP INDEX IF EXISTS idx_shopping_list_items_created_at;
DROP INDEX IF EXISTS idx_shopping_list_items_purchased_at;
DROP INDEX IF EXISTS idx_shopping_list_items_is_purchased;
DROP INDEX IF EXISTS idx_shopping_list_items_product_variant_id;
DROP INDEX IF EXISTS idx_shopping_list_items_shopping_list_id;
DROP INDEX IF EXISTS idx_shopping_lists_updated_at;
DROP INDEX IF EXISTS idx_shopping_lists_created_at;
DROP INDEX IF EXISTS idx_shopping_lists_status;
DROP INDEX IF EXISTS idx_shopping_lists_created_by;
DROP INDEX IF EXISTS idx_shopping_lists_pantry_id;

-- Drop tables
DROP TABLE IF EXISTS shopping_list_items;
DROP TABLE IF EXISTS shopping_lists;
