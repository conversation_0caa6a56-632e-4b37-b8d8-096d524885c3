-- Drop indexes first
DROP INDEX IF EXISTS idx_purchase_items_product_variant_id;
DROP INDEX IF EXISTS idx_purchase_items_purchase_id;
DROP INDEX IF EXISTS idx_purchases_purchase_date;
DROP INDEX IF EXISTS idx_purchases_purchased_by_user_id;
DROP INDEX IF EXISTS idx_purchases_store_id;
DROP INDEX IF EXISTS idx_purchases_pantry_id;

-- Drop tables in proper order to respect foreign key constraints
DROP TABLE IF EXISTS purchase_items;
DROP TABLE IF EXISTS purchases;
DROP TABLE IF EXISTS stores;