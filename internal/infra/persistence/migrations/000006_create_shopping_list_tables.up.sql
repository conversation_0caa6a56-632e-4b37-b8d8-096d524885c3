-- Create shopping_lists table
CREATE TABLE shopping_lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create shopping_list_items table
CREATE TABLE shopping_list_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shopping_list_id UUID NOT NULL REFERENCES shopping_lists(id) ON DELETE CASCADE,
    product_variant_id UUID REFERENCES product_variants(variant_id) ON DELETE SET NULL,
    free_text_name VARCHAR(255),
    quantity_desired DECIMAL(10,3) NOT NULL CHECK (quantity_desired > 0),
    unit_of_measure_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
    notes TEXT,
    is_purchased BOOLEAN NOT NULL DEFAULT FALSE,
    purchased_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure either product_variant_id or free_text_name is provided, but not both
    CONSTRAINT check_item_reference CHECK (
        (product_variant_id IS NOT NULL AND free_text_name IS NULL) OR
        (product_variant_id IS NULL AND free_text_name IS NOT NULL)
    ),
    
    -- Ensure purchased_at is set when is_purchased is true
    CONSTRAINT check_purchased_at CHECK (
        (is_purchased = FALSE AND purchased_at IS NULL) OR
        (is_purchased = TRUE AND purchased_at IS NOT NULL)
    )
);

-- Create indexes for better query performance
CREATE INDEX idx_shopping_lists_pantry_id ON shopping_lists(pantry_id);
CREATE INDEX idx_shopping_lists_created_by ON shopping_lists(created_by);
CREATE INDEX idx_shopping_lists_status ON shopping_lists(status);
CREATE INDEX idx_shopping_lists_created_at ON shopping_lists(created_at);
CREATE INDEX idx_shopping_lists_updated_at ON shopping_lists(updated_at);

CREATE INDEX idx_shopping_list_items_shopping_list_id ON shopping_list_items(shopping_list_id);
CREATE INDEX idx_shopping_list_items_product_variant_id ON shopping_list_items(product_variant_id);
CREATE INDEX idx_shopping_list_items_is_purchased ON shopping_list_items(is_purchased);
CREATE INDEX idx_shopping_list_items_purchased_at ON shopping_list_items(purchased_at);
CREATE INDEX idx_shopping_list_items_created_at ON shopping_list_items(created_at);

-- Create composite indexes for common query patterns
CREATE INDEX idx_shopping_lists_pantry_status ON shopping_lists(pantry_id, status);
CREATE INDEX idx_shopping_list_items_list_purchased ON shopping_list_items(shopping_list_id, is_purchased);

-- Create trigger to update updated_at timestamp for shopping_lists
CREATE OR REPLACE FUNCTION update_shopping_lists_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_shopping_lists_updated_at
    BEFORE UPDATE ON shopping_lists
    FOR EACH ROW
    EXECUTE FUNCTION update_shopping_lists_updated_at();

-- Create trigger to update updated_at timestamp for shopping_list_items
CREATE OR REPLACE FUNCTION update_shopping_list_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_shopping_list_items_updated_at
    BEFORE UPDATE ON shopping_list_items
    FOR EACH ROW
    EXECUTE FUNCTION update_shopping_list_items_updated_at();

-- Create trigger to automatically set purchased_at when is_purchased changes to true
CREATE OR REPLACE FUNCTION set_shopping_list_item_purchased_at()
RETURNS TRIGGER AS $$
BEGIN
    -- If is_purchased is being set to true and purchased_at is null, set it to now
    IF NEW.is_purchased = TRUE AND OLD.is_purchased = FALSE AND NEW.purchased_at IS NULL THEN
        NEW.purchased_at = NOW();
    END IF;
    
    -- If is_purchased is being set to false, clear purchased_at
    IF NEW.is_purchased = FALSE AND OLD.is_purchased = TRUE THEN
        NEW.purchased_at = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_shopping_list_item_purchased_at
    BEFORE UPDATE ON shopping_list_items
    FOR EACH ROW
    EXECUTE FUNCTION set_shopping_list_item_purchased_at();

-- Add comments for documentation
COMMENT ON TABLE shopping_lists IS 'Shopping lists created by users for specific pantries';
COMMENT ON COLUMN shopping_lists.status IS 'Status of the shopping list: active, completed, or archived';
COMMENT ON COLUMN shopping_lists.created_by IS 'User who created the shopping list';

COMMENT ON TABLE shopping_list_items IS 'Items in shopping lists, can reference product variants or be free text';
COMMENT ON COLUMN shopping_list_items.product_variant_id IS 'Reference to a product variant from the catalog';
COMMENT ON COLUMN shopping_list_items.free_text_name IS 'Free text name for items not in the product catalog';
COMMENT ON COLUMN shopping_list_items.quantity_desired IS 'Desired quantity to purchase';
COMMENT ON COLUMN shopping_list_items.is_purchased IS 'Whether the item has been purchased';
COMMENT ON COLUMN shopping_list_items.purchased_at IS 'When the item was marked as purchased';
