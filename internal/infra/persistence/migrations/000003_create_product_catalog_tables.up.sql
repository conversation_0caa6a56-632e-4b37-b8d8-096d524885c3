-- Create categories table
CREATE TABLE categories (
    category_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_category_id UUID REFERENCES categories(category_id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for categories
CREATE INDEX idx_categories_parent_category_id ON categories(parent_category_id);
CREATE INDEX idx_categories_name ON categories(name);
CREATE INDEX idx_categories_deleted_at ON categories(deleted_at);
CREATE UNIQUE INDEX idx_categories_name_parent_unique ON categories(name, parent_category_id) WHERE deleted_at IS NULL;

-- Create units_of_measure table
CREATE TABLE units_of_measure (
    unit_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('volume', 'weight', 'count', 'length', 'area', 'other')),
    description TEXT,
    is_base_unit BOOLEAN NOT NULL DEFAULT true,
    base_unit_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
    conversion_factor DECIMAL(20,10),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for units_of_measure
CREATE INDEX idx_units_of_measure_type ON units_of_measure(type);
CREATE INDEX idx_units_of_measure_base_unit_id ON units_of_measure(base_unit_id);
CREATE INDEX idx_units_of_measure_deleted_at ON units_of_measure(deleted_at);
CREATE UNIQUE INDEX idx_units_of_measure_name_unique ON units_of_measure(name) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX idx_units_of_measure_symbol_unique ON units_of_measure(symbol) WHERE deleted_at IS NULL;

-- Create products table
CREATE TABLE products (
    product_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID NOT NULL REFERENCES categories(category_id) ON DELETE RESTRICT,
    brand VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for products
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_deleted_at ON products(deleted_at);
CREATE UNIQUE INDEX idx_products_name_brand_category_unique ON products(name, brand, category_id) WHERE deleted_at IS NULL;

-- Create product_variants table
CREATE TABLE product_variants (
    variant_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(product_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    barcode_gtin VARCHAR(14),
    image_url VARCHAR(500),
    packaging_type VARCHAR(20) NOT NULL DEFAULT 'single' CHECK (packaging_type IN ('single', 'bulk', 'multi-pack', 'other')),
    default_unit_of_measure_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for product_variants
CREATE INDEX idx_product_variants_product_id ON product_variants(product_id);
CREATE INDEX idx_product_variants_barcode_gtin ON product_variants(barcode_gtin);
CREATE INDEX idx_product_variants_default_unit_of_measure_id ON product_variants(default_unit_of_measure_id);
CREATE INDEX idx_product_variants_name ON product_variants(name);
CREATE INDEX idx_product_variants_deleted_at ON product_variants(deleted_at);
CREATE UNIQUE INDEX idx_product_variants_barcode_unique ON product_variants(barcode_gtin) WHERE barcode_gtin IS NOT NULL AND deleted_at IS NULL;
CREATE UNIQUE INDEX idx_product_variants_product_name_unique ON product_variants(product_id, name) WHERE deleted_at IS NULL;

-- Create triggers for updated_at columns
CREATE TRIGGER update_categories_updated_at 
    BEFORE UPDATE ON categories 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_units_of_measure_updated_at 
    BEFORE UPDATE ON units_of_measure 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_variants_updated_at 
    BEFORE UPDATE ON product_variants 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default categories
INSERT INTO categories (name, description) VALUES 
    ('Food & Beverages', 'All food and beverage items'),
    ('Household Items', 'Cleaning supplies, paper products, etc.'),
    ('Personal Care', 'Health and beauty products'),
    ('Pet Supplies', 'Food and supplies for pets'),
    ('Other', 'Miscellaneous items');

-- Insert subcategories for Food & Beverages
INSERT INTO categories (name, description, parent_category_id) 
SELECT 'Dairy & Eggs', 'Milk, cheese, yogurt, eggs', category_id FROM categories WHERE name = 'Food & Beverages';

INSERT INTO categories (name, description, parent_category_id) 
SELECT 'Meat & Seafood', 'Fresh and frozen meat, poultry, seafood', category_id FROM categories WHERE name = 'Food & Beverages';

INSERT INTO categories (name, description, parent_category_id) 
SELECT 'Fruits & Vegetables', 'Fresh and frozen produce', category_id FROM categories WHERE name = 'Food & Beverages';

INSERT INTO categories (name, description, parent_category_id) 
SELECT 'Pantry Staples', 'Canned goods, grains, spices, condiments', category_id FROM categories WHERE name = 'Food & Beverages';

INSERT INTO categories (name, description, parent_category_id) 
SELECT 'Beverages', 'Water, juice, soda, coffee, tea', category_id FROM categories WHERE name = 'Food & Beverages';

INSERT INTO categories (name, description, parent_category_id) 
SELECT 'Snacks & Sweets', 'Chips, cookies, candy, nuts', category_id FROM categories WHERE name = 'Food & Beverages';

INSERT INTO categories (name, description, parent_category_id) 
SELECT 'Frozen Foods', 'Frozen meals, ice cream, frozen vegetables', category_id FROM categories WHERE name = 'Food & Beverages';

-- Insert default units of measure
-- Volume units (base: liter)
INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit) VALUES 
    ('Liter', 'L', 'volume', 'Base unit for volume', true);

INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit, base_unit_id, conversion_factor) 
SELECT 'Milliliter', 'mL', 'volume', 'Milliliter', false, unit_id, 1000 FROM units_of_measure WHERE symbol = 'L';

INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit, base_unit_id, conversion_factor) 
SELECT 'Gallon', 'gal', 'volume', 'US Gallon', false, unit_id, 0.264172 FROM units_of_measure WHERE symbol = 'L';

INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit, base_unit_id, conversion_factor) 
SELECT 'Fluid Ounce', 'fl oz', 'volume', 'US Fluid Ounce', false, unit_id, 33.814 FROM units_of_measure WHERE symbol = 'L';

-- Weight units (base: kilogram)
INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit) VALUES 
    ('Kilogram', 'kg', 'weight', 'Base unit for weight', true);

INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit, base_unit_id, conversion_factor) 
SELECT 'Gram', 'g', 'weight', 'Gram', false, unit_id, 1000 FROM units_of_measure WHERE symbol = 'kg';

INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit, base_unit_id, conversion_factor) 
SELECT 'Pound', 'lb', 'weight', 'Pound', false, unit_id, 2.20462 FROM units_of_measure WHERE symbol = 'kg';

INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit, base_unit_id, conversion_factor) 
SELECT 'Ounce', 'oz', 'weight', 'Ounce', false, unit_id, 35.274 FROM units_of_measure WHERE symbol = 'kg';

-- Count units
INSERT INTO units_of_measure (name, symbol, type, description, is_base_unit) VALUES 
    ('Piece', 'pc', 'count', 'Individual pieces', true),
    ('Dozen', 'dz', 'count', 'Twelve pieces', true),
    ('Pack', 'pk', 'count', 'Package or pack', true),
    ('Box', 'box', 'count', 'Box or container', true),
    ('Bottle', 'btl', 'count', 'Bottle', true),
    ('Can', 'can', 'count', 'Can or tin', true),
    ('Bag', 'bag', 'count', 'Bag or pouch', true);
