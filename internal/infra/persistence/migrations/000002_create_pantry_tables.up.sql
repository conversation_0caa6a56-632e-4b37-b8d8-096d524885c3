-- Create pantries table
CREATE TABLE pantries (
    pantry_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for pantries
CREATE INDEX idx_pantries_owner_user_id ON pantries(owner_user_id);
CREATE INDEX idx_pantries_name ON pantries(name);
CREATE INDEX idx_pantries_deleted_at ON pantries(deleted_at);
CREATE UNIQUE INDEX idx_pantries_owner_name_unique ON pantries(owner_user_id, name) WHERE deleted_at IS NULL;

-- Create pantry_memberships table
CREATE TABLE pantry_memberships (
    pantry_membership_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL DEFAULT 'editor' CHECK (role IN ('owner', 'admin', 'editor', 'viewer')),
    status VARCHAR(30) NOT NULL DEFAULT 'pending_invitation' CHECK (status IN ('pending_invitation', 'active', 'inactive', 'removed')),
    joined_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    invited_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for pantry_memberships
CREATE INDEX idx_pantry_memberships_pantry_id ON pantry_memberships(pantry_id);
CREATE INDEX idx_pantry_memberships_user_id ON pantry_memberships(user_id);
CREATE INDEX idx_pantry_memberships_status ON pantry_memberships(status);
CREATE INDEX idx_pantry_memberships_deleted_at ON pantry_memberships(deleted_at);
CREATE UNIQUE INDEX idx_pantry_memberships_unique ON pantry_memberships(pantry_id, user_id) WHERE deleted_at IS NULL;

-- Create pantry_locations table
CREATE TABLE pantry_locations (
    pantry_location_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

-- Create indexes for pantry_locations
CREATE INDEX idx_pantry_locations_pantry_id ON pantry_locations(pantry_id);
CREATE INDEX idx_pantry_locations_name ON pantry_locations(name);
CREATE INDEX idx_pantry_locations_deleted_at ON pantry_locations(deleted_at);
CREATE UNIQUE INDEX idx_pantry_locations_pantry_name_unique ON pantry_locations(pantry_id, name) WHERE deleted_at IS NULL;

-- Create trigger for pantries table
CREATE TRIGGER update_pantries_updated_at 
    BEFORE UPDATE ON pantries 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for pantry_locations table
CREATE TRIGGER update_pantry_locations_updated_at 
    BEFORE UPDATE ON pantry_locations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default pantry locations for common storage areas
-- This will be handled by the application logic instead of hardcoded data
