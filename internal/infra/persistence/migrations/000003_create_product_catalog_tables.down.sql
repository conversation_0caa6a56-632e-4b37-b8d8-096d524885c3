-- Drop triggers
DROP TRIGGER IF EXISTS update_product_variants_updated_at ON product_variants;
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
DROP TRIGGER IF EXISTS update_units_of_measure_updated_at ON units_of_measure;
DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;

-- Drop tables (in reverse order due to foreign key constraints)
DROP TABLE IF EXISTS product_variants;
DROP TABLE IF EXISTS products;
DROP TABLE IF EXISTS units_of_measure;
DROP TABLE IF EXISTS categories;
