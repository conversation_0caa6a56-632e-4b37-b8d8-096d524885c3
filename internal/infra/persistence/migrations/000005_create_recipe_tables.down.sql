-- Drop indexes
DROP INDEX IF EXISTS idx_recipes_user_id;
DROP INDEX IF EXISTS idx_recipes_is_public;
DROP INDEX IF EXISTS idx_recipes_is_favorite;
DROP INDEX IF EXISTS idx_recipes_difficulty;
DROP INDEX IF EXISTS idx_recipes_cuisine;
DROP INDEX IF EXISTS idx_recipes_category;
DROP INDEX IF EXISTS idx_recipes_created_at;
DROP INDEX IF EXISTS idx_recipes_updated_at;
DROP INDEX IF EXISTS idx_recipes_cook_count;
DROP INDEX IF EXISTS idx_recipes_rating;

DROP INDEX IF EXISTS idx_recipe_ingredients_recipe_id;
DROP INDEX IF EXISTS idx_recipe_ingredients_product_variant_id;
DROP INDEX IF EXISTS idx_recipe_ingredients_order;

DROP INDEX IF EXISTS idx_recipe_instructions_recipe_id;
DROP INDEX IF EXISTS idx_recipe_instructions_step_number;

DROP INDEX IF EXISTS idx_recipe_media_recipe_id;
DROP INDEX IF EXISTS idx_recipe_media_type;
DROP INDEX IF EXISTS idx_recipe_media_is_main;

DROP INDEX IF EXISTS idx_recipe_nutrition_recipe_id;

DROP INDEX IF EXISTS idx_recipe_reviews_recipe_id;
DROP INDEX IF EXISTS idx_recipe_reviews_user_id;
DROP INDEX IF EXISTS idx_recipe_reviews_rating;

DROP INDEX IF EXISTS idx_recipe_collections_user_id;
DROP INDEX IF EXISTS idx_recipe_collections_is_public;

DROP INDEX IF EXISTS idx_recipe_collection_items_collection_id;
DROP INDEX IF EXISTS idx_recipe_collection_items_recipe_id;
DROP INDEX IF EXISTS idx_recipe_collection_items_order;

DROP INDEX IF EXISTS idx_recipe_tags_name;
DROP INDEX IF EXISTS idx_recipe_tags_usage_count;

DROP INDEX IF EXISTS idx_recipes_title_search;
DROP INDEX IF EXISTS idx_recipes_description_search;

-- Drop tables in reverse order of creation (respecting foreign key constraints)
DROP TABLE IF EXISTS recipe_recipe_tags;
DROP TABLE IF EXISTS recipe_collection_items;
DROP TABLE IF EXISTS recipe_collections;
DROP TABLE IF EXISTS recipe_reviews;
DROP TABLE IF EXISTS recipe_nutrition;
DROP TABLE IF EXISTS recipe_media;
DROP TABLE IF EXISTS recipe_instructions;
DROP TABLE IF EXISTS recipe_ingredients;
DROP TABLE IF EXISTS recipes;
DROP TABLE IF EXISTS recipe_tags;
