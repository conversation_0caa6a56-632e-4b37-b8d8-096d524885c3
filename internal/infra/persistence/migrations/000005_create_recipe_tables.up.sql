-- Create recipe tags table
CREATE TABLE recipe_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7), -- hex color
    icon VARCHAR(50),
    is_system BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recipes table
CREATE TABLE recipes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    cuisine VARCHAR(100),
    category VARCHAR(100),
    difficulty VARCHAR(20) DEFAULT 'medium' CHECK (difficulty IN ('easy', 'medium', 'hard', 'expert')),
    prep_time INTEGER, -- minutes
    cook_time INTEGER, -- minutes
    total_time INTEGER, -- minutes
    servings INTEGER DEFAULT 4,
    calories INTEGER,
    is_public BOOLEAN DEFAULT FALSE,
    is_favorite BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2), -- 1-5 stars
    cook_count INTEGER DEFAULT 0,
    last_cooked_at TIMESTAMP WITH TIME ZONE,
    source VARCHAR(500), -- URL or book reference
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recipe ingredients table
CREATE TABLE recipe_ingredients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    product_variant_id UUID REFERENCES product_variants(variant_id),
    name VARCHAR(200) NOT NULL, -- fallback if no product variant
    quantity DECIMAL(10,3) NOT NULL,
    unit_of_measure_id UUID REFERENCES units_of_measure(unit_id),
    unit VARCHAR(50), -- fallback unit
    preparation VARCHAR(200), -- "diced", "chopped", etc.
    is_optional BOOLEAN DEFAULT FALSE,
    is_garnish BOOLEAN DEFAULT FALSE,
    "order" INTEGER NOT NULL,
    notes VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recipe instructions table
CREATE TABLE recipe_instructions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    step_number INTEGER NOT NULL,
    title VARCHAR(200),
    instruction TEXT NOT NULL,
    duration INTEGER, -- minutes for this step
    temperature INTEGER, -- celsius
    image_url VARCHAR(500),
    video_url VARCHAR(500),
    tips TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recipe media table
CREATE TABLE recipe_media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('image', 'video')),
    url VARCHAR(500) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    size BIGINT, -- bytes
    mime_type VARCHAR(100),
    width INTEGER,
    height INTEGER,
    duration INTEGER, -- seconds for videos
    is_main BOOLEAN DEFAULT FALSE,
    "order" INTEGER DEFAULT 0,
    caption VARCHAR(500),
    alt_text VARCHAR(200),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recipe nutrition table
CREATE TABLE recipe_nutrition (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID NOT NULL UNIQUE REFERENCES recipes(id) ON DELETE CASCADE,
    calories INTEGER,
    protein DECIMAL(8,2), -- grams
    carbohydrates DECIMAL(8,2), -- grams
    fat DECIMAL(8,2), -- grams
    fiber DECIMAL(8,2), -- grams
    sugar DECIMAL(8,2), -- grams
    sodium DECIMAL(8,2), -- milligrams
    cholesterol DECIMAL(8,2), -- milligrams
    vitamin_a DECIMAL(8,2), -- IU
    vitamin_c DECIMAL(8,2), -- milligrams
    calcium DECIMAL(8,2), -- milligrams
    iron DECIMAL(8,2), -- milligrams
    serving_size VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recipe reviews table
CREATE TABLE recipe_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    image_url VARCHAR(500),
    cooked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(recipe_id, user_id) -- One review per user per recipe
);

-- Create recipe collections table
CREATE TABLE recipe_collections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    image_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create recipe collection items table
CREATE TABLE recipe_collection_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    collection_id UUID NOT NULL REFERENCES recipe_collections(id) ON DELETE CASCADE,
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    notes VARCHAR(500),
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(collection_id, recipe_id) -- One recipe per collection
);

-- Create many-to-many relationship table for recipes and tags
CREATE TABLE recipe_recipe_tags (
    recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
    recipe_tag_id UUID NOT NULL REFERENCES recipe_tags(id) ON DELETE CASCADE,
    PRIMARY KEY (recipe_id, recipe_tag_id)
);

-- Create indexes for better performance
CREATE INDEX idx_recipes_user_id ON recipes(user_id);
CREATE INDEX idx_recipes_is_public ON recipes(is_public);
CREATE INDEX idx_recipes_is_favorite ON recipes(is_favorite);
CREATE INDEX idx_recipes_difficulty ON recipes(difficulty);
CREATE INDEX idx_recipes_cuisine ON recipes(cuisine);
CREATE INDEX idx_recipes_category ON recipes(category);
CREATE INDEX idx_recipes_created_at ON recipes(created_at);
CREATE INDEX idx_recipes_updated_at ON recipes(updated_at);
CREATE INDEX idx_recipes_cook_count ON recipes(cook_count);
CREATE INDEX idx_recipes_rating ON recipes(rating);

CREATE INDEX idx_recipe_ingredients_recipe_id ON recipe_ingredients(recipe_id);
CREATE INDEX idx_recipe_ingredients_product_variant_id ON recipe_ingredients(product_variant_id);
CREATE INDEX idx_recipe_ingredients_order ON recipe_ingredients("order");

CREATE INDEX idx_recipe_instructions_recipe_id ON recipe_instructions(recipe_id);
CREATE INDEX idx_recipe_instructions_step_number ON recipe_instructions(step_number);

CREATE INDEX idx_recipe_media_recipe_id ON recipe_media(recipe_id);
CREATE INDEX idx_recipe_media_type ON recipe_media(type);
CREATE INDEX idx_recipe_media_is_main ON recipe_media(is_main);

CREATE INDEX idx_recipe_nutrition_recipe_id ON recipe_nutrition(recipe_id);

CREATE INDEX idx_recipe_reviews_recipe_id ON recipe_reviews(recipe_id);
CREATE INDEX idx_recipe_reviews_user_id ON recipe_reviews(user_id);
CREATE INDEX idx_recipe_reviews_rating ON recipe_reviews(rating);

CREATE INDEX idx_recipe_collections_user_id ON recipe_collections(user_id);
CREATE INDEX idx_recipe_collections_is_public ON recipe_collections(is_public);

CREATE INDEX idx_recipe_collection_items_collection_id ON recipe_collection_items(collection_id);
CREATE INDEX idx_recipe_collection_items_recipe_id ON recipe_collection_items(recipe_id);
CREATE INDEX idx_recipe_collection_items_order ON recipe_collection_items("order");

CREATE INDEX idx_recipe_tags_name ON recipe_tags(name);
CREATE INDEX idx_recipe_tags_usage_count ON recipe_tags(usage_count);

-- Create full-text search indexes
CREATE INDEX idx_recipes_title_search ON recipes USING gin(to_tsvector('english', title));
CREATE INDEX idx_recipes_description_search ON recipes USING gin(to_tsvector('english', description));

-- Insert some default system tags
INSERT INTO recipe_tags (name, description, is_system, color, icon) VALUES
('vegetarian', 'Vegetarian recipes', TRUE, '#4CAF50', '🥬'),
('vegan', 'Vegan recipes', TRUE, '#8BC34A', '🌱'),
('gluten-free', 'Gluten-free recipes', TRUE, '#FF9800', '🌾'),
('dairy-free', 'Dairy-free recipes', TRUE, '#2196F3', '🥛'),
('low-carb', 'Low carbohydrate recipes', TRUE, '#9C27B0', '🥗'),
('keto', 'Ketogenic diet recipes', TRUE, '#673AB7', '🥑'),
('paleo', 'Paleo diet recipes', TRUE, '#795548', '🍖'),
('quick', 'Quick and easy recipes', TRUE, '#F44336', '⚡'),
('healthy', 'Healthy recipes', TRUE, '#4CAF50', '💚'),
('comfort-food', 'Comfort food recipes', TRUE, '#FF5722', '🍲'),
('dessert', 'Dessert recipes', TRUE, '#E91E63', '🍰'),
('breakfast', 'Breakfast recipes', TRUE, '#FFC107', '🍳'),
('lunch', 'Lunch recipes', TRUE, '#FF9800', '🥪'),
('dinner', 'Dinner recipes', TRUE, '#3F51B5', '🍽️'),
('snack', 'Snack recipes', TRUE, '#009688', '🍿'),
('appetizer', 'Appetizer recipes', TRUE, '#607D8B', '🥨'),
('soup', 'Soup recipes', TRUE, '#00BCD4', '🍜'),
('salad', 'Salad recipes', TRUE, '#8BC34A', '🥗'),
('pasta', 'Pasta recipes', TRUE, '#FFEB3B', '🍝'),
('pizza', 'Pizza recipes', TRUE, '#FF5722', '🍕');
