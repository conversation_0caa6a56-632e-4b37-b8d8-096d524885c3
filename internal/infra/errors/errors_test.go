package errors

import (
	"fmt"
	"net/http"
	"testing"
)

func TestNewAppError(t *testing.T) {
	code := ErrCodeNotFound
	message := "Resource not found"
	
	err := New(code, message)
	
	if err.Code != code {
		t.<PERSON>rrorf("Expected code %s, got %s", code, err.Code)
	}
	
	if err.Message != message {
		t.Errorf("Expected message %s, got %s", message, err.Message)
	}
	
	if err.HTTPStatus != http.StatusNotFound {
		t.Errorf("Expected HTTP status %d, got %d", http.StatusNotFound, err.HTTPStatus)
	}
}

func TestAppErrorWithDetails(t *testing.T) {
	err := New(ErrCodeValidationFailed, "Validation failed")
	details := map[string]interface{}{
		"field1": "error1",
		"field2": "error2",
	}
	
	err.WithDetails(details)
	
	if len(err.Details) != 2 {
		t.<PERSON>("Expected 2 details, got %d", len(err.Details))
	}
	
	if err.Details["field1"] != "error1" {
		t.<PERSON><PERSON><PERSON>("Expected field1 to be 'error1', got %v", err.Details["field1"])
	}
}

func TestAppErrorWithCause(t *testing.T) {
	originalErr := fmt.Errorf("original error")
	err := New(ErrCodeInternalError, "Internal error").WithCause(originalErr)
	
	if err.Cause != originalErr {
		t.Errorf("Expected cause to be %v, got %v", originalErr, err.Cause)
	}
	
	if err.Unwrap() != originalErr {
		t.Errorf("Expected unwrap to return %v, got %v", originalErr, err.Unwrap())
	}
}

func TestWrapError(t *testing.T) {
	originalErr := fmt.Errorf("database connection failed")
	wrappedErr := Wrap(originalErr, ErrCodeDatabaseError, "Failed to connect to database")
	
	if wrappedErr.Code != ErrCodeDatabaseError {
		t.Errorf("Expected code %s, got %s", ErrCodeDatabaseError, wrappedErr.Code)
	}
	
	if wrappedErr.Cause != originalErr {
		t.Errorf("Expected cause to be %v, got %v", originalErr, wrappedErr.Cause)
	}
	
	if wrappedErr.HTTPStatus != http.StatusInternalServerError {
		t.Errorf("Expected HTTP status %d, got %d", http.StatusInternalServerError, wrappedErr.HTTPStatus)
	}
}

func TestErrorString(t *testing.T) {
	// Test without cause
	err := New(ErrCodeNotFound, "User not found")
	expected := "NOT_FOUND: User not found"
	if err.Error() != expected {
		t.Errorf("Expected error string %s, got %s", expected, err.Error())
	}
	
	// Test with cause
	originalErr := fmt.Errorf("database error")
	errWithCause := New(ErrCodeInternalError, "Internal error").WithCause(originalErr)
	expectedWithCause := "INTERNAL_ERROR: Internal error (caused by: database error)"
	if errWithCause.Error() != expectedWithCause {
		t.Errorf("Expected error string %s, got %s", expectedWithCause, errWithCause.Error())
	}
}

func TestGetHTTPStatus(t *testing.T) {
	tests := []struct {
		code           ErrorCode
		expectedStatus int
	}{
		{ErrCodeUnauthorized, http.StatusUnauthorized},
		{ErrCodeForbidden, http.StatusForbidden},
		{ErrCodeNotFound, http.StatusNotFound},
		{ErrCodeValidationFailed, http.StatusBadRequest},
		{ErrCodeAlreadyExists, http.StatusConflict},
		{ErrCodeInternalError, http.StatusInternalServerError},
		{ErrCodeRateLimitExceeded, http.StatusTooManyRequests},
	}
	
	for _, test := range tests {
		err := New(test.code, "test message")
		if err.HTTPStatus != test.expectedStatus {
			t.Errorf("Expected HTTP status %d for code %s, got %d", 
				test.expectedStatus, test.code, err.HTTPStatus)
		}
	}
}

func TestIsAppError(t *testing.T) {
	appErr := New(ErrCodeNotFound, "Not found")
	regularErr := fmt.Errorf("regular error")
	
	if !IsAppError(appErr) {
		t.Error("Expected IsAppError to return true for AppError")
	}
	
	if IsAppError(regularErr) {
		t.Error("Expected IsAppError to return false for regular error")
	}
}

func TestGetAppError(t *testing.T) {
	appErr := New(ErrCodeNotFound, "Not found")
	regularErr := fmt.Errorf("regular error")
	
	extracted := GetAppError(appErr)
	if extracted != appErr {
		t.Error("Expected GetAppError to return the same AppError")
	}
	
	extracted = GetAppError(regularErr)
	if extracted != nil {
		t.Error("Expected GetAppError to return nil for regular error")
	}
}

func TestValidationError(t *testing.T) {
	fields := map[string]string{
		"email":    "Invalid email format",
		"password": "Password too short",
	}
	
	validationErr := NewValidationError("Validation failed", fields)
	
	if validationErr.Code != ErrCodeValidationFailed {
		t.Errorf("Expected code %s, got %s", ErrCodeValidationFailed, validationErr.Code)
	}
	
	if len(validationErr.Fields) != 2 {
		t.Errorf("Expected 2 fields, got %d", len(validationErr.Fields))
	}
	
	if validationErr.Fields["email"] != "Invalid email format" {
		t.Errorf("Expected email error 'Invalid email format', got %s", validationErr.Fields["email"])
	}
}

func TestBusinessError(t *testing.T) {
	rule := "MAX_PANTRIES_PER_USER"
	message := "User cannot have more than 5 pantries"
	
	businessErr := NewBusinessError(rule, message)
	
	if businessErr.Code != ErrCodeBusinessRuleViolation {
		t.Errorf("Expected code %s, got %s", ErrCodeBusinessRuleViolation, businessErr.Code)
	}
	
	if businessErr.Rule != rule {
		t.Errorf("Expected rule %s, got %s", rule, businessErr.Rule)
	}
	
	if businessErr.Message != message {
		t.Errorf("Expected message %s, got %s", message, businessErr.Message)
	}
}
