package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode represents application-specific error codes
type ErrorCode string

const (
	// Authentication errors
	ErrCodeInvalidCredentials    ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeTokenExpired         ErrorCode = "TOKEN_EXPIRED"
	ErrCodeTokenInvalid         ErrorCode = "TOKEN_INVALID"
	ErrCodeUnauthorized         ErrorCode = "UNAUTHORIZED"
	
	// Authorization errors
	ErrCodeForbidden            ErrorCode = "FORBIDDEN"
	ErrCodeInsufficientPermissions ErrorCode = "INSUFFICIENT_PERMISSIONS"
	
	// Validation errors
	ErrCodeValidationFailed     ErrorCode = "VALIDATION_FAILED"
	ErrCodeInvalidInput         ErrorCode = "INVALID_INPUT"
	ErrCodeMissingRequiredField ErrorCode = "MISSING_REQUIRED_FIELD"
	
	// Resource errors
	ErrCodeNotFound             ErrorCode = "NOT_FOUND"
	ErrCodeAlreadyExists        ErrorCode = "ALREADY_EXISTS"
	ErrCodeConflict             ErrorCode = "CONFLICT"
	
	// Business logic errors
	ErrCodeBusinessRuleViolation ErrorCode = "BUSINESS_RULE_VIOLATION"
	ErrCodeInvalidOperation      ErrorCode = "INVALID_OPERATION"
	ErrCodeQuotaExceeded        ErrorCode = "QUOTA_EXCEEDED"
	
	// System errors
	ErrCodeInternalError        ErrorCode = "INTERNAL_ERROR"
	ErrCodeServiceUnavailable   ErrorCode = "SERVICE_UNAVAILABLE"
	ErrCodeDatabaseError        ErrorCode = "DATABASE_ERROR"
	ErrCodeExternalServiceError ErrorCode = "EXTERNAL_SERVICE_ERROR"
	
	// Rate limiting
	ErrCodeRateLimitExceeded    ErrorCode = "RATE_LIMIT_EXCEEDED"
	
	// Idempotency
	ErrCodeIdempotencyConflict  ErrorCode = "IDEMPOTENCY_CONFLICT"
)

// AppError represents a structured application error
type AppError struct {
	Code       ErrorCode              `json:"code"`
	Message    string                 `json:"message"`
	Details    map[string]interface{} `json:"details,omitempty"`
	HTTPStatus int                    `json:"-"`
	Cause      error                  `json:"-"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap returns the underlying error
func (e *AppError) Unwrap() error {
	return e.Cause
}

// WithDetails adds details to the error
func (e *AppError) WithDetails(details map[string]interface{}) *AppError {
	e.Details = details
	return e
}

// WithCause adds a cause to the error
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// New creates a new AppError
func New(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getDefaultHTTPStatus(code),
	}
}

// NewWithStatus creates a new AppError with custom HTTP status
func NewWithStatus(code ErrorCode, message string, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
	}
}

// Wrap wraps an existing error with application context
func Wrap(err error, code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getDefaultHTTPStatus(code),
		Cause:      err,
	}
}

// getDefaultHTTPStatus returns the default HTTP status for an error code
func getDefaultHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrCodeInvalidCredentials, ErrCodeTokenExpired, ErrCodeTokenInvalid, ErrCodeUnauthorized:
		return http.StatusUnauthorized
	case ErrCodeForbidden, ErrCodeInsufficientPermissions:
		return http.StatusForbidden
	case ErrCodeValidationFailed, ErrCodeInvalidInput, ErrCodeMissingRequiredField:
		return http.StatusBadRequest
	case ErrCodeNotFound:
		return http.StatusNotFound
	case ErrCodeAlreadyExists, ErrCodeConflict, ErrCodeBusinessRuleViolation:
		return http.StatusConflict
	case ErrCodeInvalidOperation:
		return http.StatusBadRequest
	case ErrCodeQuotaExceeded:
		return http.StatusTooManyRequests
	case ErrCodeRateLimitExceeded:
		return http.StatusTooManyRequests
	case ErrCodeIdempotencyConflict:
		return http.StatusConflict
	case ErrCodeServiceUnavailable:
		return http.StatusServiceUnavailable
	case ErrCodeInternalError, ErrCodeDatabaseError, ErrCodeExternalServiceError:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// Predefined common errors
var (
	ErrInvalidCredentials = New(ErrCodeInvalidCredentials, "Invalid username or password")
	ErrTokenExpired      = New(ErrCodeTokenExpired, "Token has expired")
	ErrTokenInvalid      = New(ErrCodeTokenInvalid, "Invalid token")
	ErrUnauthorized      = New(ErrCodeUnauthorized, "Authentication required")
	ErrForbidden         = New(ErrCodeForbidden, "Access denied")
	ErrNotFound          = New(ErrCodeNotFound, "Resource not found")
	ErrAlreadyExists     = New(ErrCodeAlreadyExists, "Resource already exists")
	ErrValidationFailed  = New(ErrCodeValidationFailed, "Validation failed")
	ErrInternalError     = New(ErrCodeInternalError, "Internal server error")
	ErrServiceUnavailable = New(ErrCodeServiceUnavailable, "Service temporarily unavailable")
	ErrRateLimitExceeded = New(ErrCodeRateLimitExceeded, "Rate limit exceeded")
)

// ValidationError represents a validation error with field-specific details
type ValidationError struct {
	*AppError
	Fields map[string]string `json:"fields"`
}

// NewValidationError creates a new validation error
func NewValidationError(message string, fields map[string]string) *ValidationError {
	return &ValidationError{
		AppError: New(ErrCodeValidationFailed, message),
		Fields:   fields,
	}
}

// BusinessError represents a business rule violation
type BusinessError struct {
	*AppError
	Rule string `json:"rule"`
}

// NewBusinessError creates a new business error
func NewBusinessError(rule, message string) *BusinessError {
	return &BusinessError{
		AppError: New(ErrCodeBusinessRuleViolation, message),
		Rule:     rule,
	}
}

// IsAppError checks if an error is an AppError
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// GetAppError extracts AppError from error chain
func GetAppError(err error) *AppError {
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}
	return nil
}

// GetHTTPStatus extracts HTTP status from error
func GetHTTPStatus(err error) int {
	if appErr := GetAppError(err); appErr != nil {
		return appErr.HTTPStatus
	}
	return http.StatusInternalServerError
}
