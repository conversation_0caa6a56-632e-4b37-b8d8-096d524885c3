package domain

import (
	"context"
	"time"

	"github.com/google/uuid"
)

// NotificationChannel represents different notification delivery channels
type NotificationChannel string

const (
	NotificationChannelEmail     NotificationChannel = "email"
	NotificationChannelTelegram  NotificationChannel = "telegram"
	NotificationChannelSupabase  NotificationChannel = "supabase"
	NotificationChannelWebhook   NotificationChannel = "webhook"
	NotificationChannelInApp     NotificationChannel = "in_app"
)

// NotificationPriority represents the priority level of a notification
type NotificationPriority string

const (
	NotificationPriorityLow      NotificationPriority = "low"
	NotificationPriorityMedium   NotificationPriority = "medium"
	NotificationPriorityHigh     NotificationPriority = "high"
	NotificationPriorityCritical NotificationPriority = "critical"
)

// NotificationType represents different types of notifications
type NotificationType string

const (
	NotificationTypeExpirationWarning NotificationType = "expiration_warning"
	NotificationTypeExpirationAlert   NotificationType = "expiration_alert"
	NotificationTypeExpirationCritical NotificationType = "expiration_critical"
	NotificationTypeInventoryLowStock NotificationType = "inventory_low_stock"
	NotificationTypeInventoryEmpty    NotificationType = "inventory_empty"
	NotificationTypeRecipeReady       NotificationType = "recipe_ready"
	NotificationTypeShoppingReminder  NotificationType = "shopping_reminder"
)

// Notification represents a notification to be sent
type Notification struct {
	ID          uuid.UUID                  `json:"id"`
	UserID      uuid.UUID                  `json:"user_id"`
	PantryID    *uuid.UUID                 `json:"pantry_id,omitempty"`
	Type        NotificationType           `json:"type"`
	Channel     NotificationChannel        `json:"channel"`
	Priority    NotificationPriority       `json:"priority"`
	Title       string                     `json:"title"`
	Message     string                     `json:"message"`
	Data        map[string]interface{}     `json:"data,omitempty"`
	ScheduledAt *time.Time                 `json:"scheduled_at,omitempty"`
	SentAt      *time.Time                 `json:"sent_at,omitempty"`
	Status      NotificationStatus         `json:"status"`
	Error       *string                    `json:"error,omitempty"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
}

// NotificationStatus represents the status of a notification
type NotificationStatus string

const (
	NotificationStatusPending NotificationStatus = "pending"
	NotificationStatusSent    NotificationStatus = "sent"
	NotificationStatusFailed  NotificationStatus = "failed"
	NotificationStatusSkipped NotificationStatus = "skipped"
)

// NotificationProvider defines the interface for notification providers
type NotificationProvider interface {
	GetChannel() NotificationChannel
	Send(ctx context.Context, notification *Notification) error
	IsEnabled() bool
	ValidateConfig() error
}

// NotificationService defines the interface for notification management
type NotificationService interface {
	SendNotification(ctx context.Context, notification *Notification) error
	SendBulkNotifications(ctx context.Context, notifications []*Notification) error
	RegisterProvider(provider NotificationProvider) error
	GetSupportedChannels() []NotificationChannel
}

// NotificationRepository defines the interface for notification persistence
type NotificationRepository interface {
	Create(notification *Notification) error
	GetByID(id uuid.UUID) (*Notification, error)
	GetByUserID(userID uuid.UUID, page, limit int) ([]*Notification, int64, error)
	GetPendingNotifications(limit int) ([]*Notification, error)
	Update(notification *Notification) error
	MarkAsSent(id uuid.UUID) error
	MarkAsFailed(id uuid.UUID, errorMsg string) error
	Delete(id uuid.UUID) error
}

// NewNotification creates a new notification
func NewNotification(
	userID uuid.UUID,
	pantryID *uuid.UUID,
	notificationType NotificationType,
	channel NotificationChannel,
	priority NotificationPriority,
	title, message string,
	data map[string]interface{},
	scheduledAt *time.Time,
) *Notification {
	now := time.Now()
	return &Notification{
		ID:          uuid.New(),
		UserID:      userID,
		PantryID:    pantryID,
		Type:        notificationType,
		Channel:     channel,
		Priority:    priority,
		Title:       title,
		Message:     message,
		Data:        data,
		ScheduledAt: scheduledAt,
		Status:      NotificationStatusPending,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// MarkAsSent marks the notification as sent
func (n *Notification) MarkAsSent() {
	now := time.Now()
	n.Status = NotificationStatusSent
	n.SentAt = &now
	n.UpdatedAt = now
}

// MarkAsFailed marks the notification as failed
func (n *Notification) MarkAsFailed(errorMsg string) {
	n.Status = NotificationStatusFailed
	n.Error = &errorMsg
	n.UpdatedAt = time.Now()
}

// MarkAsSkipped marks the notification as skipped
func (n *Notification) MarkAsSkipped() {
	n.Status = NotificationStatusSkipped
	n.UpdatedAt = time.Now()
}

// IsScheduled checks if the notification is scheduled for future delivery
func (n *Notification) IsScheduled() bool {
	return n.ScheduledAt != nil && n.ScheduledAt.After(time.Now())
}

// IsReadyToSend checks if the notification is ready to be sent
func (n *Notification) IsReadyToSend() bool {
	if n.Status != NotificationStatusPending {
		return false
	}
	
	if n.ScheduledAt == nil {
		return true
	}
	
	return !n.ScheduledAt.After(time.Now())
}

// NotificationTemplate represents a template for notifications
type NotificationTemplate struct {
	Type     NotificationType `json:"type"`
	Channel  NotificationChannel `json:"channel"`
	Title    string `json:"title"`
	Message  string `json:"message"`
	Priority NotificationPriority `json:"priority"`
}

// Common notification templates
var NotificationTemplates = map[NotificationType]map[NotificationChannel]NotificationTemplate{
	NotificationTypeExpirationWarning: {
		NotificationChannelEmail: {
			Type:     NotificationTypeExpirationWarning,
			Channel:  NotificationChannelEmail,
			Title:    "Items Expiring Soon",
			Message:  "You have {{.count}} items expiring in the next {{.days}} days in your {{.pantry_name}} pantry.",
			Priority: NotificationPriorityMedium,
		},
		NotificationChannelTelegram: {
			Type:     NotificationTypeExpirationWarning,
			Channel:  NotificationChannelTelegram,
			Title:    "⚠️ Items Expiring Soon",
			Message:  "🥛 {{.count}} items expiring in {{.days}} days in {{.pantry_name}}",
			Priority: NotificationPriorityMedium,
		},
	},
	NotificationTypeExpirationAlert: {
		NotificationChannelEmail: {
			Type:     NotificationTypeExpirationAlert,
			Channel:  NotificationChannelEmail,
			Title:    "Items Expiring Tomorrow",
			Message:  "{{.count}} items in your {{.pantry_name}} pantry are expiring tomorrow. Check them now to avoid waste.",
			Priority: NotificationPriorityHigh,
		},
		NotificationChannelTelegram: {
			Type:     NotificationTypeExpirationAlert,
			Channel:  NotificationChannelTelegram,
			Title:    "🚨 Items Expiring Tomorrow",
			Message:  "⏰ {{.count}} items expiring tomorrow in {{.pantry_name}}!",
			Priority: NotificationPriorityHigh,
		},
	},
	NotificationTypeExpirationCritical: {
		NotificationChannelEmail: {
			Type:     NotificationTypeExpirationCritical,
			Channel:  NotificationChannelEmail,
			Title:    "Items Expired Today",
			Message:  "{{.count}} items in your {{.pantry_name}} pantry have expired today. Please check and remove them.",
			Priority: NotificationPriorityCritical,
		},
		NotificationChannelTelegram: {
			Type:     NotificationTypeExpirationCritical,
			Channel:  NotificationChannelTelegram,
			Title:    "🔴 Items Expired Today",
			Message:  "💀 {{.count}} items expired today in {{.pantry_name}}! Remove them now.",
			Priority: NotificationPriorityCritical,
		},
	},
}
