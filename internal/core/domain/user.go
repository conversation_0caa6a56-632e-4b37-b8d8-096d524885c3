package domain

import (
	"time"

	"github.com/google/uuid"
)

// User represents a user in the system
type User struct {
	ID                uuid.UUID  `json:"id"`
	Username          string     `json:"username"`
	Email             string     `json:"email"`
	PasswordHash      string     `json:"-"`
	FirstName         *string    `json:"first_name,omitempty"`
	LastName          *string    `json:"last_name,omitempty"`
	ProfilePictureURL *string    `json:"profile_picture_url,omitempty"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	DeletedAt         *time.Time `json:"deleted_at,omitempty"`
	
	// Domain events
	events []DomainEvent
}

// New<PERSON>ser creates a new user
func NewUser(username, email, passwordHash string) *User {
	user := &User{
		ID:           uuid.New(),
		Username:     username,
		Email:        email,
		PasswordHash: passwordHash,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		events:       make([]DomainEvent, 0),
	}
	
	// Add domain event
	user.AddEvent(NewUserRegisteredEvent(user.ID, user.Email, user.Username))
	
	return user
}

// UpdateProfile updates user profile information
func (u *User) UpdateProfile(firstName, lastName *string, profilePictureURL *string) {
	u.FirstName = firstName
	u.LastName = lastName
	u.ProfilePictureURL = profilePictureURL
	u.UpdatedAt = time.Now()
	
	// Add domain event
	u.AddEvent(NewUserProfileUpdatedEvent(u.ID, u.Email))
}

// ChangePassword updates user password
func (u *User) ChangePassword(newPasswordHash string) {
	u.PasswordHash = newPasswordHash
	u.UpdatedAt = time.Now()
	
	// Add domain event
	u.AddEvent(NewUserPasswordChangedEvent(u.ID, u.Email))
}

// SoftDelete marks user as deleted
func (u *User) SoftDelete() {
	now := time.Now()
	u.DeletedAt = &now
	u.UpdatedAt = now
	
	// Add domain event
	u.AddEvent(NewUserDeletedEvent(u.ID, u.Email))
}

// IsDeleted checks if user is soft deleted
func (u *User) IsDeleted() bool {
	return u.DeletedAt != nil
}

// GetFullName returns user's full name
func (u *User) GetFullName() string {
	if u.FirstName != nil && u.LastName != nil {
		return *u.FirstName + " " + *u.LastName
	}
	if u.FirstName != nil {
		return *u.FirstName
	}
	if u.LastName != nil {
		return *u.LastName
	}
	return u.Username
}

// AddEvent adds a domain event
func (u *User) AddEvent(event DomainEvent) {
	u.events = append(u.events, event)
}

// GetEvents returns all domain events
func (u *User) GetEvents() []DomainEvent {
	return u.events
}

// ClearEvents clears all domain events
func (u *User) ClearEvents() {
	u.events = make([]DomainEvent, 0)
}

// RefreshToken represents a JWT refresh token
type RefreshToken struct {
	ID        uuid.UUID `json:"id"`
	UserID    uuid.UUID `json:"user_id"`
	TokenHash string    `json:"-"`
	ExpiresAt time.Time `json:"expires_at"`
	IsRevoked bool      `json:"is_revoked"`
	CreatedAt time.Time `json:"created_at"`
}

// NewRefreshToken creates a new refresh token
func NewRefreshToken(userID uuid.UUID, tokenHash string, expiresAt time.Time) *RefreshToken {
	return &RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: tokenHash,
		ExpiresAt: expiresAt,
		IsRevoked: false,
		CreatedAt: time.Now(),
	}
}

// Revoke marks the refresh token as revoked
func (rt *RefreshToken) Revoke() {
	rt.IsRevoked = true
}

// IsExpired checks if the refresh token is expired
func (rt *RefreshToken) IsExpired() bool {
	return time.Now().After(rt.ExpiresAt)
}

// IsValid checks if the refresh token is valid (not revoked and not expired)
func (rt *RefreshToken) IsValid() bool {
	return !rt.IsRevoked && !rt.IsExpired()
}

// UserRepository defines the interface for user data access
type UserRepository interface {
	// Create creates a new user
	Create(user *User) error
	
	// GetByID retrieves a user by ID
	GetByID(id uuid.UUID) (*User, error)
	
	// GetByEmail retrieves a user by email
	GetByEmail(email string) (*User, error)
	
	// GetByUsername retrieves a user by username
	GetByUsername(username string) (*User, error)
	
	// Update updates an existing user
	Update(user *User) error
	
	// Delete soft deletes a user
	Delete(id uuid.UUID) error
	
	// ExistsByEmail checks if a user exists with the given email
	ExistsByEmail(email string) (bool, error)
	
	// ExistsByUsername checks if a user exists with the given username
	ExistsByUsername(username string) (bool, error)
}

// RefreshTokenRepository defines the interface for refresh token data access
type RefreshTokenRepository interface {
	// Create creates a new refresh token
	Create(token *RefreshToken) error
	
	// GetByTokenHash retrieves a refresh token by its hash
	GetByTokenHash(tokenHash string) (*RefreshToken, error)
	
	// GetByUserID retrieves all refresh tokens for a user
	GetByUserID(userID uuid.UUID) ([]*RefreshToken, error)
	
	// Update updates an existing refresh token
	Update(token *RefreshToken) error
	
	// RevokeByUserID revokes all refresh tokens for a user
	RevokeByUserID(userID uuid.UUID) error
	
	// RevokeByTokenHash revokes a specific refresh token
	RevokeByTokenHash(tokenHash string) error
	
	// DeleteExpired deletes all expired refresh tokens
	DeleteExpired() error
}
