package domain

import (
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// Store represents a shopping location
type Store struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	Address     *string    `json:"address,omitempty"`
	City        *string    `json:"city,omitempty"`
	Country     *string    `json:"country,omitempty"`
	PhoneNumber *string    `json:"phone_number,omitempty"`
	Website     *string    `json:"website,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty"`
}

// Purchase represents a shopping transaction
type Purchase struct {
	ID                uuid.UUID      `json:"id"`
	PantryID          uuid.UUID      `json:"pantry_id"`
	PurchaseDate      time.Time      `json:"purchase_date"`
	TotalAmount       float64        `json:"total_amount"`
	Currency          string         `json:"currency"`
	StoreID           *uuid.UUID     `json:"store_id,omitempty"`
	StoreName         *string        `json:"store_name,omitempty"`
	ReceiptImageURL   *string        `json:"receipt_image_url,omitempty"`
	PurchasedByUserID uuid.UUID      `json:"purchased_by_user_id"`
	Notes             *string        `json:"notes,omitempty"`
	Items             []PurchaseItem `json:"items"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         *time.Time     `json:"deleted_at,omitempty"`

	// Domain events
	events []DomainEvent
}

// PurchaseItem represents an item in a purchase transaction
type PurchaseItem struct {
	ID               uuid.UUID  `json:"id"`
	PurchaseID       uuid.UUID  `json:"purchase_id"`
	ProductVariantID uuid.UUID  `json:"product_variant_id"`
	QuantityBought   float64    `json:"quantity_bought"`
	UnitOfMeasureID  uuid.UUID  `json:"unit_of_measure_id"`
	PricePerUnit     float64    `json:"price_per_unit"`
	TotalPrice       float64    `json:"total_price"`
	Notes            *string    `json:"notes,omitempty"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	DeletedAt        *time.Time `json:"deleted_at,omitempty"`
}

// NewStore creates a new store
func NewStore(name string, address, city, country, phoneNumber, website *string) *Store {
	now := time.Now()
	return &Store{
		ID:          uuid.New(),
		Name:        name,
		Address:     address,
		City:        city,
		Country:     country,
		PhoneNumber: phoneNumber,
		Website:     website,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// NewPurchase creates a new purchase transaction
func NewPurchase(
	pantryID uuid.UUID,
	purchaseDate time.Time,
	totalAmount float64,
	currency string,
	storeID *uuid.UUID,
	storeName *string,
	receiptImageURL *string,
	purchasedByUserID uuid.UUID,
	notes *string,
) (*Purchase, error) {
	if err := ValidatePurchaseAmount(totalAmount); err != nil {
		return nil, err
	}

	if err := ValidatePurchaseNotes(notes); err != nil {
		return nil, err
	}

	now := time.Now()
	purchase := &Purchase{
		ID:                uuid.New(),
		PantryID:          pantryID,
		PurchaseDate:      purchaseDate,
		TotalAmount:       totalAmount,
		Currency:          currency,
		StoreID:           storeID,
		StoreName:         storeName,
		ReceiptImageURL:   receiptImageURL,
		PurchasedByUserID: purchasedByUserID,
		Notes:             notes,
		Items:             make([]PurchaseItem, 0),
		CreatedAt:         now,
		UpdatedAt:         now,
		events:            make([]DomainEvent, 0),
	}

	// Add domain event
	purchase.AddEvent(NewPurchaseCreatedEvent(
		purchase.ID,
		purchase.PantryID,
		purchase.TotalAmount,
		purchase.Currency,
		purchase.PurchasedByUserID,
	))

	return purchase, nil
}

// NewPurchaseItem creates a new purchase item
func NewPurchaseItem(
	purchaseID uuid.UUID,
	productVariantID uuid.UUID,
	quantityBought float64,
	unitOfMeasureID uuid.UUID,
	pricePerUnit float64,
	notes *string,
) (*PurchaseItem, error) {
	if err := ValidatePurchaseQuantity(quantityBought); err != nil {
		return nil, err
	}

	if err := ValidatePurchasePrice(pricePerUnit); err != nil {
		return nil, err
	}

	now := time.Now()
	return &PurchaseItem{
		ID:               uuid.New(),
		PurchaseID:       purchaseID,
		ProductVariantID: productVariantID,
		QuantityBought:   quantityBought,
		UnitOfMeasureID:  unitOfMeasureID,
		PricePerUnit:     pricePerUnit,
		TotalPrice:       quantityBought * pricePerUnit,
		Notes:            notes,
		CreatedAt:        now,
		UpdatedAt:        now,
	}, nil
}

// AddPurchaseItem adds an item to the purchase
func (p *Purchase) AddPurchaseItem(item *PurchaseItem) {
	p.Items = append(p.Items, *item)
	p.UpdatedAt = time.Now()

	// Add domain event
	p.AddEvent(NewPurchaseItemAddedEvent(
		p.ID,
		item.ID,
		item.ProductVariantID,
		item.QuantityBought,
		item.UnitOfMeasureID,
		item.TotalPrice,
	))
}

// UpdateTotalAmount updates the total amount of the purchase
func (p *Purchase) UpdateTotalAmount() {
	var total float64
	for _, item := range p.Items {
		total += item.TotalPrice
	}
	p.TotalAmount = total
	p.UpdatedAt = time.Now()
}

// SoftDelete marks the purchase as deleted
func (p *Purchase) SoftDelete() {
	now := time.Now()
	p.DeletedAt = &now
	p.UpdatedAt = now

	// Add domain event
	p.AddEvent(NewPurchaseDeletedEvent(p.ID, p.PantryID))
}

// AddEvent adds a domain event
func (p *Purchase) AddEvent(event DomainEvent) {
	p.events = append(p.events, event)
}

// GetEvents returns all domain events
func (p *Purchase) GetEvents() []DomainEvent {
	return p.events
}

// ClearEvents clears all domain events
func (p *Purchase) ClearEvents() {
	p.events = make([]DomainEvent, 0)
}

// Validation functions

func ValidatePurchaseAmount(amount float64) error {
	if amount < 0 {
		return errors.NewValidationError("Invalid amount", map[string]string{
			"amount": "Amount cannot be negative",
		})
	}

	if amount > 999999.99 {
		return errors.NewValidationError("Invalid amount", map[string]string{
			"amount": "Amount is too large",
		})
	}

	return nil
}

func ValidatePurchaseQuantity(quantity float64) error {
	if quantity <= 0 {
		return errors.NewValidationError("Invalid quantity", map[string]string{
			"quantity": "Quantity must be positive",
		})
	}

	if quantity > 999999.99 {
		return errors.NewValidationError("Invalid quantity", map[string]string{
			"quantity": "Quantity is too large",
		})
	}

	return nil
}

func ValidatePurchasePrice(price float64) error {
	if price < 0 {
		return errors.NewValidationError("Invalid price", map[string]string{
			"price": "Price cannot be negative",
		})
	}

	if price > 999999.99 {
		return errors.NewValidationError("Invalid price", map[string]string{
			"price": "Price is too large",
		})
	}

	return nil
}

func ValidatePurchaseNotes(notes *string) error {
	if notes != nil && len(*notes) > 1000 {
		return errors.NewValidationError("Invalid notes", map[string]string{
			"notes": "Notes cannot exceed 1000 characters",
		})
	}
	return nil
}

// Repository interfaces

type PurchaseRepository interface {
	Create(purchase *Purchase) error
	GetByID(id uuid.UUID) (*Purchase, error)
	GetByPantryID(pantryID uuid.UUID, page, limit int) ([]*Purchase, int64, error)
	GetByUserID(userID uuid.UUID, page, limit int) ([]*Purchase, int64, error)
	Update(purchase *Purchase) error
	Delete(id uuid.UUID) error
}

type StoreRepository interface {
	Create(store *Store) error
	GetByID(id uuid.UUID) (*Store, error)
	GetAll(page, limit int) ([]*Store, int64, error)
	Update(store *Store) error
	Delete(id uuid.UUID) error
	SearchByName(name string) ([]*Store, error)
}

// Domain Events

// PurchaseCreatedEvent is raised when a purchase is created
type PurchaseCreatedEvent struct {
	*BaseDomainEvent
}

// NewPurchaseCreatedEvent creates a new purchase created event
func NewPurchaseCreatedEvent(purchaseID, pantryID uuid.UUID, totalAmount float64, currency string, purchasedByUserID uuid.UUID) *PurchaseCreatedEvent {
	eventData := map[string]interface{}{
		"pantry_id":           pantryID,
		"total_amount":        totalAmount,
		"currency":           currency,
		"purchased_by_user_id": purchasedByUserID,
	}

	return &PurchaseCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("purchase.created", purchaseID, eventData),
	}
}

// PurchaseItemAddedEvent is raised when an item is added to a purchase
type PurchaseItemAddedEvent struct {
	*BaseDomainEvent
}

// NewPurchaseItemAddedEvent creates a new purchase item added event
func NewPurchaseItemAddedEvent(purchaseID, itemID, productVariantID uuid.UUID, quantity float64, unitID uuid.UUID, totalPrice float64) *PurchaseItemAddedEvent {
	eventData := map[string]interface{}{
		"item_id":            itemID,
		"product_variant_id": productVariantID,
		"quantity":          quantity,
		"unit_of_measure_id": unitID,
		"total_price":       totalPrice,
	}

	return &PurchaseItemAddedEvent{
		BaseDomainEvent: newBaseDomainEvent("purchase.item_added", purchaseID, eventData),
	}
}

// PurchaseDeletedEvent is raised when a purchase is deleted
type PurchaseDeletedEvent struct {
	*BaseDomainEvent
}

// NewPurchaseDeletedEvent creates a new purchase deleted event
func NewPurchaseDeletedEvent(purchaseID, pantryID uuid.UUID) *PurchaseDeletedEvent {
	eventData := map[string]interface{}{
		"pantry_id": pantryID,
	}

	return &PurchaseDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("purchase.deleted", purchaseID, eventData),
	}
}
