package domain

import (
	"time"

	"github.com/google/uuid"
)

// IdempotencyKey represents an idempotency key for request deduplication
type IdempotencyKey struct {
	Key                 string            `json:"key"`
	RequestHash         string            `json:"request_hash"`
	Method              string            `json:"method"`
	Path                string            `json:"path"`
	UserID              *uuid.UUID        `json:"user_id,omitempty"`
	Status              IdempotencyStatus `json:"status"`
	RequestBody         string            `json:"request_body,omitempty"`
	ResponseCode        int               `json:"response_code,omitempty"`
	ResponseBody        string            `json:"response_body,omitempty"`
	ResponseHeaders     map[string]string `json:"response_headers,omitempty"`
	CreatedAt           time.Time         `json:"created_at"`
	ExpiresAt           time.Time         `json:"expires_at"`
	ProcessingStartedAt *time.Time        `json:"processing_started_at,omitempty"`
	CompletedAt         *time.Time        `json:"completed_at,omitempty"`
}

// IdempotencyStatus represents the status of an idempotent request
type IdempotencyStatus string

const (
	IdempotencyStatusPending   IdempotencyStatus = "pending"   // Request is being processed
	IdempotencyStatusCompleted IdempotencyStatus = "completed" // Request completed successfully
	IdempotencyStatusFailed    IdempotencyStatus = "failed"    // Request failed
	IdempotencyStatusExpired   IdempotencyStatus = "expired"   // Request expired
)

// IdempotencyConfig represents configuration for idempotency middleware
type IdempotencyConfig struct {
	Enabled            bool          `json:"enabled"`
	DefaultTTL         time.Duration `json:"default_ttl"`
	MaxTTL             time.Duration `json:"max_ttl"`
	KeyHeader          string        `json:"key_header"`
	IncludeUserID      bool          `json:"include_user_id"`
	IncludeRequestBody bool          `json:"include_request_body"`
	ExcludedPaths      []string      `json:"excluded_paths"`
	ExcludedMethods    []string      `json:"excluded_methods"`
	MaxRequestSize     int64         `json:"max_request_size"`
	MaxResponseSize    int64         `json:"max_response_size"`
}

// IdempotencyService defines the interface for idempotency management
type IdempotencyService interface {
	// CheckIdempotency checks if a request is idempotent and returns cached response if available
	CheckIdempotency(key string, requestHash string, method string, path string, userID *uuid.UUID) (*IdempotencyKey, error)

	// StartProcessing marks an idempotent request as being processed
	StartProcessing(key string) error

	// CompleteRequest stores the response for an idempotent request
	CompleteRequest(key string, statusCode int, responseBody string, responseHeaders map[string]string) error

	// FailRequest marks an idempotent request as failed
	FailRequest(key string, errorMessage string) error

	// GenerateKey generates an idempotency key from request data
	GenerateKey(method string, path string, userID *uuid.UUID, requestBody string) string

	// CreateRequestHash creates a hash of the request for comparison
	CreateRequestHash(method string, path string, userID *uuid.UUID, requestBody string) string

	// IsIdempotentMethod checks if an HTTP method should be treated as idempotent
	IsIdempotentMethod(method string) bool

	// ShouldSkipPath checks if a path should be excluded from idempotency
	ShouldSkipPath(path string) bool

	// CleanupExpired removes expired idempotency keys
	CleanupExpired() error

	// GetConfig returns the current configuration
	GetConfig() *IdempotencyConfig

	// GetStats returns statistics about idempotency usage
	GetStats() (map[string]interface{}, error)
}

// IdempotencyRepository defines the interface for idempotency persistence
type IdempotencyRepository interface {
	// Store stores an idempotency key
	Store(key *IdempotencyKey) error

	// Get retrieves an idempotency key
	Get(key string) (*IdempotencyKey, error)

	// Update updates an idempotency key
	Update(key *IdempotencyKey) error

	// Delete removes an idempotency key
	Delete(key string) error

	// SetProcessing atomically sets a key to processing status if it doesn't exist
	SetProcessing(key string, requestHash string, method string, path string, userID *uuid.UUID, ttl time.Duration) (*IdempotencyKey, bool, error)

	// GetExpiredKeys returns keys that have expired
	GetExpiredKeys(limit int) ([]string, error)

	// DeleteBatch deletes multiple keys in a batch
	DeleteBatch(keys []string) error
}

// NewIdempotencyKey creates a new idempotency key
func NewIdempotencyKey(key string, requestHash string, method string, path string, userID *uuid.UUID, ttl time.Duration) *IdempotencyKey {
	now := time.Now()
	return &IdempotencyKey{
		Key:         key,
		RequestHash: requestHash,
		Method:      method,
		Path:        path,
		UserID:      userID,
		Status:      IdempotencyStatusPending,
		CreatedAt:   now,
		ExpiresAt:   now.Add(ttl),
	}
}

// IsExpired checks if the idempotency key has expired
func (ik *IdempotencyKey) IsExpired() bool {
	return time.Now().After(ik.ExpiresAt)
}

// IsCompleted checks if the request has been completed
func (ik *IdempotencyKey) IsCompleted() bool {
	return ik.Status == IdempotencyStatusCompleted
}

// IsFailed checks if the request has failed
func (ik *IdempotencyKey) IsFailed() bool {
	return ik.Status == IdempotencyStatusFailed
}

// IsPending checks if the request is still pending
func (ik *IdempotencyKey) IsPending() bool {
	return ik.Status == IdempotencyStatusPending
}

// MarkProcessingStarted marks the request as processing started
func (ik *IdempotencyKey) MarkProcessingStarted() {
	now := time.Now()
	ik.ProcessingStartedAt = &now
}

// MarkCompleted marks the request as completed
func (ik *IdempotencyKey) MarkCompleted(statusCode int, responseBody string, responseHeaders map[string]string) {
	now := time.Now()
	ik.Status = IdempotencyStatusCompleted
	ik.ResponseCode = statusCode
	ik.ResponseBody = responseBody
	ik.ResponseHeaders = responseHeaders
	ik.CompletedAt = &now
}

// MarkFailed marks the request as failed
func (ik *IdempotencyKey) MarkFailed() {
	now := time.Now()
	ik.Status = IdempotencyStatusFailed
	ik.CompletedAt = &now
}

// GetProcessingDuration returns how long the request has been processing
func (ik *IdempotencyKey) GetProcessingDuration() time.Duration {
	if ik.ProcessingStartedAt == nil {
		return 0
	}

	endTime := time.Now()
	if ik.CompletedAt != nil {
		endTime = *ik.CompletedAt
	}

	return endTime.Sub(*ik.ProcessingStartedAt)
}

// DefaultIdempotencyConfig returns default configuration for idempotency
func DefaultIdempotencyConfig() *IdempotencyConfig {
	return &IdempotencyConfig{
		Enabled:            true,
		DefaultTTL:         24 * time.Hour,
		MaxTTL:             7 * 24 * time.Hour,
		KeyHeader:          "Idempotency-Key",
		IncludeUserID:      true,
		IncludeRequestBody: true,
		ExcludedPaths: []string{
			"/health",
			"/metrics",
			"/ping",
		},
		ExcludedMethods: []string{
			"GET",
			"HEAD",
			"OPTIONS",
		},
		MaxRequestSize:  1024 * 1024, // 1MB
		MaxResponseSize: 1024 * 1024, // 1MB
	}
}

// ShouldProcessRequest checks if a request should be processed for idempotency
func (config *IdempotencyConfig) ShouldProcessRequest(method string, path string, requestSize int64) bool {
	if !config.Enabled {
		return false
	}

	// Check if method is excluded
	for _, excludedMethod := range config.ExcludedMethods {
		if method == excludedMethod {
			return false
		}
	}

	// Check if path is excluded
	for _, excludedPath := range config.ExcludedPaths {
		if path == excludedPath {
			return false
		}
	}

	// Check request size
	if requestSize > config.MaxRequestSize {
		return false
	}

	return true
}
