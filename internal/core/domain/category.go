package domain

import (
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// Category represents a product category in the system
type Category struct {
	ID               uuid.UUID  `json:"id"`
	Name             string     `json:"name"`
	Description      *string    `json:"description,omitempty"`
	ParentCategoryID *uuid.UUID `json:"parent_category_id,omitempty"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	DeletedAt        *time.Time `json:"deleted_at,omitempty"`

	// Aggregate root fields
	events []DomainEvent
}

// NewCategory creates a new category
func NewCategory(name string, description *string, parentCategoryID *uuid.UUID) *Category {
	now := time.Now()
	category := &Category{
		ID:               uuid.New(),
		Name:             name,
		Description:      description,
		ParentCategoryID: parentCategoryID,
		CreatedAt:        now,
		UpdatedAt:        now,
		events:           make([]DomainEvent, 0),
	}

	// Add domain event
	category.AddEvent(NewCategoryCreatedEvent(category.ID, category.Name, category.ParentCategoryID))

	return category
}

// UpdateDetails updates category details
func (c *Category) UpdateDetails(name string, description *string) {
	c.Name = name
	c.Description = description
	c.UpdatedAt = time.Now()

	// Add domain event
	c.AddEvent(NewCategoryUpdatedEvent(c.ID, c.Name, c.Description))
}

// Move moves the category to a different parent
func (c *Category) Move(newParentCategoryID *uuid.UUID) {
	oldParentID := c.ParentCategoryID
	c.ParentCategoryID = newParentCategoryID
	c.UpdatedAt = time.Now()

	// Add domain event
	c.AddEvent(NewCategoryMovedEvent(c.ID, oldParentID, newParentCategoryID))
}

// SoftDelete marks the category as deleted
func (c *Category) SoftDelete() {
	now := time.Now()
	c.DeletedAt = &now
	c.UpdatedAt = now

	// Add domain event
	c.AddEvent(NewCategoryDeletedEvent(c.ID, c.Name))
}

// IsDeleted checks if the category is soft deleted
func (c *Category) IsDeleted() bool {
	return c.DeletedAt != nil
}

// IsTopLevel checks if this is a top-level category
func (c *Category) IsTopLevel() bool {
	return c.ParentCategoryID == nil
}

// GetFullPath returns the full category path (for display purposes)
// This would typically be computed by the application layer with parent data
func (c *Category) GetFullPath(parents []string) string {
	if len(parents) == 0 {
		return c.Name
	}

	fullPath := ""
	for _, parent := range parents {
		fullPath += parent + " > "
	}
	fullPath += c.Name

	return fullPath
}

// Domain Events implementation
func (c *Category) AddEvent(event DomainEvent) {
	c.events = append(c.events, event)
}

func (c *Category) GetEvents() []DomainEvent {
	return c.events
}

func (c *Category) ClearEvents() {
	c.events = make([]DomainEvent, 0)
}

// CategoryRepository defines the interface for category persistence
type CategoryRepository interface {
	Create(category *Category) error
	GetByID(id uuid.UUID) (*Category, error)
	GetByName(name string) (*Category, error)
	GetTopLevelCategories() ([]*Category, error)
	GetSubCategories(parentID uuid.UUID) ([]*Category, error)
	GetAllCategories() ([]*Category, error)
	Update(category *Category) error
	Delete(id uuid.UUID) error
	ExistsByName(name string, parentID *uuid.UUID) (bool, error)
	GetCategoryPath(categoryID uuid.UUID) ([]*Category, error) // Returns path from root to category
}

// Domain Events

// CategoryCreatedEvent is raised when a category is created
type CategoryCreatedEvent struct {
	*BaseDomainEvent
}

// NewCategoryCreatedEvent creates a new category created event
func NewCategoryCreatedEvent(categoryID uuid.UUID, name string, parentCategoryID *uuid.UUID) *CategoryCreatedEvent {
	eventData := map[string]interface{}{
		"name": name,
	}

	if parentCategoryID != nil {
		eventData["parent_category_id"] = *parentCategoryID
	}

	return &CategoryCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("category.created", categoryID, eventData),
	}
}

// CategoryUpdatedEvent is raised when a category is updated
type CategoryUpdatedEvent struct {
	*BaseDomainEvent
}

// NewCategoryUpdatedEvent creates a new category updated event
func NewCategoryUpdatedEvent(categoryID uuid.UUID, name string, description *string) *CategoryUpdatedEvent {
	eventData := map[string]interface{}{
		"name": name,
	}

	if description != nil {
		eventData["description"] = *description
	}

	return &CategoryUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("category.updated", categoryID, eventData),
	}
}

// CategoryMovedEvent is raised when a category is moved to a different parent
type CategoryMovedEvent struct {
	*BaseDomainEvent
}

// NewCategoryMovedEvent creates a new category moved event
func NewCategoryMovedEvent(categoryID uuid.UUID, oldParentID, newParentID *uuid.UUID) *CategoryMovedEvent {
	eventData := map[string]interface{}{}

	if oldParentID != nil {
		eventData["old_parent_id"] = *oldParentID
	}

	if newParentID != nil {
		eventData["new_parent_id"] = *newParentID
	}

	return &CategoryMovedEvent{
		BaseDomainEvent: newBaseDomainEvent("category.moved", categoryID, eventData),
	}
}

// CategoryDeletedEvent is raised when a category is deleted
type CategoryDeletedEvent struct {
	*BaseDomainEvent
}

// NewCategoryDeletedEvent creates a new category deleted event
func NewCategoryDeletedEvent(categoryID uuid.UUID, name string) *CategoryDeletedEvent {
	eventData := map[string]interface{}{
		"name": name,
	}

	return &CategoryDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("category.deleted", categoryID, eventData),
	}
}

// Validation functions

// ValidateCategoryName validates a category name
func ValidateCategoryName(name string) error {
	if len(strings.TrimSpace(name)) == 0 {
		return errors.NewValidationError("Category name cannot be empty", map[string]string{
			"name": "Category name is required",
		})
	}

	if len(name) > 100 {
		return errors.NewValidationError("Category name too long", map[string]string{
			"name": "Category name must be 100 characters or less",
		})
	}

	return nil
}

// ValidateCategoryHierarchy validates that moving a category won't create a cycle
func ValidateCategoryHierarchy(categoryID uuid.UUID, newParentID *uuid.UUID, repo CategoryRepository) error {
	if newParentID == nil {
		return nil // Moving to root is always valid
	}

	if categoryID == *newParentID {
		return errors.NewValidationError("Invalid hierarchy", map[string]string{
			"parent_category_id": "Category cannot be its own parent",
		})
	}

	// Check if the new parent is a descendant of the category being moved
	currentID := newParentID
	for currentID != nil {
		if *currentID == categoryID {
			return errors.NewValidationError("Invalid hierarchy", map[string]string{
				"parent_category_id": "Cannot move category to one of its descendants",
			})
		}

		// Get parent of current category
		category, err := repo.GetByID(*currentID)
		if err != nil {
			return err
		}

		currentID = category.ParentCategoryID
	}

	return nil
}
