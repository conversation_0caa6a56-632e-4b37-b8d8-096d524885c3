package domain

import (
	"testing"
)

func TestGetRolePermissions(t *testing.T) {
	// Test Owner permissions
	ownerPerms := GetRolePermissions(PantryRoleOwner)
	if len(ownerPerms) == 0 {
		t.Error("Owner should have permissions")
	}
	
	// Owner should have all permissions
	expectedOwnerPerms := []PantryPermission{
		PermissionViewPantry,
		PermissionEditPantry,
		PermissionDeletePantry,
		PermissionTransferOwnership,
		PermissionInviteMembers,
		PermissionRemoveMembers,
		PermissionUpdateMemberRoles,
		PermissionViewMembers,
		PermissionCreateLocations,
		PermissionEditLocations,
		PermissionDeleteLocations,
		PermissionViewLocations,
	}
	
	for _, perm := range expectedOwnerPerms {
		if !HasPermission(PantryRoleOwner, perm) {
			t.Errorf("Owner should have permission %s", perm)
		}
	}
	
	// Test Admin permissions
	adminPerms := GetRolePermissions(PantryRoleAdmin)
	if len(adminPerms) == 0 {
		t.Error("Admin should have permissions")
	}
	
	// Admin should have most permissions except ownership transfer and pantry deletion
	if HasPermission(PantryRoleAdmin, PermissionTransferOwnership) {
		t.Error("Admin should not have transfer ownership permission")
	}
	
	if HasPermission(PantryRoleAdmin, PermissionDeletePantry) {
		t.Error("Admin should not have delete pantry permission")
	}
	
	if !HasPermission(PantryRoleAdmin, PermissionInviteMembers) {
		t.Error("Admin should have invite members permission")
	}
	
	// Test Editor permissions
	editorPerms := GetRolePermissions(PantryRoleEditor)
	if len(editorPerms) == 0 {
		t.Error("Editor should have permissions")
	}
	
	// Editor should not have member management permissions
	if HasPermission(PantryRoleEditor, PermissionInviteMembers) {
		t.Error("Editor should not have invite members permission")
	}
	
	if HasPermission(PantryRoleEditor, PermissionRemoveMembers) {
		t.Error("Editor should not have remove members permission")
	}
	
	if !HasPermission(PantryRoleEditor, PermissionCreateLocations) {
		t.Error("Editor should have create locations permission")
	}
	
	// Test Viewer permissions
	viewerPerms := GetRolePermissions(PantryRoleViewer)
	if len(viewerPerms) == 0 {
		t.Error("Viewer should have permissions")
	}
	
	// Viewer should only have read permissions
	if HasPermission(PantryRoleViewer, PermissionCreateLocations) {
		t.Error("Viewer should not have create locations permission")
	}
	
	if HasPermission(PantryRoleViewer, PermissionEditLocations) {
		t.Error("Viewer should not have edit locations permission")
	}
	
	if !HasPermission(PantryRoleViewer, PermissionViewLocations) {
		t.Error("Viewer should have view locations permission")
	}
	
	if !HasPermission(PantryRoleViewer, PermissionViewPantry) {
		t.Error("Viewer should have view pantry permission")
	}
}

func TestCanManageRole(t *testing.T) {
	// Owner can manage all roles
	if !CanManageRole(PantryRoleOwner, PantryRoleAdmin) {
		t.Error("Owner should be able to manage Admin")
	}
	
	if !CanManageRole(PantryRoleOwner, PantryRoleEditor) {
		t.Error("Owner should be able to manage Editor")
	}
	
	if !CanManageRole(PantryRoleOwner, PantryRoleViewer) {
		t.Error("Owner should be able to manage Viewer")
	}
	
	// Admin can manage Editor and Viewer
	if !CanManageRole(PantryRoleAdmin, PantryRoleEditor) {
		t.Error("Admin should be able to manage Editor")
	}
	
	if !CanManageRole(PantryRoleAdmin, PantryRoleViewer) {
		t.Error("Admin should be able to manage Viewer")
	}
	
	if CanManageRole(PantryRoleAdmin, PantryRoleOwner) {
		t.Error("Admin should not be able to manage Owner")
	}
	
	if CanManageRole(PantryRoleAdmin, PantryRoleAdmin) {
		t.Error("Admin should not be able to manage other Admins")
	}
	
	// Editor cannot manage any roles
	if CanManageRole(PantryRoleEditor, PantryRoleViewer) {
		t.Error("Editor should not be able to manage Viewer")
	}
	
	if CanManageRole(PantryRoleEditor, PantryRoleEditor) {
		t.Error("Editor should not be able to manage other Editors")
	}
	
	// Viewer cannot manage any roles
	if CanManageRole(PantryRoleViewer, PantryRoleViewer) {
		t.Error("Viewer should not be able to manage other Viewers")
	}
}

func TestIsValidRoleTransition(t *testing.T) {
	// Cannot transition to Owner role
	if IsValidRoleTransition(PantryRoleAdmin, PantryRoleOwner) {
		t.Error("Should not be able to transition to Owner role")
	}
	
	if IsValidRoleTransition(PantryRoleEditor, PantryRoleOwner) {
		t.Error("Should not be able to transition to Owner role")
	}
	
	if IsValidRoleTransition(PantryRoleViewer, PantryRoleOwner) {
		t.Error("Should not be able to transition to Owner role")
	}
	
	// All other transitions should be valid
	if !IsValidRoleTransition(PantryRoleAdmin, PantryRoleEditor) {
		t.Error("Should be able to transition from Admin to Editor")
	}
	
	if !IsValidRoleTransition(PantryRoleEditor, PantryRoleAdmin) {
		t.Error("Should be able to transition from Editor to Admin")
	}
	
	if !IsValidRoleTransition(PantryRoleViewer, PantryRoleEditor) {
		t.Error("Should be able to transition from Viewer to Editor")
	}
}

func TestGetRoleHierarchyLevel(t *testing.T) {
	ownerLevel := GetRoleHierarchyLevel(PantryRoleOwner)
	adminLevel := GetRoleHierarchyLevel(PantryRoleAdmin)
	editorLevel := GetRoleHierarchyLevel(PantryRoleEditor)
	viewerLevel := GetRoleHierarchyLevel(PantryRoleViewer)
	
	// Check hierarchy order
	if ownerLevel <= adminLevel {
		t.Error("Owner should have higher hierarchy level than Admin")
	}
	
	if adminLevel <= editorLevel {
		t.Error("Admin should have higher hierarchy level than Editor")
	}
	
	if editorLevel <= viewerLevel {
		t.Error("Editor should have higher hierarchy level than Viewer")
	}
	
	// Check specific values
	if ownerLevel != 4 {
		t.Errorf("Expected Owner level 4, got %d", ownerLevel)
	}
	
	if adminLevel != 3 {
		t.Errorf("Expected Admin level 3, got %d", adminLevel)
	}
	
	if editorLevel != 2 {
		t.Errorf("Expected Editor level 2, got %d", editorLevel)
	}
	
	if viewerLevel != 1 {
		t.Errorf("Expected Viewer level 1, got %d", viewerLevel)
	}
	
	// Test invalid role
	invalidLevel := GetRoleHierarchyLevel(PantryRole("invalid"))
	if invalidLevel != 0 {
		t.Errorf("Expected invalid role level 0, got %d", invalidLevel)
	}
}

func TestHasPermission(t *testing.T) {
	// Test specific permission checks
	testCases := []struct {
		role       PantryRole
		permission PantryPermission
		expected   bool
	}{
		{PantryRoleOwner, PermissionDeletePantry, true},
		{PantryRoleAdmin, PermissionDeletePantry, false},
		{PantryRoleEditor, PermissionDeletePantry, false},
		{PantryRoleViewer, PermissionDeletePantry, false},
		
		{PantryRoleOwner, PermissionInviteMembers, true},
		{PantryRoleAdmin, PermissionInviteMembers, true},
		{PantryRoleEditor, PermissionInviteMembers, false},
		{PantryRoleViewer, PermissionInviteMembers, false},
		
		{PantryRoleOwner, PermissionCreateLocations, true},
		{PantryRoleAdmin, PermissionCreateLocations, true},
		{PantryRoleEditor, PermissionCreateLocations, true},
		{PantryRoleViewer, PermissionCreateLocations, false},
		
		{PantryRoleOwner, PermissionViewPantry, true},
		{PantryRoleAdmin, PermissionViewPantry, true},
		{PantryRoleEditor, PermissionViewPantry, true},
		{PantryRoleViewer, PermissionViewPantry, true},
	}
	
	for _, tc := range testCases {
		result := HasPermission(tc.role, tc.permission)
		if result != tc.expected {
			t.Errorf("HasPermission(%s, %s) = %v, expected %v", 
				tc.role, tc.permission, result, tc.expected)
		}
	}
}
