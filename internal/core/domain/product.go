package domain

import (
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// ProductVariantPackagingType represents the packaging type of a product variant
type ProductVariantPackagingType string

const (
	PackagingTypeSingle    ProductVariantPackagingType = "single"
	PackagingTypeBulk      ProductVariantPackagingType = "bulk"
	PackagingTypeMultiPack ProductVariantPackagingType = "multi-pack"
	PackagingTypeOther     ProductVariantPackagingType = "other"
)

// Product represents a generic product in the catalog
type Product struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty"`
	CategoryID  uuid.UUID  `json:"category_id"`
	Brand       *string    `json:"brand,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty"`

	// Aggregate root fields
	events []DomainEvent
}

// ProductVariant represents a specific variant of a product
type ProductVariant struct {
	ID                     uuid.UUID                   `json:"id"`
	ProductID              uuid.UUID                   `json:"product_id"`
	Name                   string                      `json:"name"`
	Description            *string                     `json:"description,omitempty"`
	BarcodeGTIN            *string                     `json:"barcode_gtin,omitempty"`
	ImageURL               *string                     `json:"image_url,omitempty"`
	PackagingType          ProductVariantPackagingType `json:"packaging_type"`
	DefaultUnitOfMeasureID *uuid.UUID                  `json:"default_unit_of_measure_id,omitempty"`
	ShelfLifeDays          *int                        `json:"shelf_life_days,omitempty"`
	CreatedAt              time.Time                   `json:"created_at"`
	UpdatedAt              time.Time                   `json:"updated_at"`
	DeletedAt              *time.Time                  `json:"deleted_at,omitempty"`

	// Aggregate root fields
	events []DomainEvent
}

// NewProduct creates a new product
func NewProduct(name string, description *string, categoryID uuid.UUID, brand *string) *Product {
	now := time.Now()
	product := &Product{
		ID:          uuid.New(),
		Name:        name,
		Description: description,
		CategoryID:  categoryID,
		Brand:       brand,
		CreatedAt:   now,
		UpdatedAt:   now,
		events:      make([]DomainEvent, 0),
	}

	// Add domain event
	product.AddEvent(NewProductCreatedEvent(product.ID, product.Name, product.CategoryID, product.Brand))

	return product
}

// UpdateDetails updates product details
func (p *Product) UpdateDetails(name string, description *string, categoryID uuid.UUID, brand *string) {
	p.Name = name
	p.Description = description
	p.CategoryID = categoryID
	p.Brand = brand
	p.UpdatedAt = time.Now()

	// Add domain event
	p.AddEvent(NewProductUpdatedEvent(p.ID, p.Name, p.CategoryID, p.Brand))
}

// SoftDelete marks the product as deleted
func (p *Product) SoftDelete() {
	now := time.Now()
	p.DeletedAt = &now
	p.UpdatedAt = now

	// Add domain event
	p.AddEvent(NewProductDeletedEvent(p.ID, p.Name))
}

// IsDeleted checks if the product is soft deleted
func (p *Product) IsDeleted() bool {
	return p.DeletedAt != nil
}

// GetDisplayName returns the display name including brand if available
func (p *Product) GetDisplayName() string {
	if p.Brand != nil && *p.Brand != "" {
		return *p.Brand + " " + p.Name
	}
	return p.Name
}

// Domain Events implementation for Product
func (p *Product) AddEvent(event DomainEvent) {
	p.events = append(p.events, event)
}

func (p *Product) GetEvents() []DomainEvent {
	return p.events
}

func (p *Product) ClearEvents() {
	p.events = make([]DomainEvent, 0)
}

// NewProductVariant creates a new product variant
func NewProductVariant(productID uuid.UUID, name string, description *string, barcodeGTIN *string, imageURL *string, packagingType ProductVariantPackagingType, defaultUnitID *uuid.UUID, shelfLifeDays *int) *ProductVariant {
	// Validate shelf life if provided
	if shelfLifeDays != nil && *shelfLifeDays <= 0 {
		panic("shelf life days must be positive")
	}

	now := time.Now()
	variant := &ProductVariant{
		ID:                     uuid.New(),
		ProductID:              productID,
		Name:                   name,
		Description:            description,
		BarcodeGTIN:            barcodeGTIN,
		ImageURL:               imageURL,
		PackagingType:          packagingType,
		DefaultUnitOfMeasureID: defaultUnitID,
		ShelfLifeDays:          shelfLifeDays,
		CreatedAt:              now,
		UpdatedAt:              now,
		events:                 make([]DomainEvent, 0),
	}

	// Add domain event
	variant.AddEvent(NewProductVariantCreatedEvent(variant.ID, variant.ProductID, variant.Name, variant.BarcodeGTIN))

	return variant
}

// UpdateDetails updates product variant details
func (pv *ProductVariant) UpdateDetails(name string, description *string, imageURL *string, packagingType ProductVariantPackagingType, defaultUnitID *uuid.UUID) {
	pv.Name = name
	pv.Description = description
	pv.ImageURL = imageURL
	pv.PackagingType = packagingType
	pv.DefaultUnitOfMeasureID = defaultUnitID
	pv.UpdatedAt = time.Now()

	// Add domain event
	pv.AddEvent(NewProductVariantUpdatedEvent(pv.ID, pv.Name))
}

// UpdateBarcode updates the barcode (with validation)
func (pv *ProductVariant) UpdateBarcode(barcodeGTIN *string) error {
	// Validate barcode format if provided
	if barcodeGTIN != nil && *barcodeGTIN != "" {
		if err := ValidateBarcodeGTIN(*barcodeGTIN); err != nil {
			return err
		}
	}

	pv.BarcodeGTIN = barcodeGTIN
	pv.UpdatedAt = time.Now()

	// Add domain event
	pv.AddEvent(NewProductVariantBarcodeUpdatedEvent(pv.ID, pv.BarcodeGTIN))

	return nil
}

// SoftDelete marks the product variant as deleted
func (pv *ProductVariant) SoftDelete() {
	now := time.Now()
	pv.DeletedAt = &now
	pv.UpdatedAt = now

	// Add domain event
	pv.AddEvent(NewProductVariantDeletedEvent(pv.ID, pv.Name))
}

// IsDeleted checks if the product variant is soft deleted
func (pv *ProductVariant) IsDeleted() bool {
	return pv.DeletedAt != nil
}

// HasBarcode checks if the variant has a barcode
func (pv *ProductVariant) HasBarcode() bool {
	return pv.BarcodeGTIN != nil && *pv.BarcodeGTIN != ""
}

// Domain Events implementation for ProductVariant
func (pv *ProductVariant) AddEvent(event DomainEvent) {
	pv.events = append(pv.events, event)
}

func (pv *ProductVariant) GetEvents() []DomainEvent {
	return pv.events
}

func (pv *ProductVariant) ClearEvents() {
	pv.events = make([]DomainEvent, 0)
}

// Repository interfaces

// ProductRepository defines the interface for product persistence
type ProductRepository interface {
	Create(product *Product) error
	GetByID(id uuid.UUID) (*Product, error)
	GetByNameAndBrandAndCategory(name string, brand *string, categoryID uuid.UUID) (*Product, error)
	GetByCategory(categoryID uuid.UUID, page, limit int) ([]*Product, int64, error)
	SearchProducts(query string, categoryID *uuid.UUID, page, limit int) ([]*Product, int64, error)
	Update(product *Product) error
	Delete(id uuid.UUID) error
	ExistsByNameAndBrandAndCategory(name string, brand *string, categoryID uuid.UUID) (bool, error)
}

// ProductVariantRepository defines the interface for product variant persistence
type ProductVariantRepository interface {
	Create(variant *ProductVariant) error
	GetByID(id uuid.UUID) (*ProductVariant, error)
	GetByProductID(productID uuid.UUID) ([]*ProductVariant, error)
	GetByBarcode(barcodeGTIN string) (*ProductVariant, error)
	SearchVariants(query string, productID *uuid.UUID, page, limit int) ([]*ProductVariant, int64, error)
	Update(variant *ProductVariant) error
	Delete(id uuid.UUID) error
	ExistsByProductAndName(productID uuid.UUID, name string) (bool, error)
	ExistsByBarcode(barcodeGTIN string) (bool, error)
}

// Validation functions

// ValidateBarcodeGTIN validates a GTIN barcode
func ValidateBarcodeGTIN(barcode string) error {
	// Basic validation - GTIN can be 8, 12, 13, or 14 digits
	if len(barcode) < 8 || len(barcode) > 14 {
		return errors.NewValidationError("Invalid barcode length", map[string]string{
			"barcode_gtin": "Barcode must be 8-14 digits long",
		})
	}

	// Check if all characters are digits
	for _, char := range barcode {
		if char < '0' || char > '9' {
			return errors.NewValidationError("Invalid barcode format", map[string]string{
				"barcode_gtin": "Barcode must contain only digits",
			})
		}
	}

	return nil
}

// ValidateProductName validates product name
func ValidateProductName(name string) error {
	if len(name) == 0 {
		return errors.NewValidationError("Product name is required", map[string]string{
			"name": "Product name cannot be empty",
		})
	}

	if len(name) > 255 {
		return errors.NewValidationError("Product name too long", map[string]string{
			"name": "Product name cannot exceed 255 characters",
		})
	}

	return nil
}

// ValidateProductVariantName validates product variant name
func ValidateProductVariantName(name string) error {
	if len(name) == 0 {
		return errors.NewValidationError("Product variant name is required", map[string]string{
			"name": "Product variant name cannot be empty",
		})
	}

	if len(name) > 255 {
		return errors.NewValidationError("Product variant name too long", map[string]string{
			"name": "Product variant name cannot exceed 255 characters",
		})
	}

	return nil
}

// Domain Events

// ProductCreatedEvent is raised when a product is created
type ProductCreatedEvent struct {
	*BaseDomainEvent
}

// NewProductCreatedEvent creates a new product created event
func NewProductCreatedEvent(productID uuid.UUID, name string, categoryID uuid.UUID, brand *string) *ProductCreatedEvent {
	eventData := map[string]interface{}{
		"name":        name,
		"category_id": categoryID,
	}

	if brand != nil {
		eventData["brand"] = *brand
	}

	return &ProductCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("product.created", productID, eventData),
	}
}

// ProductUpdatedEvent is raised when a product is updated
type ProductUpdatedEvent struct {
	*BaseDomainEvent
}

// NewProductUpdatedEvent creates a new product updated event
func NewProductUpdatedEvent(productID uuid.UUID, name string, categoryID uuid.UUID, brand *string) *ProductUpdatedEvent {
	eventData := map[string]interface{}{
		"name":        name,
		"category_id": categoryID,
	}

	if brand != nil {
		eventData["brand"] = *brand
	}

	return &ProductUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("product.updated", productID, eventData),
	}
}

// ProductDeletedEvent is raised when a product is deleted
type ProductDeletedEvent struct {
	*BaseDomainEvent
}

// NewProductDeletedEvent creates a new product deleted event
func NewProductDeletedEvent(productID uuid.UUID, name string) *ProductDeletedEvent {
	eventData := map[string]interface{}{
		"name": name,
	}

	return &ProductDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("product.deleted", productID, eventData),
	}
}

// ProductVariantCreatedEvent is raised when a product variant is created
type ProductVariantCreatedEvent struct {
	*BaseDomainEvent
}

// NewProductVariantCreatedEvent creates a new product variant created event
func NewProductVariantCreatedEvent(variantID, productID uuid.UUID, name string, barcodeGTIN *string) *ProductVariantCreatedEvent {
	eventData := map[string]interface{}{
		"product_id": productID,
		"name":       name,
	}

	if barcodeGTIN != nil {
		eventData["barcode_gtin"] = *barcodeGTIN
	}

	return &ProductVariantCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("product_variant.created", variantID, eventData),
	}
}

// ProductVariantUpdatedEvent is raised when a product variant is updated
type ProductVariantUpdatedEvent struct {
	*BaseDomainEvent
}

// NewProductVariantUpdatedEvent creates a new product variant updated event
func NewProductVariantUpdatedEvent(variantID uuid.UUID, name string) *ProductVariantUpdatedEvent {
	eventData := map[string]interface{}{
		"name": name,
	}

	return &ProductVariantUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("product_variant.updated", variantID, eventData),
	}
}

// ProductVariantBarcodeUpdatedEvent is raised when a product variant barcode is updated
type ProductVariantBarcodeUpdatedEvent struct {
	*BaseDomainEvent
}

// NewProductVariantBarcodeUpdatedEvent creates a new product variant barcode updated event
func NewProductVariantBarcodeUpdatedEvent(variantID uuid.UUID, barcodeGTIN *string) *ProductVariantBarcodeUpdatedEvent {
	eventData := map[string]interface{}{}

	if barcodeGTIN != nil {
		eventData["barcode_gtin"] = *barcodeGTIN
	}

	return &ProductVariantBarcodeUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("product_variant.barcode_updated", variantID, eventData),
	}
}

// ProductVariantDeletedEvent is raised when a product variant is deleted
type ProductVariantDeletedEvent struct {
	*BaseDomainEvent
}

// NewProductVariantDeletedEvent creates a new product variant deleted event
func NewProductVariantDeletedEvent(variantID uuid.UUID, name string) *ProductVariantDeletedEvent {
	eventData := map[string]interface{}{
		"name": name,
	}

	return &ProductVariantDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("product_variant.deleted", variantID, eventData),
	}
}
