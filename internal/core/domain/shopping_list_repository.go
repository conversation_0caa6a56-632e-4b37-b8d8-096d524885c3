package domain

import (
	"context"
	"fmt"

	"github.com/google/uuid"
)

// ShoppingListRepository defines the interface for shopping list persistence
type ShoppingListRepository interface {
	// Shopping List operations
	Create(ctx context.Context, shoppingList *ShoppingList) error
	GetByID(ctx context.Context, id uuid.UUID) (*ShoppingList, error)
	GetByPantryID(ctx context.Context, pantryID uuid.UUID, filters ShoppingListFilters) ([]*ShoppingList, error)
	Update(ctx context.Context, shoppingList *ShoppingList) error
	Delete(ctx context.Context, id uuid.UUID) error

	// Shopping List Item operations
	AddItem(ctx context.Context, item *ShoppingListItemEntity) error
	UpdateItem(ctx context.Context, item *ShoppingListItemEntity) error
	RemoveItem(ctx context.Context, itemID uuid.UUID) error
	GetItemByID(ctx context.Context, itemID uuid.UUID) (*ShoppingListItemEntity, error)
	GetItemsByShoppingListID(ctx context.Context, shoppingListID uuid.UUID) ([]*ShoppingListItemEntity, error)

	// Bulk operations
	MarkItemsAsPurchased(ctx context.Context, itemIDs []uuid.UUID) error
	MarkItemsAsNotPurchased(ctx context.Context, itemIDs []uuid.UUID) error

	// Statistics and analytics
	GetShoppingListStats(ctx context.Context, shoppingListID uuid.UUID) (*ShoppingListStats, error)
	GetPantryShoppingListStats(ctx context.Context, pantryID uuid.UUID) (*PantryShoppingListStats, error)
}

// ShoppingListFilters represents filters for shopping list queries
type ShoppingListFilters struct {
	Status    *ShoppingListStatus `json:"status,omitempty"`
	CreatedBy *uuid.UUID          `json:"created_by,omitempty"`
	Limit     *int                `json:"limit,omitempty"`
	Offset    *int                `json:"offset,omitempty"`
	SortBy    *string             `json:"sort_by,omitempty"`    // "created_at", "updated_at", "name"
	SortOrder *string             `json:"sort_order,omitempty"` // "asc", "desc"
}

// ShoppingListStats represents statistics for a shopping list
type ShoppingListStats struct {
	ShoppingListID       uuid.UUID `json:"shopping_list_id"`
	TotalItems           int       `json:"total_items"`
	PurchasedItems       int       `json:"purchased_items"`
	RemainingItems       int       `json:"remaining_items"`
	CompletionPercentage float64   `json:"completion_percentage"`
}

// PantryShoppingListStats represents statistics for all shopping lists in a pantry
type PantryShoppingListStats struct {
	PantryID               uuid.UUID `json:"pantry_id"`
	TotalShoppingLists     int       `json:"total_shopping_lists"`
	ActiveShoppingLists    int       `json:"active_shopping_lists"`
	CompletedShoppingLists int       `json:"completed_shopping_lists"`
	ArchivedShoppingLists  int       `json:"archived_shopping_lists"`
	TotalItems             int       `json:"total_items"`
	TotalPurchasedItems    int       `json:"total_purchased_items"`
	OverallCompletionRate  float64   `json:"overall_completion_rate"`
}

// ShoppingListService defines the interface for shopping list business logic
type ShoppingListService interface {
	// Shopping List operations
	CreateShoppingList(ctx context.Context, req CreateShoppingListRequest) (*ShoppingList, error)
	GetShoppingList(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*ShoppingList, error)
	GetShoppingListsByPantry(ctx context.Context, pantryID uuid.UUID, userID uuid.UUID, filters ShoppingListFilters) ([]*ShoppingList, error)
	UpdateShoppingList(ctx context.Context, req UpdateShoppingListRequest) (*ShoppingList, error)
	DeleteShoppingList(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	ChangeShoppingListStatus(ctx context.Context, req ChangeShoppingListStatusRequest) (*ShoppingList, error)

	// Shopping List Item operations
	AddItemToShoppingList(ctx context.Context, req AddShoppingListItemRequest) (*ShoppingListItemEntity, error)
	UpdateShoppingListItem(ctx context.Context, req UpdateShoppingListItemRequest) (*ShoppingListItemEntity, error)
	RemoveItemFromShoppingList(ctx context.Context, req RemoveShoppingListItemRequest) error
	MarkItemAsPurchased(ctx context.Context, req MarkItemPurchasedRequest) (*ShoppingListItemEntity, error)
	MarkItemAsNotPurchased(ctx context.Context, req MarkItemNotPurchasedRequest) (*ShoppingListItemEntity, error)

	// Bulk operations
	MarkMultipleItemsAsPurchased(ctx context.Context, req BulkMarkItemsPurchasedRequest) error
	MarkMultipleItemsAsNotPurchased(ctx context.Context, req BulkMarkItemsNotPurchasedRequest) error

	// Statistics
	GetShoppingListStatistics(ctx context.Context, shoppingListID uuid.UUID, userID uuid.UUID) (*ShoppingListStats, error)
	GetPantryShoppingListStatistics(ctx context.Context, pantryID uuid.UUID, userID uuid.UUID) (*PantryShoppingListStats, error)
}

// Request/Response types for shopping list operations

// CreateShoppingListRequest represents a request to create a shopping list
type CreateShoppingListRequest struct {
	PantryID    uuid.UUID `json:"pantry_id" validate:"required"`
	Name        string    `json:"name" validate:"required,min=1,max=255"`
	Description *string   `json:"description,omitempty" validate:"omitempty,max=1000"`
	UserID      uuid.UUID `json:"-"` // Set from authentication context
}

// UpdateShoppingListRequest represents a request to update a shopping list
type UpdateShoppingListRequest struct {
	ID          uuid.UUID `json:"-"` // Set from URL parameter
	Name        string    `json:"name" validate:"required,min=1,max=255"`
	Description *string   `json:"description,omitempty" validate:"omitempty,max=1000"`
	UserID      uuid.UUID `json:"-"` // Set from authentication context
}

// ChangeShoppingListStatusRequest represents a request to change shopping list status
type ChangeShoppingListStatusRequest struct {
	ID     uuid.UUID          `json:"-"` // Set from URL parameter
	Status ShoppingListStatus `json:"status" validate:"required,oneof=active completed archived"`
	UserID uuid.UUID          `json:"-"` // Set from authentication context
}

// AddShoppingListItemRequest represents a request to add an item to a shopping list
type AddShoppingListItemRequest struct {
	ShoppingListID   uuid.UUID  `json:"-"` // Set from URL parameter
	ProductVariantID *uuid.UUID `json:"product_variant_id,omitempty"`
	FreeTextName     *string    `json:"free_text_name,omitempty" validate:"omitempty,min=1,max=255"`
	QuantityDesired  float64    `json:"quantity_desired" validate:"required,gt=0"`
	UnitOfMeasureID  *uuid.UUID `json:"unit_of_measure_id,omitempty"`
	Notes            *string    `json:"notes,omitempty" validate:"omitempty,max=500"`
	UserID           uuid.UUID  `json:"-"` // Set from authentication context
}

// UpdateShoppingListItemRequest represents a request to update a shopping list item
type UpdateShoppingListItemRequest struct {
	ID               uuid.UUID  `json:"-"` // Set from URL parameter
	ProductVariantID *uuid.UUID `json:"product_variant_id,omitempty"`
	FreeTextName     *string    `json:"free_text_name,omitempty" validate:"omitempty,min=1,max=255"`
	QuantityDesired  float64    `json:"quantity_desired" validate:"required,gt=0"`
	UnitOfMeasureID  *uuid.UUID `json:"unit_of_measure_id,omitempty"`
	Notes            *string    `json:"notes,omitempty" validate:"omitempty,max=500"`
	UserID           uuid.UUID  `json:"-"` // Set from authentication context
}

// RemoveShoppingListItemRequest represents a request to remove an item from a shopping list
type RemoveShoppingListItemRequest struct {
	ID     uuid.UUID `json:"-"` // Set from URL parameter
	UserID uuid.UUID `json:"-"` // Set from authentication context
}

// MarkItemPurchasedRequest represents a request to mark an item as purchased
type MarkItemPurchasedRequest struct {
	ID     uuid.UUID `json:"-"` // Set from URL parameter
	UserID uuid.UUID `json:"-"` // Set from authentication context
}

// MarkItemNotPurchasedRequest represents a request to mark an item as not purchased
type MarkItemNotPurchasedRequest struct {
	ID     uuid.UUID `json:"-"` // Set from URL parameter
	UserID uuid.UUID `json:"-"` // Set from authentication context
}

// BulkMarkItemsPurchasedRequest represents a request to mark multiple items as purchased
type BulkMarkItemsPurchasedRequest struct {
	ItemIDs []uuid.UUID `json:"item_ids" validate:"required,min=1"`
	UserID  uuid.UUID   `json:"-"` // Set from authentication context
}

// BulkMarkItemsNotPurchasedRequest represents a request to mark multiple items as not purchased
type BulkMarkItemsNotPurchasedRequest struct {
	ItemIDs []uuid.UUID `json:"item_ids" validate:"required,min=1"`
	UserID  uuid.UUID   `json:"-"` // Set from authentication context
}

// Custom validation for ensuring either ProductVariantID or FreeTextName is provided
func (req *AddShoppingListItemRequest) Validate() error {
	if req.ProductVariantID == nil && req.FreeTextName == nil {
		return fmt.Errorf("either product_variant_id or free_text_name must be provided")
	}
	if req.ProductVariantID != nil && req.FreeTextName != nil {
		return fmt.Errorf("only one of product_variant_id or free_text_name should be provided")
	}
	return nil
}

func (req *UpdateShoppingListItemRequest) Validate() error {
	if req.ProductVariantID == nil && req.FreeTextName == nil {
		return fmt.Errorf("either product_variant_id or free_text_name must be provided")
	}
	if req.ProductVariantID != nil && req.FreeTextName != nil {
		return fmt.Errorf("only one of product_variant_id or free_text_name should be provided")
	}
	return nil
}
