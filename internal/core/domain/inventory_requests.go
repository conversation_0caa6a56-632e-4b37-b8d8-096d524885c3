package domain

import (
	"time"

	"github.com/google/uuid"
)

// Inventory Item Request/Response DTOs

// CreateInventoryItemRequest represents a request to create a new inventory item
type CreateInventoryItemRequest struct {
	LocationID       *uuid.UUID `json:"location_id,omitempty"`
	ProductVariantID uuid.UUID  `json:"product_variant_id" validate:"required"`
	Quantity         float64    `json:"quantity" validate:"required,gt=0"`
	UnitOfMeasureID  uuid.UUID  `json:"unit_of_measure_id" validate:"required"`
	PurchaseDate     *string    `json:"purchase_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	ExpirationDate   *string    `json:"expiration_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	PurchasePrice    *float64   `json:"purchase_price,omitempty" validate:"omitempty,gte=0"`
	Notes            *string    `json:"notes,omitempty" validate:"omitempty,max=1000"`
}

// UpdateInventoryItemRequest represents a request to update an inventory item
type UpdateInventoryItemRequest struct {
	LocationID     *uuid.UUID `json:"location_id,omitempty"`
	Quantity       float64    `json:"quantity" validate:"required,gt=0"`
	PurchaseDate   *string    `json:"purchase_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	ExpirationDate *string    `json:"expiration_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	PurchasePrice  *float64   `json:"purchase_price,omitempty" validate:"omitempty,gte=0"`
	Notes          *string    `json:"notes,omitempty" validate:"omitempty,max=1000"`
}

// UpdateInventoryQuantityRequest represents a request to update item quantity
type UpdateInventoryQuantityRequest struct {
	Quantity float64 `json:"quantity" validate:"required,gte=0"`
}

// ConsumeInventoryRequest represents a request to consume inventory
type ConsumeInventoryRequest struct {
	ConsumedQuantity float64 `json:"consumed_quantity" validate:"required,gt=0"`
	Notes            *string `json:"notes,omitempty" validate:"omitempty,max=500"`
}

// RestockInventoryRequest represents a request to restock inventory
type RestockInventoryRequest struct {
	AddedQuantity  float64  `json:"added_quantity" validate:"required,gt=0"`
	PurchaseDate   *string  `json:"purchase_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	ExpirationDate *string  `json:"expiration_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	PurchasePrice  *float64 `json:"purchase_price,omitempty" validate:"omitempty,gte=0"`
	Notes          *string  `json:"notes,omitempty" validate:"omitempty,max=500"`
}

// MoveInventoryRequest represents a request to move inventory to a different location
type MoveInventoryRequest struct {
	LocationID *uuid.UUID `json:"location_id,omitempty"`
}

// InventoryItemResponse represents an inventory item in API responses
type InventoryItemResponse struct {
	ID               uuid.UUID           `json:"id"`
	PantryID         uuid.UUID           `json:"pantry_id"`
	LocationID       *uuid.UUID          `json:"location_id,omitempty"`
	ProductVariantID uuid.UUID           `json:"product_variant_id"`
	Quantity         float64             `json:"quantity"`
	UnitOfMeasureID  uuid.UUID           `json:"unit_of_measure_id"`
	PurchaseDate     *string             `json:"purchase_date,omitempty"`
	ExpirationDate   *string             `json:"expiration_date,omitempty"`
	PurchasePrice    *float64            `json:"purchase_price,omitempty"`
	Notes            *string             `json:"notes,omitempty"`
	Status           InventoryItemStatus `json:"status"`
	CreatedAt        string              `json:"created_at"`
	UpdatedAt        string              `json:"updated_at"`

	// Optional nested data
	Location       *PantryLocationResponse `json:"location,omitempty"`
	ProductVariant *ProductVariantResponse `json:"product_variant,omitempty"`
	UnitOfMeasure  *UnitOfMeasureResponse  `json:"unit_of_measure,omitempty"`
}

// Search and Filter DTOs

// InventorySearchRequest represents an inventory search request
type InventorySearchRequest struct {
	Query            string               `json:"query,omitempty"`
	LocationID       *uuid.UUID           `json:"location_id,omitempty"`
	ProductVariantID *uuid.UUID           `json:"product_variant_id,omitempty"`
	Status           *InventoryItemStatus `json:"status,omitempty"`
	ExpiringInDays   *int                 `json:"expiring_in_days,omitempty" validate:"omitempty,gte=0,lte=365"`
	Page             int                  `json:"page,omitempty" validate:"omitempty,min=1"`
	Limit            int                  `json:"limit,omitempty" validate:"omitempty,min=1,max=100"`
}

// InventoryStatsResponse represents inventory statistics
type InventoryStatsResponse struct {
	TotalItems    int64                   `json:"total_items"`
	TotalValue    float64                 `json:"total_value"`
	ExpiringItems int64                   `json:"expiring_items"`
	ExpiredItems  int64                   `json:"expired_items"`
	LowStockItems int64                   `json:"low_stock_items"`
	EmptyItems    int64                   `json:"empty_items"`
	LocationStats []LocationStatsResponse `json:"location_stats,omitempty"`
}

// LocationStatsResponse represents statistics for a specific location
type LocationStatsResponse struct {
	LocationID   *uuid.UUID `json:"location_id"`
	LocationName *string    `json:"location_name,omitempty"`
	ItemCount    int64      `json:"item_count"`
	TotalValue   float64    `json:"total_value"`
}

// ExpiringItemsResponse represents items that are expiring soon
type ExpiringItemsResponse struct {
	ExpiringToday    []InventoryItemResponse `json:"expiring_today"`
	ExpiringThisWeek []InventoryItemResponse `json:"expiring_this_week"`
	Expired          []InventoryItemResponse `json:"expired"`
}

// LowStockItemsResponse represents items with low stock
type LowStockItemsResponse struct {
	LowStockItems []InventoryItemResponse `json:"low_stock_items"`
	EmptyItems    []InventoryItemResponse `json:"empty_items"`
}

// Inventory Transaction DTOs

// InventoryTransactionType represents the type of inventory transaction
type InventoryTransactionType string

const (
	TransactionTypeAdd     InventoryTransactionType = "add"
	TransactionTypeConsume InventoryTransactionType = "consume"
	TransactionTypeMove    InventoryTransactionType = "move"
	TransactionTypeUpdate  InventoryTransactionType = "update"
	TransactionTypeDelete  InventoryTransactionType = "delete"
)

// InventoryTransactionRequest represents a request to log an inventory transaction
type InventoryTransactionRequest struct {
	Type             InventoryTransactionType `json:"type" validate:"required,oneof=add consume move update delete"`
	Quantity         *float64                 `json:"quantity,omitempty"`
	PreviousQuantity *float64                 `json:"previous_quantity,omitempty"`
	Notes            *string                  `json:"notes,omitempty" validate:"omitempty,max=500"`
}

// InventoryTransactionResponse represents an inventory transaction in API responses
type InventoryTransactionResponse struct {
	ID               uuid.UUID                `json:"id"`
	InventoryItemID  uuid.UUID                `json:"inventory_item_id"`
	Type             InventoryTransactionType `json:"type"`
	Quantity         *float64                 `json:"quantity,omitempty"`
	PreviousQuantity *float64                 `json:"previous_quantity,omitempty"`
	Notes            *string                  `json:"notes,omitempty"`
	CreatedAt        string                   `json:"created_at"`
	CreatedBy        uuid.UUID                `json:"created_by"`

	// Optional nested data
	InventoryItem *InventoryItemResponse `json:"inventory_item,omitempty"`
}

// Bulk Operations DTOs

// BulkCreateInventoryItemsRequest represents a request to create multiple inventory items
type BulkCreateInventoryItemsRequest struct {
	Items []CreateInventoryItemRequest `json:"items" validate:"required,min=1,max=100,dive"`
}

// BulkCreateInventoryItemsResponse represents the response for bulk create operation
type BulkCreateInventoryItemsResponse struct {
	SuccessCount int                                `json:"success_count"`
	FailureCount int                                `json:"failure_count"`
	TotalCount   int                                `json:"total_count"`
	Results      []BulkInventoryItemOperationResult `json:"results"`
	Summary      BulkOperationSummary               `json:"summary"`
}

// BulkUpdateInventoryItemsRequest represents a request to update multiple inventory items
type BulkUpdateInventoryItemsRequest struct {
	Updates []BulkInventoryItemUpdate `json:"updates" validate:"required,min=1,max=100,dive"`
}

// BulkInventoryItemUpdate represents a single item update in bulk operation
type BulkInventoryItemUpdate struct {
	ItemID  uuid.UUID                  `json:"item_id" validate:"required"`
	Updates UpdateInventoryItemRequest `json:"updates" validate:"required"`
}

// BulkUpdateInventoryItemsResponse represents the response for bulk update operation
type BulkUpdateInventoryItemsResponse struct {
	SuccessCount int                                `json:"success_count"`
	FailureCount int                                `json:"failure_count"`
	TotalCount   int                                `json:"total_count"`
	Results      []BulkInventoryItemOperationResult `json:"results"`
	Summary      BulkOperationSummary               `json:"summary"`
}

// BulkConsumeInventoryRequest represents a request to consume from multiple inventory items
type BulkConsumeInventoryRequest struct {
	Consumptions []BulkInventoryItemConsumption `json:"consumptions" validate:"required,min=1,max=100,dive"`
	Reason       string                         `json:"reason,omitempty" validate:"max=500"`
	Notes        string                         `json:"notes,omitempty" validate:"max=1000"`
}

// BulkInventoryItemConsumption represents a single item consumption in bulk operation
type BulkInventoryItemConsumption struct {
	ItemID           uuid.UUID `json:"item_id" validate:"required"`
	ConsumedQuantity float64   `json:"consumed_quantity" validate:"required,gt=0"`
	Notes            string    `json:"notes,omitempty" validate:"max=500"`
}

// BulkConsumeInventoryResponse represents the response for bulk consume operation
type BulkConsumeInventoryResponse struct {
	SuccessCount int                                `json:"success_count"`
	FailureCount int                                `json:"failure_count"`
	TotalCount   int                                `json:"total_count"`
	Results      []BulkInventoryItemOperationResult `json:"results"`
	Summary      BulkOperationSummary               `json:"summary"`
}

// BulkDeleteInventoryItemsRequest represents a request to delete multiple inventory items
type BulkDeleteInventoryItemsRequest struct {
	ItemIDs []uuid.UUID `json:"item_ids" validate:"required,min=1,max=100,dive,required"`
	Reason  string      `json:"reason,omitempty" validate:"max=500"`
}

// BulkDeleteInventoryItemsResponse represents the response for bulk delete operation
type BulkDeleteInventoryItemsResponse struct {
	SuccessCount int                                `json:"success_count"`
	FailureCount int                                `json:"failure_count"`
	TotalCount   int                                `json:"total_count"`
	Results      []BulkInventoryItemOperationResult `json:"results"`
	Summary      BulkOperationSummary               `json:"summary"`
}

// BulkInventoryItemOperationResult represents the result of a single operation in bulk
type BulkInventoryItemOperationResult struct {
	Index   int                    `json:"index"`
	ItemID  *uuid.UUID             `json:"item_id,omitempty"`
	Success bool                   `json:"success"`
	Item    *InventoryItemResponse `json:"item,omitempty"`
	Error   *BulkOperationError    `json:"error,omitempty"`
}

// BulkOperationError represents an error that occurred during bulk operation
type BulkOperationError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Field   string `json:"field,omitempty"`
}

// BulkOperationSummary provides summary statistics for bulk operations
type BulkOperationSummary struct {
	TotalItems            int         `json:"total_items"`
	SuccessfulItems       int         `json:"successful_items"`
	FailedItems           int         `json:"failed_items"`
	SuccessRate           float64     `json:"success_rate"`
	TotalQuantityAdded    float64     `json:"total_quantity_added,omitempty"`
	TotalQuantityConsumed float64     `json:"total_quantity_consumed,omitempty"`
	AffectedPantries      []uuid.UUID `json:"affected_pantries,omitempty"`
}

// Recipe Integration DTOs

// RecipeConsumptionRequest represents a request to consume ingredients for a recipe
type RecipeConsumptionRequest struct {
	RecipeID     *uuid.UUID                    `json:"recipe_id,omitempty"`
	RecipeName   string                        `json:"recipe_name,omitempty" validate:"max=200"`
	Ingredients  []RecipeIngredientConsumption `json:"ingredients" validate:"required,min=1,max=50,dive"`
	ServingSize  int                           `json:"serving_size,omitempty" validate:"min=1,max=20"`
	Notes        string                        `json:"notes,omitempty" validate:"max=1000"`
	AutoSelect   bool                          `json:"auto_select"`   // Auto-select items based on expiration dates
	AllowPartial bool                          `json:"allow_partial"` // Allow partial consumption if insufficient stock
}

// RecipeIngredientConsumption represents consumption of a single ingredient
type RecipeIngredientConsumption struct {
	ProductVariantID uuid.UUID   `json:"product_variant_id" validate:"required"`
	RequiredQuantity float64     `json:"required_quantity" validate:"required,gt=0"`
	UnitOfMeasureID  uuid.UUID   `json:"unit_of_measure_id" validate:"required"`
	AllowPartial     bool        `json:"allow_partial"`
	PreferredItems   []uuid.UUID `json:"preferred_items,omitempty"`
	Notes            string      `json:"notes,omitempty" validate:"max=500"`
}

// RecipeConsumptionResponse represents the response for recipe consumption
type RecipeConsumptionResponse struct {
	RecipeID         *uuid.UUID                          `json:"recipe_id,omitempty"`
	RecipeName       string                              `json:"recipe_name,omitempty"`
	SuccessCount     int                                 `json:"success_count"`
	FailureCount     int                                 `json:"failure_count"`
	PartialCount     int                                 `json:"partial_count"`
	TotalIngredients int                                 `json:"total_ingredients"`
	Results          []RecipeIngredientConsumptionResult `json:"results"`
	Summary          RecipeConsumptionSummary            `json:"summary"`
	CanProceed       bool                                `json:"can_proceed"`
}

// RecipeIngredientConsumptionResult represents the result of consuming a single ingredient
type RecipeIngredientConsumptionResult struct {
	ProductVariantID  uuid.UUID                     `json:"product_variant_id"`
	RequiredQuantity  float64                       `json:"required_quantity"`
	ConsumedQuantity  float64                       `json:"consumed_quantity"`
	RemainingQuantity float64                       `json:"remaining_quantity"`
	UnitOfMeasureID   uuid.UUID                     `json:"unit_of_measure_id"`
	Success           bool                          `json:"success"`
	Partial           bool                          `json:"partial"`
	ItemsConsumed     []ConsumedInventoryItemDetail `json:"items_consumed"`
	Error             *BulkOperationError           `json:"error,omitempty"`
}

// ConsumedInventoryItemDetail represents details of a consumed inventory item
type ConsumedInventoryItemDetail struct {
	ItemID            uuid.UUID  `json:"item_id"`
	ConsumedQuantity  float64    `json:"consumed_quantity"`
	RemainingQuantity float64    `json:"remaining_quantity"`
	LocationID        *uuid.UUID `json:"location_id,omitempty"`
	ExpirationDate    *string    `json:"expiration_date,omitempty"`
	PurchasePrice     *float64   `json:"purchase_price,omitempty"`
}

// RecipeConsumptionSummary provides summary for recipe consumption
type RecipeConsumptionSummary struct {
	TotalIngredients      int     `json:"total_ingredients"`
	SuccessfulIngredients int     `json:"successful_ingredients"`
	PartialIngredients    int     `json:"partial_ingredients"`
	FailedIngredients     int     `json:"failed_ingredients"`
	CompletionRate        float64 `json:"completion_rate"`
	CanProceedWithRecipe  bool    `json:"can_proceed_with_recipe"`
	TotalValue            float64 `json:"total_value"`
	TotalItemsConsumed    int     `json:"total_items_consumed"`
}

// Shopping List Integration DTOs

// GenerateShoppingListRequest represents a request to generate a shopping list
type GenerateShoppingListRequest struct {
	IncludeLowStock     bool        `json:"include_low_stock"`
	IncludeEmpty        bool        `json:"include_empty"`
	IncludeExpiringSoon bool        `json:"include_expiring_soon"`
	ExpiringInDays      *int        `json:"expiring_in_days,omitempty" validate:"omitempty,gte=0,lte=30"`
	CategoryIDs         []uuid.UUID `json:"category_ids,omitempty"`
	LowStockThreshold   *float64    `json:"low_stock_threshold,omitempty" validate:"omitempty,gt=0"`
	UseConsumptionData  bool        `json:"use_consumption_data"`
	ConsumptionDays     *int        `json:"consumption_days,omitempty" validate:"omitempty,gte=7,lte=90"`
	IncludeRecipeItems  bool        `json:"include_recipe_items"`
	RecipeIDs           []uuid.UUID `json:"recipe_ids,omitempty"`
}

// GenerateShoppingListResponse represents the response for shopping list generation
type GenerateShoppingListResponse struct {
	TotalItems    int                 `json:"total_items"`
	LowStockItems int                 `json:"low_stock_items"`
	EmptyItems    int                 `json:"empty_items"`
	ExpiringItems int                 `json:"expiring_items"`
	RecipeItems   int                 `json:"recipe_items"`
	Items         []ShoppingListItem  `json:"items"`
	Summary       ShoppingListSummary `json:"summary"`
	GeneratedAt   string              `json:"generated_at"`
}

// ShoppingListItem represents an item in the shopping list
type ShoppingListItem struct {
	ProductVariantID  uuid.UUID            `json:"product_variant_id"`
	ProductName       string               `json:"product_name"`
	VariantName       string               `json:"variant_name"`
	CategoryID        uuid.UUID            `json:"category_id"`
	CategoryName      string               `json:"category_name"`
	SuggestedQuantity float64              `json:"suggested_quantity"`
	UnitOfMeasureID   uuid.UUID            `json:"unit_of_measure_id"`
	UnitName          string               `json:"unit_name"`
	UnitSymbol        string               `json:"unit_symbol"`
	CurrentQuantity   float64              `json:"current_quantity"`
	Reasons           []ShoppingListReason `json:"reasons"`
	Priority          ShoppingListPriority `json:"priority"`
	EstimatedPrice    *float64             `json:"estimated_price,omitempty"`
	LastPurchasePrice *float64             `json:"last_purchase_price,omitempty"`
	LastPurchaseDate  *string              `json:"last_purchase_date,omitempty"`
	ConsumptionRate   *float64             `json:"consumption_rate,omitempty"` // per day
	DaysUntilEmpty    *int                 `json:"days_until_empty,omitempty"`
	RecommendedBrands []string             `json:"recommended_brands,omitempty"`
}

// ShoppingListReason represents why an item is on the shopping list
type ShoppingListReason struct {
	Type        string                 `json:"type"` // "low_stock", "empty", "expiring_soon", "recipe_required"
	Description string                 `json:"description"`
	Details     map[string]interface{} `json:"details,omitempty"`
}

// ShoppingListPriority represents the priority level of a shopping list item
type ShoppingListPriority string

const (
	ShoppingListPriorityHigh   ShoppingListPriority = "high"
	ShoppingListPriorityMedium ShoppingListPriority = "medium"
	ShoppingListPriorityLow    ShoppingListPriority = "low"
)

// ShoppingListSummary provides summary statistics for the shopping list
type ShoppingListSummary struct {
	TotalItems          int               `json:"total_items"`
	HighPriorityItems   int               `json:"high_priority_items"`
	MediumPriorityItems int               `json:"medium_priority_items"`
	LowPriorityItems    int               `json:"low_priority_items"`
	EstimatedTotal      float64           `json:"estimated_total"`
	CategoriesCount     int               `json:"categories_count"`
	TopCategories       []CategorySummary `json:"top_categories"`
}

// CategorySummary represents a summary of items in a category
type CategorySummary struct {
	CategoryID    uuid.UUID `json:"category_id"`
	CategoryName  string    `json:"category_name"`
	ItemCount     int       `json:"item_count"`
	EstimatedCost float64   `json:"estimated_cost"`
}

// RecipeShoppingListRequest represents a request to generate shopping list from recipes
type RecipeShoppingListRequest struct {
	RecipeIDs           []uuid.UUID `json:"recipe_ids" validate:"required,min=1,max=20,dive,required"`
	ServingSizes        []int       `json:"serving_sizes,omitempty" validate:"omitempty,dive,min=1,max=20"`
	ExcludeAvailable    bool        `json:"exclude_available"`
	IncludeAlternatives bool        `json:"include_alternatives"`
	GroupByCategory     bool        `json:"group_by_category"`
}

// RecipeShoppingListResponse represents the response for recipe-based shopping list
type RecipeShoppingListResponse struct {
	RecipeCount      int                   `json:"recipe_count"`
	TotalIngredients int                   `json:"total_ingredients"`
	MissingItems     int                   `json:"missing_items"`
	PartialItems     int                   `json:"partial_items"`
	Items            []ShoppingListItem    `json:"items"`
	RecipeBreakdown  []RecipeItemBreakdown `json:"recipe_breakdown"`
	Summary          ShoppingListSummary   `json:"summary"`
	GeneratedAt      string                `json:"generated_at"`
}

// RecipeItemBreakdown shows which recipes require which items
type RecipeItemBreakdown struct {
	RecipeID      uuid.UUID `json:"recipe_id"`
	RecipeName    string    `json:"recipe_name"`
	ServingSize   int       `json:"serving_size"`
	RequiredItems int       `json:"required_items"`
	MissingItems  int       `json:"missing_items"`
	EstimatedCost float64   `json:"estimated_cost"`
}

// Expiration Tracking DTOs

// ExpirationTrackingRequest represents a request to check for expiring items
type ExpirationTrackingRequest struct {
	WarningDays  int                   `json:"warning_days" validate:"min=1,max=30"`
	AlertDays    int                   `json:"alert_days" validate:"min=0,max=7"`
	CriticalDays int                   `json:"critical_days" validate:"min=0,max=1"`
	CategoryIDs  []uuid.UUID           `json:"category_ids,omitempty"`
	Channels     []NotificationChannel `json:"channels,omitempty"`
	SendAlerts   bool                  `json:"send_alerts"`
}

// ExpirationTrackingResponse represents the response for expiration tracking
type ExpirationTrackingResponse struct {
	TotalItems    int                     `json:"total_items"`
	WarningItems  int                     `json:"warning_items"`
	AlertItems    int                     `json:"alert_items"`
	CriticalItems int                     `json:"critical_items"`
	ExpiredItems  int                     `json:"expired_items"`
	Items         []ExpiringInventoryItem `json:"items"`
	Summary       ExpirationSummary       `json:"summary"`
	AlertsSent    int                     `json:"alerts_sent"`
	CheckedAt     string                  `json:"checked_at"`
}

// ExpiringInventoryItem represents an inventory item that is expiring
type ExpiringInventoryItem struct {
	ItemID           uuid.UUID          `json:"item_id"`
	ProductVariantID uuid.UUID          `json:"product_variant_id"`
	ProductName      string             `json:"product_name"`
	VariantName      string             `json:"variant_name"`
	CategoryID       uuid.UUID          `json:"category_id"`
	CategoryName     string             `json:"category_name"`
	Quantity         float64            `json:"quantity"`
	UnitOfMeasureID  uuid.UUID          `json:"unit_of_measure_id"`
	UnitName         string             `json:"unit_name"`
	UnitSymbol       string             `json:"unit_symbol"`
	ExpirationDate   string             `json:"expiration_date"`
	DaysUntilExpiry  int                `json:"days_until_expiry"`
	ExpirationStatus ExpirationStatus   `json:"expiration_status"`
	LocationID       *uuid.UUID         `json:"location_id,omitempty"`
	LocationName     *string            `json:"location_name,omitempty"`
	PurchasePrice    *float64           `json:"purchase_price,omitempty"`
	EstimatedValue   float64            `json:"estimated_value"`
	Actions          []ExpirationAction `json:"actions"`
}

// ExpirationStatus represents the expiration status of an item
type ExpirationStatus string

const (
	ExpirationStatusWarning  ExpirationStatus = "warning"  // Expiring in warning_days
	ExpirationStatusAlert    ExpirationStatus = "alert"    // Expiring in alert_days
	ExpirationStatusCritical ExpirationStatus = "critical" // Expiring in critical_days
	ExpirationStatusExpired  ExpirationStatus = "expired"  // Already expired
)

// ExpirationAction represents suggested actions for expiring items
type ExpirationAction struct {
	Type        string `json:"type"` // "consume", "freeze", "donate", "discard"
	Description string `json:"description"`
	Priority    int    `json:"priority"` // 1 = highest priority
}

// ExpirationSummary provides summary statistics for expiration tracking
type ExpirationSummary struct {
	TotalValue         float64                     `json:"total_value"`
	WarningValue       float64                     `json:"warning_value"`
	AlertValue         float64                     `json:"alert_value"`
	CriticalValue      float64                     `json:"critical_value"`
	ExpiredValue       float64                     `json:"expired_value"`
	TopCategories      []CategoryExpirationSummary `json:"top_categories"`
	RecommendedActions []string                    `json:"recommended_actions"`
}

// CategoryExpirationSummary represents expiration summary for a category
type CategoryExpirationSummary struct {
	CategoryID   uuid.UUID        `json:"category_id"`
	CategoryName string           `json:"category_name"`
	ItemCount    int              `json:"item_count"`
	TotalValue   float64          `json:"total_value"`
	WorstStatus  ExpirationStatus `json:"worst_status"`
}

// AlertConfigurationRequest represents a request to configure expiration alerts
type AlertConfigurationRequest struct {
	Enabled         bool                  `json:"enabled"`
	WarningDays     int                   `json:"warning_days" validate:"min=1,max=30"`
	AlertDays       int                   `json:"alert_days" validate:"min=0,max=7"`
	CriticalDays    int                   `json:"critical_days" validate:"min=0,max=1"`
	Channels        []NotificationChannel `json:"channels" validate:"required,min=1,dive,required"`
	QuietHours      *QuietHours           `json:"quiet_hours,omitempty"`
	CategoryFilters []uuid.UUID           `json:"category_filters,omitempty"`
	MinValue        *float64              `json:"min_value,omitempty" validate:"omitempty,gte=0"`
}

// QuietHours represents time periods when notifications should not be sent
type QuietHours struct {
	Enabled   bool   `json:"enabled"`
	StartTime string `json:"start_time" validate:"required_if=Enabled true"`
	EndTime   string `json:"end_time" validate:"required_if=Enabled true"`
	Timezone  string `json:"timezone" validate:"required_if=Enabled true"`
}

// AlertConfiguration represents the stored alert configuration for a user/pantry
type AlertConfiguration struct {
	ID              uuid.UUID             `json:"id"`
	UserID          uuid.UUID             `json:"user_id"`
	PantryID        *uuid.UUID            `json:"pantry_id,omitempty"`
	Enabled         bool                  `json:"enabled"`
	WarningDays     int                   `json:"warning_days"`
	AlertDays       int                   `json:"alert_days"`
	CriticalDays    int                   `json:"critical_days"`
	Channels        []NotificationChannel `json:"channels"`
	QuietHours      *QuietHours           `json:"quiet_hours,omitempty"`
	CategoryFilters []uuid.UUID           `json:"category_filters,omitempty"`
	MinValue        *float64              `json:"min_value,omitempty"`
	LastChecked     *time.Time            `json:"last_checked,omitempty"`
	CreatedAt       time.Time             `json:"created_at"`
	UpdatedAt       time.Time             `json:"updated_at"`
}

// NewAlertConfiguration creates a new alert configuration
func NewAlertConfiguration(userID uuid.UUID, pantryID *uuid.UUID, req *AlertConfigurationRequest) *AlertConfiguration {
	now := time.Now()
	return &AlertConfiguration{
		ID:              uuid.New(),
		UserID:          userID,
		PantryID:        pantryID,
		Enabled:         req.Enabled,
		WarningDays:     req.WarningDays,
		AlertDays:       req.AlertDays,
		CriticalDays:    req.CriticalDays,
		Channels:        req.Channels,
		QuietHours:      req.QuietHours,
		CategoryFilters: req.CategoryFilters,
		MinValue:        req.MinValue,
		CreatedAt:       now,
		UpdatedAt:       now,
	}
}

// UpdateConfiguration updates the alert configuration
func (ac *AlertConfiguration) UpdateConfiguration(req *AlertConfigurationRequest) {
	ac.Enabled = req.Enabled
	ac.WarningDays = req.WarningDays
	ac.AlertDays = req.AlertDays
	ac.CriticalDays = req.CriticalDays
	ac.Channels = req.Channels
	ac.QuietHours = req.QuietHours
	ac.CategoryFilters = req.CategoryFilters
	ac.MinValue = req.MinValue
	ac.UpdatedAt = time.Now()
}

// UpdateLastChecked updates the last checked timestamp
func (ac *AlertConfiguration) UpdateLastChecked() {
	now := time.Now()
	ac.LastChecked = &now
	ac.UpdatedAt = now
}
