package domain

import (
	"time"

	"github.com/google/uuid"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// PantryRole represents the role of a user in a pantry
type PantryRole string

const (
	PantryRoleOwner  PantryRole = "owner"
	PantryRoleAdmin  PantryRole = "admin"
	PantryRoleEditor PantryRole = "editor"
	PantryRoleViewer PantryRole = "viewer"
)

// PantryMembershipStatus represents the status of a pantry membership
type PantryMembershipStatus string

const (
	PantryMembershipStatusPendingInvitation PantryMembershipStatus = "pending_invitation"
	PantryMembershipStatusActive            PantryMembershipStatus = "active"
	PantryMembershipStatusInactive          PantryMembershipStatus = "inactive"
	PantryMembershipStatusRemoved           PantryMembershipStatus = "removed"
)

// Pantry represents a pantry space owned by a user
type Pantry struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty"`
	OwnerUserID uuid.UUID  `json:"owner_user_id"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty"`

	// Domain events
	events []DomainEvent
}

// NewPantry creates a new pantry
func NewPantry(name string, description *string, ownerUserID uuid.UUID) *Pantry {
	pantry := &Pantry{
		ID:          uuid.New(),
		Name:        name,
		Description: description,
		OwnerUserID: ownerUserID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		events:      make([]DomainEvent, 0),
	}

	// Add domain event
	pantry.AddEvent(NewPantryCreatedEvent(pantry.ID, pantry.OwnerUserID, pantry.Name))

	return pantry
}

// UpdateDetails updates pantry name and description
func (p *Pantry) UpdateDetails(name string, description *string) {
	p.Name = name
	p.Description = description
	p.UpdatedAt = time.Now()

	// Add domain event
	p.AddEvent(NewPantryUpdatedEvent(p.ID, p.OwnerUserID, name))
}

// TransferOwnership transfers ownership to another user
func (p *Pantry) TransferOwnership(newOwnerUserID uuid.UUID) {
	oldOwnerUserID := p.OwnerUserID
	p.OwnerUserID = newOwnerUserID
	p.UpdatedAt = time.Now()

	// Add domain event
	p.AddEvent(NewPantryOwnershipTransferredEvent(p.ID, oldOwnerUserID, newOwnerUserID))
}

// SoftDelete marks pantry as deleted
func (p *Pantry) SoftDelete() {
	now := time.Now()
	p.DeletedAt = &now
	p.UpdatedAt = now

	// Add domain event
	p.AddEvent(NewPantryDeletedEvent(p.ID, p.OwnerUserID))
}

// IsDeleted checks if pantry is soft deleted
func (p *Pantry) IsDeleted() bool {
	return p.DeletedAt != nil
}

// AddEvent adds a domain event
func (p *Pantry) AddEvent(event DomainEvent) {
	p.events = append(p.events, event)
}

// GetEvents returns all domain events
func (p *Pantry) GetEvents() []DomainEvent {
	return p.events
}

// ClearEvents clears all domain events
func (p *Pantry) ClearEvents() {
	p.events = make([]DomainEvent, 0)
}

// PantryMembership represents a user's membership in a pantry
type PantryMembership struct {
	ID              uuid.UUID              `json:"id"`
	PantryID        uuid.UUID              `json:"pantry_id"`
	UserID          uuid.UUID              `json:"user_id"`
	Role            PantryRole             `json:"role"`
	Status          PantryMembershipStatus `json:"status"`
	JoinedAt        time.Time              `json:"joined_at"`
	InvitedByUserID *uuid.UUID             `json:"invited_by_user_id,omitempty"`
	DeletedAt       *time.Time             `json:"deleted_at,omitempty"`

	// Domain events
	events []DomainEvent
}

// NewPantryMembership creates a new pantry membership
func NewPantryMembership(pantryID, userID uuid.UUID, role PantryRole, invitedByUserID *uuid.UUID) *PantryMembership {
	membership := &PantryMembership{
		ID:              uuid.New(),
		PantryID:        pantryID,
		UserID:          userID,
		Role:            role,
		Status:          PantryMembershipStatusPendingInvitation,
		JoinedAt:        time.Now(),
		InvitedByUserID: invitedByUserID,
		events:          make([]DomainEvent, 0),
	}

	// Add domain event
	membership.AddEvent(NewPantryMemberInvitedEvent(pantryID, userID, string(role), invitedByUserID))

	return membership
}

// AcceptInvitation accepts a pending invitation
func (pm *PantryMembership) AcceptInvitation() error {
	if pm.Status != PantryMembershipStatusPendingInvitation {
		return errors.NewBusinessError("INVALID_MEMBERSHIP_STATUS", "Can only accept pending invitations")
	}

	pm.Status = PantryMembershipStatusActive

	// Add domain event
	pm.AddEvent(NewPantryMemberJoinedEvent(pm.PantryID, pm.UserID, string(pm.Role)))

	return nil
}

// RejectInvitation rejects a pending invitation
func (pm *PantryMembership) RejectInvitation() error {
	if pm.Status != PantryMembershipStatusPendingInvitation {
		return errors.NewBusinessError("INVALID_MEMBERSHIP_STATUS", "Can only reject pending invitations")
	}

	pm.Status = PantryMembershipStatusRemoved

	// Add domain event
	pm.AddEvent(NewPantryMemberRemovedEvent(pm.PantryID, pm.UserID, string(pm.Role), "invitation_rejected"))

	return nil
}

// UpdateRole updates the member's role
func (pm *PantryMembership) UpdateRole(newRole PantryRole) {
	oldRole := pm.Role
	pm.Role = newRole

	// Add domain event
	pm.AddEvent(NewPantryMemberRoleUpdatedEvent(pm.PantryID, pm.UserID, string(oldRole), string(newRole)))
}

// Remove removes the member from the pantry
func (pm *PantryMembership) Remove(reason string) {
	pm.Status = PantryMembershipStatusRemoved
	now := time.Now()
	pm.DeletedAt = &now

	// Add domain event
	pm.AddEvent(NewPantryMemberRemovedEvent(pm.PantryID, pm.UserID, string(pm.Role), reason))
}

// IsActive checks if membership is active
func (pm *PantryMembership) IsActive() bool {
	return pm.Status == PantryMembershipStatusActive && pm.DeletedAt == nil
}

// IsPending checks if membership is pending invitation
func (pm *PantryMembership) IsPending() bool {
	return pm.Status == PantryMembershipStatusPendingInvitation && pm.DeletedAt == nil
}

// AddEvent adds a domain event
func (pm *PantryMembership) AddEvent(event DomainEvent) {
	pm.events = append(pm.events, event)
}

// GetEvents returns all domain events
func (pm *PantryMembership) GetEvents() []DomainEvent {
	return pm.events
}

// ClearEvents clears all domain events
func (pm *PantryMembership) ClearEvents() {
	pm.events = make([]DomainEvent, 0)
}

// PantryLocation represents a storage location within a pantry
type PantryLocation struct {
	ID          uuid.UUID  `json:"id"`
	PantryID    uuid.UUID  `json:"pantry_id"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty"`

	// Domain events
	events []DomainEvent
}

// NewPantryLocation creates a new pantry location
func NewPantryLocation(pantryID uuid.UUID, name string, description *string) *PantryLocation {
	location := &PantryLocation{
		ID:          uuid.New(),
		PantryID:    pantryID,
		Name:        name,
		Description: description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		events:      make([]DomainEvent, 0),
	}

	// Add domain event
	location.AddEvent(NewPantryLocationCreatedEvent(location.ID, pantryID, name))

	return location
}

// UpdateDetails updates location name and description
func (pl *PantryLocation) UpdateDetails(name string, description *string) {
	pl.Name = name
	pl.Description = description
	pl.UpdatedAt = time.Now()

	// Add domain event
	pl.AddEvent(NewPantryLocationUpdatedEvent(pl.ID, pl.PantryID, name))
}

// SoftDelete marks location as deleted
func (pl *PantryLocation) SoftDelete() {
	now := time.Now()
	pl.DeletedAt = &now
	pl.UpdatedAt = now

	// Add domain event
	pl.AddEvent(NewPantryLocationDeletedEvent(pl.ID, pl.PantryID))
}

// IsDeleted checks if location is soft deleted
func (pl *PantryLocation) IsDeleted() bool {
	return pl.DeletedAt != nil
}

// AddEvent adds a domain event
func (pl *PantryLocation) AddEvent(event DomainEvent) {
	pl.events = append(pl.events, event)
}

// GetEvents returns all domain events
func (pl *PantryLocation) GetEvents() []DomainEvent {
	return pl.events
}

// ClearEvents clears all domain events
func (pl *PantryLocation) ClearEvents() {
	pl.events = make([]DomainEvent, 0)
}

// Repository interfaces

// PantryRepository defines the interface for pantry data access
type PantryRepository interface {
	// Create creates a new pantry
	Create(pantry *Pantry) error

	// GetByID retrieves a pantry by ID
	GetByID(id uuid.UUID) (*Pantry, error)

	// GetUserPantries retrieves all pantries for a user (owned + member)
	GetUserPantries(userID uuid.UUID, page, limit int) ([]*Pantry, int64, error)

	// GetOwnedPantries retrieves pantries owned by a user
	GetOwnedPantries(userID uuid.UUID, page, limit int) ([]*Pantry, int64, error)

	// Update updates an existing pantry
	Update(pantry *Pantry) error

	// Delete soft deletes a pantry
	Delete(id uuid.UUID) error

	// ExistsByName checks if a pantry with the given name exists for a user
	ExistsByName(ownerUserID uuid.UUID, name string) (bool, error)
}

// PantryMembershipRepository defines the interface for pantry membership data access
type PantryMembershipRepository interface {
	// Create creates a new pantry membership
	Create(membership *PantryMembership) error

	// GetByID retrieves a membership by ID
	GetByID(id uuid.UUID) (*PantryMembership, error)

	// GetByPantryAndUser retrieves a membership by pantry and user
	GetByPantryAndUser(pantryID, userID uuid.UUID) (*PantryMembership, error)

	// GetPantryMembers retrieves all members of a pantry
	GetPantryMembers(pantryID uuid.UUID, page, limit int) ([]*PantryMembership, int64, error)

	// GetUserMemberships retrieves all memberships for a user
	GetUserMemberships(userID uuid.UUID, page, limit int) ([]*PantryMembership, int64, error)

	// GetPendingInvitations retrieves pending invitations for a user
	GetPendingInvitations(userID uuid.UUID) ([]*PantryMembership, error)

	// Update updates an existing membership
	Update(membership *PantryMembership) error

	// Delete removes a membership
	Delete(id uuid.UUID) error

	// ExistsActiveMembership checks if an active membership exists
	ExistsActiveMembership(pantryID, userID uuid.UUID) (bool, error)
}

// PantryLocationRepository defines the interface for pantry location data access
type PantryLocationRepository interface {
	// Create creates a new pantry location
	Create(location *PantryLocation) error

	// GetByID retrieves a location by ID
	GetByID(id uuid.UUID) (*PantryLocation, error)

	// GetByPantry retrieves all locations for a pantry
	GetByPantry(pantryID uuid.UUID) ([]*PantryLocation, error)

	// Update updates an existing location
	Update(location *PantryLocation) error

	// Delete soft deletes a location
	Delete(id uuid.UUID) error

	// ExistsByName checks if a location with the given name exists in a pantry
	ExistsByName(pantryID uuid.UUID, name string) (bool, error)
}
