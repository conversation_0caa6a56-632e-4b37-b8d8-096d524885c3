package domain

import (
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// InventoryItem represents an item in a pantry's inventory
type InventoryItem struct {
	ID               uuid.UUID  `json:"id"`
	PantryID         uuid.UUID  `json:"pantry_id"`
	LocationID       *uuid.UUID `json:"location_id,omitempty"`
	ProductVariantID uuid.UUID  `json:"product_variant_id"`
	Quantity         float64    `json:"quantity"`
	UnitOfMeasureID  uuid.UUID  `json:"unit_of_measure_id"`
	PurchaseDate     *time.Time `json:"purchase_date,omitempty"`
	ExpirationDate   *time.Time `json:"expiration_date,omitempty"`
	PurchasePrice    *float64   `json:"purchase_price,omitempty"`
	Notes            *string    `json:"notes,omitempty"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	DeletedAt        *time.Time `json:"deleted_at,omitempty"`

	// Domain events
	events []DomainEvent
}

// InventoryItemStatus represents the status of an inventory item
type InventoryItemStatus string

const (
	InventoryStatusFresh   InventoryItemStatus = "fresh"
	InventoryStatusExpired InventoryItemStatus = "expired"
	InventoryStatusLow     InventoryItemStatus = "low"
	InventoryStatusEmpty   InventoryItemStatus = "empty"
)

// NewInventoryItem creates a new inventory item
func NewInventoryItem(
	pantryID uuid.UUID,
	locationID *uuid.UUID,
	productVariantID uuid.UUID,
	quantity float64,
	unitOfMeasureID uuid.UUID,
	purchaseDate *time.Time,
	expirationDate *time.Time,
	purchasePrice *float64,
	notes *string,
) *InventoryItem {
	now := time.Now()

	item := &InventoryItem{
		ID:               uuid.New(),
		PantryID:         pantryID,
		LocationID:       locationID,
		ProductVariantID: productVariantID,
		Quantity:         quantity,
		UnitOfMeasureID:  unitOfMeasureID,
		PurchaseDate:     purchaseDate,
		ExpirationDate:   expirationDate,
		PurchasePrice:    purchasePrice,
		Notes:            notes,
		CreatedAt:        now,
		UpdatedAt:        now,
		events:           make([]DomainEvent, 0),
	}

	// Add domain event
	item.AddEvent(NewInventoryItemCreatedEvent(
		item.ID,
		item.PantryID,
		item.ProductVariantID,
		item.Quantity,
		item.UnitOfMeasureID,
	))

	return item
}

// UpdateQuantity updates the quantity of the inventory item
func (i *InventoryItem) UpdateQuantity(newQuantity float64) error {
	if newQuantity < 0 {
		return errors.NewValidationError("Invalid quantity", map[string]string{
			"quantity": "Quantity cannot be negative",
		})
	}

	oldQuantity := i.Quantity
	i.Quantity = newQuantity
	i.UpdatedAt = time.Now()

	// Add domain event
	i.AddEvent(NewInventoryItemQuantityUpdatedEvent(
		i.ID,
		oldQuantity,
		newQuantity,
		i.UnitOfMeasureID,
	))

	return nil
}

// UpdateLocation updates the location of the inventory item
func (i *InventoryItem) UpdateLocation(newLocationID *uuid.UUID) {
	oldLocationID := i.LocationID
	i.LocationID = newLocationID
	i.UpdatedAt = time.Now()

	// Add domain event
	i.AddEvent(NewInventoryItemLocationUpdatedEvent(
		i.ID,
		oldLocationID,
		newLocationID,
	))
}

// UpdateExpirationDate updates the expiration date
func (i *InventoryItem) UpdateExpirationDate(newExpirationDate *time.Time) {
	i.ExpirationDate = newExpirationDate
	i.UpdatedAt = time.Now()

	// Add domain event
	i.AddEvent(NewInventoryItemExpirationUpdatedEvent(
		i.ID,
		newExpirationDate,
	))
}

// UpdateDetails updates the item details
func (i *InventoryItem) UpdateDetails(
	quantity float64,
	locationID *uuid.UUID,
	purchaseDate *time.Time,
	expirationDate *time.Time,
	purchasePrice *float64,
	notes *string,
) error {
	if quantity < 0 {
		return errors.NewValidationError("Invalid quantity", map[string]string{
			"quantity": "Quantity cannot be negative",
		})
	}

	oldQuantity := i.Quantity
	oldLocationID := i.LocationID

	i.Quantity = quantity
	i.LocationID = locationID
	i.PurchaseDate = purchaseDate
	i.ExpirationDate = expirationDate
	i.PurchasePrice = purchasePrice
	i.Notes = notes
	i.UpdatedAt = time.Now()

	// Add domain event
	i.AddEvent(NewInventoryItemUpdatedEvent(
		i.ID,
		oldQuantity,
		quantity,
		oldLocationID,
		locationID,
	))

	return nil
}

// ConsumeQuantity reduces the quantity by the specified amount
func (i *InventoryItem) ConsumeQuantity(consumedQuantity float64) error {
	if consumedQuantity <= 0 {
		return errors.NewValidationError("Invalid consumed quantity", map[string]string{
			"consumed_quantity": "Consumed quantity must be positive",
		})
	}

	if consumedQuantity > i.Quantity {
		return errors.NewValidationError("Insufficient quantity", map[string]string{
			"consumed_quantity": "Cannot consume more than available quantity",
		})
	}

	oldQuantity := i.Quantity
	i.Quantity -= consumedQuantity
	i.UpdatedAt = time.Now()

	// Add domain event
	i.AddEvent(NewInventoryItemConsumedEvent(
		i.ID,
		consumedQuantity,
		oldQuantity,
		i.Quantity,
		i.UnitOfMeasureID,
	))

	return nil
}

// AddQuantity increases the quantity by the specified amount
func (i *InventoryItem) AddQuantity(addedQuantity float64) error {
	if addedQuantity <= 0 {
		return errors.NewValidationError("Invalid added quantity", map[string]string{
			"added_quantity": "Added quantity must be positive",
		})
	}

	oldQuantity := i.Quantity
	i.Quantity += addedQuantity
	i.UpdatedAt = time.Now()

	// Add domain event
	i.AddEvent(NewInventoryItemRestockedEvent(
		i.ID,
		addedQuantity,
		oldQuantity,
		i.Quantity,
		i.UnitOfMeasureID,
	))

	return nil
}

// GetStatus returns the current status of the inventory item
func (i *InventoryItem) GetStatus() InventoryItemStatus {
	now := time.Now()

	// Check if expired
	if i.ExpirationDate != nil && i.ExpirationDate.Before(now) {
		return InventoryStatusExpired
	}

	// Check if empty
	if i.Quantity <= 0 {
		return InventoryStatusEmpty
	}

	// Check if low (this could be configurable per item type)
	// For now, consider low if quantity is less than 10% of some baseline
	// This is a simplified implementation
	if i.Quantity < 1.0 {
		return InventoryStatusLow
	}

	return InventoryStatusFresh
}

// IsExpired checks if the item is expired
func (i *InventoryItem) IsExpired() bool {
	if i.ExpirationDate == nil {
		return false
	}
	return i.ExpirationDate.Before(time.Now())
}

// IsExpiringSoon checks if the item is expiring within the specified days
func (i *InventoryItem) IsExpiringSoon(days int) bool {
	if i.ExpirationDate == nil {
		return false
	}
	threshold := time.Now().AddDate(0, 0, days)
	return i.ExpirationDate.Before(threshold)
}

// IsEmpty checks if the item quantity is zero or negative
func (i *InventoryItem) IsEmpty() bool {
	return i.Quantity <= 0
}

// SoftDelete marks the inventory item as deleted
func (i *InventoryItem) SoftDelete() {
	now := time.Now()
	i.DeletedAt = &now
	i.UpdatedAt = now

	// Add domain event
	i.AddEvent(NewInventoryItemDeletedEvent(i.ID, i.PantryID, i.ProductVariantID))
}

// AddEvent adds a domain event
func (i *InventoryItem) AddEvent(event DomainEvent) {
	i.events = append(i.events, event)
}

// GetEvents returns all domain events
func (i *InventoryItem) GetEvents() []DomainEvent {
	return i.events
}

// ClearEvents clears all domain events
func (i *InventoryItem) ClearEvents() {
	i.events = make([]DomainEvent, 0)
}

// Repository interface for inventory items
type InventoryItemRepository interface {
	Create(item *InventoryItem) error
	GetByID(id uuid.UUID) (*InventoryItem, error)
	GetByPantryID(pantryID uuid.UUID, page, limit int) ([]*InventoryItem, int64, error)
	GetByLocationID(locationID uuid.UUID) ([]*InventoryItem, error)
	GetByProductVariantID(productVariantID uuid.UUID) ([]*InventoryItem, error)
	GetExpiringItems(pantryID uuid.UUID, days int) ([]*InventoryItem, error)
	GetLowStockItems(pantryID uuid.UUID) ([]*InventoryItem, error)
	SearchItems(pantryID uuid.UUID, query string, page, limit int) ([]*InventoryItem, int64, error)
	Update(item *InventoryItem) error
	Delete(id uuid.UUID) error
	GetTotalQuantityByProductVariant(pantryID uuid.UUID, productVariantID uuid.UUID) (float64, error)
}

// Validation functions

// ValidateInventoryQuantity validates inventory quantity
func ValidateInventoryQuantity(quantity float64) error {
	if quantity < 0 {
		return errors.NewValidationError("Invalid quantity", map[string]string{
			"quantity": "Quantity cannot be negative",
		})
	}

	// Set a reasonable upper limit to prevent data issues
	if quantity > 999999.99 {
		return errors.NewValidationError("Invalid quantity", map[string]string{
			"quantity": "Quantity is too large",
		})
	}

	return nil
}

// ValidateInventoryNotes validates inventory notes
func ValidateInventoryNotes(notes *string) error {
	if notes != nil {
		if len(strings.TrimSpace(*notes)) == 0 {
			return errors.NewValidationError("Invalid notes", map[string]string{
				"notes": "Notes cannot be empty if provided",
			})
		}

		if len(*notes) > 1000 {
			return errors.NewValidationError("Invalid notes", map[string]string{
				"notes": "Notes cannot exceed 1000 characters",
			})
		}
	}

	return nil
}

// ValidateInventoryPrice validates purchase price
func ValidateInventoryPrice(price *float64) error {
	if price != nil {
		if *price < 0 {
			return errors.NewValidationError("Invalid price", map[string]string{
				"purchase_price": "Price cannot be negative",
			})
		}

		if *price > 999999.99 {
			return errors.NewValidationError("Invalid price", map[string]string{
				"purchase_price": "Price is too large",
			})
		}
	}

	return nil
}

// Domain Events

// InventoryItemCreatedEvent is raised when an inventory item is created
type InventoryItemCreatedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemCreatedEvent creates a new inventory item created event
func NewInventoryItemCreatedEvent(itemID, pantryID, productVariantID uuid.UUID, quantity float64, unitID uuid.UUID) *InventoryItemCreatedEvent {
	eventData := map[string]interface{}{
		"pantry_id":          pantryID,
		"product_variant_id": productVariantID,
		"quantity":           quantity,
		"unit_of_measure_id": unitID,
	}

	return &InventoryItemCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.created", itemID, eventData),
	}
}

// InventoryItemQuantityUpdatedEvent is raised when quantity is updated
type InventoryItemQuantityUpdatedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemQuantityUpdatedEvent creates a new quantity updated event
func NewInventoryItemQuantityUpdatedEvent(itemID uuid.UUID, oldQuantity, newQuantity float64, unitID uuid.UUID) *InventoryItemQuantityUpdatedEvent {
	eventData := map[string]interface{}{
		"old_quantity":       oldQuantity,
		"new_quantity":       newQuantity,
		"unit_of_measure_id": unitID,
	}

	return &InventoryItemQuantityUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.quantity_updated", itemID, eventData),
	}
}

// InventoryItemLocationUpdatedEvent is raised when location is updated
type InventoryItemLocationUpdatedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemLocationUpdatedEvent creates a new location updated event
func NewInventoryItemLocationUpdatedEvent(itemID uuid.UUID, oldLocationID, newLocationID *uuid.UUID) *InventoryItemLocationUpdatedEvent {
	eventData := map[string]interface{}{}

	if oldLocationID != nil {
		eventData["old_location_id"] = *oldLocationID
	}

	if newLocationID != nil {
		eventData["new_location_id"] = *newLocationID
	}

	return &InventoryItemLocationUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.location_updated", itemID, eventData),
	}
}

// InventoryItemExpirationUpdatedEvent is raised when expiration date is updated
type InventoryItemExpirationUpdatedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemExpirationUpdatedEvent creates a new expiration updated event
func NewInventoryItemExpirationUpdatedEvent(itemID uuid.UUID, expirationDate *time.Time) *InventoryItemExpirationUpdatedEvent {
	eventData := map[string]interface{}{}

	if expirationDate != nil {
		eventData["expiration_date"] = *expirationDate
	}

	return &InventoryItemExpirationUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.expiration_updated", itemID, eventData),
	}
}

// InventoryItemUpdatedEvent is raised when item details are updated
type InventoryItemUpdatedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemUpdatedEvent creates a new item updated event
func NewInventoryItemUpdatedEvent(itemID uuid.UUID, oldQuantity, newQuantity float64, oldLocationID, newLocationID *uuid.UUID) *InventoryItemUpdatedEvent {
	eventData := map[string]interface{}{
		"old_quantity": oldQuantity,
		"new_quantity": newQuantity,
	}

	if oldLocationID != nil {
		eventData["old_location_id"] = *oldLocationID
	}

	if newLocationID != nil {
		eventData["new_location_id"] = *newLocationID
	}

	return &InventoryItemUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.updated", itemID, eventData),
	}
}

// InventoryItemConsumedEvent is raised when quantity is consumed
type InventoryItemConsumedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemConsumedEvent creates a new item consumed event
func NewInventoryItemConsumedEvent(itemID uuid.UUID, consumedQuantity, oldQuantity, newQuantity float64, unitID uuid.UUID) *InventoryItemConsumedEvent {
	eventData := map[string]interface{}{
		"consumed_quantity":  consumedQuantity,
		"old_quantity":       oldQuantity,
		"new_quantity":       newQuantity,
		"unit_of_measure_id": unitID,
	}

	return &InventoryItemConsumedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.consumed", itemID, eventData),
	}
}

// InventoryItemRestockedEvent is raised when quantity is added
type InventoryItemRestockedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemRestockedEvent creates a new item restocked event
func NewInventoryItemRestockedEvent(itemID uuid.UUID, addedQuantity, oldQuantity, newQuantity float64, unitID uuid.UUID) *InventoryItemRestockedEvent {
	eventData := map[string]interface{}{
		"added_quantity":     addedQuantity,
		"old_quantity":       oldQuantity,
		"new_quantity":       newQuantity,
		"unit_of_measure_id": unitID,
	}

	return &InventoryItemRestockedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.restocked", itemID, eventData),
	}
}

// InventoryItemDeletedEvent is raised when an item is deleted
type InventoryItemDeletedEvent struct {
	*BaseDomainEvent
}

// NewInventoryItemDeletedEvent creates a new item deleted event
func NewInventoryItemDeletedEvent(itemID, pantryID, productVariantID uuid.UUID) *InventoryItemDeletedEvent {
	eventData := map[string]interface{}{
		"pantry_id":          pantryID,
		"product_variant_id": productVariantID,
	}

	return &InventoryItemDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("inventory_item.deleted", itemID, eventData),
	}
}
