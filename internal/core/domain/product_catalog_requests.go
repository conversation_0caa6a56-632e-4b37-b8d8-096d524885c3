package domain

import "github.com/google/uuid"

// Category Request/Response DTOs

// CreateCategoryRequest represents a request to create a new category
type CreateCategoryRequest struct {
	Name             string     `json:"name" validate:"required,min=1,max=100"`
	Description      *string    `json:"description,omitempty" validate:"omitempty,max=500"`
	ParentCategoryID *uuid.UUID `json:"parent_category_id,omitempty"`
}

// UpdateCategoryRequest represents a request to update a category
type UpdateCategoryRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=500"`
}

// MoveCategoryRequest represents a request to move a category to a different parent
type MoveCategoryRequest struct {
	ParentCategoryID *uuid.UUID `json:"parent_category_id,omitempty"`
}

// CategoryResponse represents a category in API responses
type CategoryResponse struct {
	ID               uuid.UUID `json:"id"`
	Name             string    `json:"name"`
	Description      *string   `json:"description,omitempty"`
	ParentCategoryID *uuid.UUID `json:"parent_category_id,omitempty"`
	CreatedAt        string    `json:"created_at"`
	UpdatedAt        string    `json:"updated_at"`
	
	// Optional nested data
	ParentCategory *CategoryResponse   `json:"parent_category,omitempty"`
	SubCategories  []CategoryResponse  `json:"sub_categories,omitempty"`
	ProductCount   *int64              `json:"product_count,omitempty"`
}

// Unit of Measure Request/Response DTOs

// CreateUnitOfMeasureRequest represents a request to create a new unit
type CreateUnitOfMeasureRequest struct {
	Name        string            `json:"name" validate:"required,min=1,max=100"`
	Symbol      string            `json:"symbol" validate:"required,min=1,max=20"`
	Type        UnitOfMeasureType `json:"type" validate:"required,oneof=volume weight count length area other"`
	Description *string           `json:"description,omitempty" validate:"omitempty,max=500"`
}

// CreateDerivedUnitRequest represents a request to create a derived unit
type CreateDerivedUnitRequest struct {
	Name             string            `json:"name" validate:"required,min=1,max=100"`
	Symbol           string            `json:"symbol" validate:"required,min=1,max=20"`
	Type             UnitOfMeasureType `json:"type" validate:"required,oneof=volume weight count length area other"`
	Description      *string           `json:"description,omitempty" validate:"omitempty,max=500"`
	BaseUnitID       uuid.UUID         `json:"base_unit_id" validate:"required"`
	ConversionFactor float64           `json:"conversion_factor" validate:"required,gt=0"`
}

// UpdateUnitOfMeasureRequest represents a request to update a unit
type UpdateUnitOfMeasureRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Symbol      string  `json:"symbol" validate:"required,min=1,max=20"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=500"`
}

// UpdateConversionFactorRequest represents a request to update conversion factor
type UpdateConversionFactorRequest struct {
	ConversionFactor float64 `json:"conversion_factor" validate:"required,gt=0"`
}

// UnitOfMeasureResponse represents a unit of measure in API responses
type UnitOfMeasureResponse struct {
	ID               uuid.UUID         `json:"id"`
	Name             string            `json:"name"`
	Symbol           string            `json:"symbol"`
	Type             UnitOfMeasureType `json:"type"`
	Description      *string           `json:"description,omitempty"`
	IsBaseUnit       bool              `json:"is_base_unit"`
	BaseUnitID       *uuid.UUID        `json:"base_unit_id,omitempty"`
	ConversionFactor *float64          `json:"conversion_factor,omitempty"`
	CreatedAt        string            `json:"created_at"`
	UpdatedAt        string            `json:"updated_at"`
	
	// Optional nested data
	BaseUnit     *UnitOfMeasureResponse   `json:"base_unit,omitempty"`
	DerivedUnits []UnitOfMeasureResponse  `json:"derived_units,omitempty"`
}

// Product Request/Response DTOs

// CreateProductRequest represents a request to create a new product
type CreateProductRequest struct {
	Name        string     `json:"name" validate:"required,min=1,max=255"`
	Description *string    `json:"description,omitempty" validate:"omitempty,max=1000"`
	CategoryID  uuid.UUID  `json:"category_id" validate:"required"`
	Brand       *string    `json:"brand,omitempty" validate:"omitempty,max=100"`
}

// UpdateProductRequest represents a request to update a product
type UpdateProductRequest struct {
	Name        string     `json:"name" validate:"required,min=1,max=255"`
	Description *string    `json:"description,omitempty" validate:"omitempty,max=1000"`
	CategoryID  uuid.UUID  `json:"category_id" validate:"required"`
	Brand       *string    `json:"brand,omitempty" validate:"omitempty,max=100"`
}

// ProductResponse represents a product in API responses
type ProductResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description *string   `json:"description,omitempty"`
	CategoryID  uuid.UUID `json:"category_id"`
	Brand       *string   `json:"brand,omitempty"`
	CreatedAt   string    `json:"created_at"`
	UpdatedAt   string    `json:"updated_at"`
	
	// Optional nested data
	Category     *CategoryResponse         `json:"category,omitempty"`
	Variants     []ProductVariantResponse  `json:"variants,omitempty"`
	VariantCount *int64                    `json:"variant_count,omitempty"`
}

// Product Variant Request/Response DTOs

// CreateProductVariantRequest represents a request to create a new product variant
type CreateProductVariantRequest struct {
	Name                   string                       `json:"name" validate:"required,min=1,max=255"`
	Description            *string                      `json:"description,omitempty" validate:"omitempty,max=1000"`
	BarcodeGTIN            *string                      `json:"barcode_gtin,omitempty" validate:"omitempty,min=8,max=14"`
	ImageURL               *string                      `json:"image_url,omitempty" validate:"omitempty,url,max=500"`
	PackagingType          ProductVariantPackagingType `json:"packaging_type" validate:"required,oneof=single bulk multi-pack other"`
	DefaultUnitOfMeasureID *uuid.UUID                   `json:"default_unit_of_measure_id,omitempty"`
}

// UpdateProductVariantRequest represents a request to update a product variant
type UpdateProductVariantRequest struct {
	Name                   string                       `json:"name" validate:"required,min=1,max=255"`
	Description            *string                      `json:"description,omitempty" validate:"omitempty,max=1000"`
	ImageURL               *string                      `json:"image_url,omitempty" validate:"omitempty,url,max=500"`
	PackagingType          ProductVariantPackagingType `json:"packaging_type" validate:"required,oneof=single bulk multi-pack other"`
	DefaultUnitOfMeasureID *uuid.UUID                   `json:"default_unit_of_measure_id,omitempty"`
}

// UpdateVariantBarcodeRequest represents a request to update a variant's barcode
type UpdateVariantBarcodeRequest struct {
	BarcodeGTIN *string `json:"barcode_gtin,omitempty" validate:"omitempty,min=8,max=14"`
}

// ProductVariantResponse represents a product variant in API responses
type ProductVariantResponse struct {
	ID                     uuid.UUID                    `json:"id"`
	ProductID              uuid.UUID                    `json:"product_id"`
	Name                   string                       `json:"name"`
	Description            *string                      `json:"description,omitempty"`
	BarcodeGTIN            *string                      `json:"barcode_gtin,omitempty"`
	ImageURL               *string                      `json:"image_url,omitempty"`
	PackagingType          ProductVariantPackagingType `json:"packaging_type"`
	DefaultUnitOfMeasureID *uuid.UUID                   `json:"default_unit_of_measure_id,omitempty"`
	ShelfLifeDays          *int                         `json:"shelf_life_days,omitempty"`
	CreatedAt              string                       `json:"created_at"`
	UpdatedAt              string                       `json:"updated_at"`
	
	// Optional nested data
	Product              *ProductResponse         `json:"product,omitempty"`
	DefaultUnitOfMeasure *UnitOfMeasureResponse   `json:"default_unit_of_measure,omitempty"`
}

// Search and Filter DTOs

// ProductSearchRequest represents a product search request
type ProductSearchRequest struct {
	Query      string     `json:"query,omitempty"`
	CategoryID *uuid.UUID `json:"category_id,omitempty"`
	Brand      *string    `json:"brand,omitempty"`
	Page       int        `json:"page,omitempty" validate:"omitempty,min=1"`
	Limit      int        `json:"limit,omitempty" validate:"omitempty,min=1,max=100"`
}

// ProductVariantSearchRequest represents a product variant search request
type ProductVariantSearchRequest struct {
	Query     string     `json:"query,omitempty"`
	ProductID *uuid.UUID `json:"product_id,omitempty"`
	Page      int        `json:"page,omitempty" validate:"omitempty,min=1"`
	Limit     int        `json:"limit,omitempty" validate:"omitempty,min=1,max=100"`
}

// BarcodeSearchRequest represents a barcode search request
type BarcodeSearchRequest struct {
	BarcodeGTIN string `json:"barcode_gtin" validate:"required,min=8,max=14"`
}

// Unit Conversion DTOs

// ConvertQuantityRequest represents a unit conversion request
type ConvertQuantityRequest struct {
	Quantity   float64   `json:"quantity" validate:"required,gt=0"`
	FromUnitID uuid.UUID `json:"from_unit_id" validate:"required"`
	ToUnitID   uuid.UUID `json:"to_unit_id" validate:"required"`
}

// ConvertQuantityResponse represents a unit conversion response
type ConvertQuantityResponse struct {
	OriginalQuantity float64                `json:"original_quantity"`
	ConvertedQuantity float64               `json:"converted_quantity"`
	FromUnit         UnitOfMeasureResponse  `json:"from_unit"`
	ToUnit           UnitOfMeasureResponse  `json:"to_unit"`
}

// Category Tree DTOs

// CategoryTreeResponse represents a hierarchical category tree
type CategoryTreeResponse struct {
	ID           uuid.UUID              `json:"id"`
	Name         string                 `json:"name"`
	Description  *string                `json:"description,omitempty"`
	ProductCount int64                  `json:"product_count"`
	Children     []CategoryTreeResponse `json:"children,omitempty"`
}
