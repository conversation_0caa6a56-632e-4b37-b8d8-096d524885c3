package domain

import (
	"testing"
	"time"

	"github.com/google/uuid"
)

func TestNewUser(t *testing.T) {
	username := "testuser"
	email := "<EMAIL>"
	passwordHash := "hashedpassword123"
	
	user := NewUser(username, email, passwordHash)
	
	// Test basic fields
	if user.Username != username {
		t.<PERSON><PERSON><PERSON>("Expected username %s, got %s", username, user.Username)
	}
	
	if user.Email != email {
		t.Errorf("Expected email %s, got %s", email, user.Email)
	}
	
	if user.PasswordHash != passwordHash {
		t.<PERSON>rrorf("Expected password hash %s, got %s", passwordHash, user.PasswordHash)
	}
	
	// Test generated fields
	if user.ID == uuid.Nil {
		t.<PERSON>r("Expected non-nil UUID")
	}
	
	if user.CreatedAt.IsZero() {
		t.<PERSON>rror("Expected non-zero CreatedAt")
	}
	
	if user.UpdatedAt.IsZero() {
		t.<PERSON>r("Expected non-zero UpdatedAt")
	}
	
	// Test events
	events := user.GetEvents()
	if len(events) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 event, got %d", len(events))
	}
	
	if events[0].GetEventType() != "user.registered" {
		t.Errorf("Expected event type 'user.registered', got %s", events[0].GetEventType())
	}
}

func TestUserUpdateProfile(t *testing.T) {
	user := NewUser("testuser", "<EMAIL>", "hash")
	user.ClearEvents() // Clear registration event
	
	firstName := "John"
	lastName := "Doe"
	profileURL := "https://example.com/profile.jpg"
	
	originalUpdatedAt := user.UpdatedAt
	time.Sleep(1 * time.Millisecond) // Ensure time difference
	
	user.UpdateProfile(&firstName, &lastName, &profileURL)
	
	// Test updated fields
	if user.FirstName == nil || *user.FirstName != firstName {
		t.Errorf("Expected first name %s, got %v", firstName, user.FirstName)
	}
	
	if user.LastName == nil || *user.LastName != lastName {
		t.Errorf("Expected last name %s, got %v", lastName, user.LastName)
	}
	
	if user.ProfilePictureURL == nil || *user.ProfilePictureURL != profileURL {
		t.Errorf("Expected profile URL %s, got %v", profileURL, user.ProfilePictureURL)
	}
	
	// Test updated timestamp
	if !user.UpdatedAt.After(originalUpdatedAt) {
		t.Error("Expected UpdatedAt to be updated")
	}
	
	// Test events
	events := user.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}
	
	if events[0].GetEventType() != "user.profile_updated" {
		t.Errorf("Expected event type 'user.profile_updated', got %s", events[0].GetEventType())
	}
}

func TestUserSoftDelete(t *testing.T) {
	user := NewUser("testuser", "<EMAIL>", "hash")
	
	if user.IsDeleted() {
		t.Error("Expected user to not be deleted initially")
	}
	
	user.SoftDelete()
	
	if !user.IsDeleted() {
		t.Error("Expected user to be deleted after SoftDelete")
	}
	
	if user.DeletedAt == nil {
		t.Error("Expected DeletedAt to be set")
	}
}

func TestUserGetFullName(t *testing.T) {
	user := NewUser("testuser", "<EMAIL>", "hash")
	
	// Test with no names
	if user.GetFullName() != "testuser" {
		t.Errorf("Expected full name 'testuser', got %s", user.GetFullName())
	}
	
	// Test with first name only
	firstName := "John"
	user.FirstName = &firstName
	if user.GetFullName() != "John" {
		t.Errorf("Expected full name 'John', got %s", user.GetFullName())
	}
	
	// Test with both names
	lastName := "Doe"
	user.LastName = &lastName
	if user.GetFullName() != "John Doe" {
		t.Errorf("Expected full name 'John Doe', got %s", user.GetFullName())
	}
	
	// Test with last name only
	user.FirstName = nil
	if user.GetFullName() != "Doe" {
		t.Errorf("Expected full name 'Doe', got %s", user.GetFullName())
	}
}

func TestRefreshToken(t *testing.T) {
	userID := uuid.New()
	tokenHash := "hashedtoken123"
	expiresAt := time.Now().Add(24 * time.Hour)
	
	token := NewRefreshToken(userID, tokenHash, expiresAt)
	
	// Test basic fields
	if token.UserID != userID {
		t.Errorf("Expected user ID %s, got %s", userID, token.UserID)
	}
	
	if token.TokenHash != tokenHash {
		t.Errorf("Expected token hash %s, got %s", tokenHash, token.TokenHash)
	}
	
	if !token.ExpiresAt.Equal(expiresAt) {
		t.Errorf("Expected expires at %v, got %v", expiresAt, token.ExpiresAt)
	}
	
	// Test generated fields
	if token.ID == uuid.Nil {
		t.Error("Expected non-nil UUID")
	}
	
	if token.IsRevoked {
		t.Error("Expected token to not be revoked initially")
	}
	
	// Test validity
	if !token.IsValid() {
		t.Error("Expected token to be valid")
	}
	
	// Test revocation
	token.Revoke()
	if !token.IsRevoked {
		t.Error("Expected token to be revoked")
	}
	
	if token.IsValid() {
		t.Error("Expected revoked token to be invalid")
	}
}

func TestRefreshTokenExpiration(t *testing.T) {
	userID := uuid.New()
	tokenHash := "hashedtoken123"
	expiresAt := time.Now().Add(-1 * time.Hour) // Expired
	
	token := NewRefreshToken(userID, tokenHash, expiresAt)
	
	if !token.IsExpired() {
		t.Error("Expected token to be expired")
	}
	
	if token.IsValid() {
		t.Error("Expected expired token to be invalid")
	}
}
