package domain

import (
	"testing"
	"time"

	"github.com/google/uuid"
)

func TestNewPantry(t *testing.T) {
	name := "My Kitchen Pantry"
	description := "Main kitchen storage"
	ownerUserID := uuid.New()

	pantry := NewPantry(name, &description, ownerUserID)

	// Test basic fields
	if pantry.Name != name {
		t.<PERSON>rf("Expected name %s, got %s", name, pantry.Name)
	}

	if pantry.Description == nil || *pantry.Description != description {
		t.<PERSON>rrorf("Expected description %s, got %v", description, pantry.Description)
	}

	if pantry.OwnerUserID != ownerUserID {
		t.Errorf("Expected owner user ID %s, got %s", ownerUserID, pantry.OwnerUserID)
	}

	// Test generated fields
	if pantry.ID == uuid.Nil {
		t.Error("Expected non-nil UUID")
	}

	if pantry.CreatedAt.IsZero() {
		t.Error("Expected non-zero CreatedAt")
	}

	if pantry.UpdatedAt.IsZero() {
		t.<PERSON>rror("Expected non-zero UpdatedAt")
	}

	// Test events
	events := pantry.GetEvents()
	if len(events) != 1 {
		t.<PERSON><PERSON>rf("Expected 1 event, got %d", len(events))
	}

	if events[0].GetEventType() != "pantry.created" {
		t.Errorf("Expected event type 'pantry.created', got %s", events[0].GetEventType())
	}
}

func TestPantryUpdateDetails(t *testing.T) {
	pantry := NewPantry("Original Name", nil, uuid.New())
	pantry.ClearEvents() // Clear creation event

	newName := "Updated Name"
	newDescription := "Updated description"

	originalUpdatedAt := pantry.UpdatedAt
	time.Sleep(1 * time.Millisecond) // Ensure time difference

	pantry.UpdateDetails(newName, &newDescription)

	// Test updated fields
	if pantry.Name != newName {
		t.Errorf("Expected name %s, got %s", newName, pantry.Name)
	}

	if pantry.Description == nil || *pantry.Description != newDescription {
		t.Errorf("Expected description %s, got %v", newDescription, pantry.Description)
	}

	// Test updated timestamp
	if !pantry.UpdatedAt.After(originalUpdatedAt) {
		t.Error("Expected UpdatedAt to be updated")
	}

	// Test events
	events := pantry.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}

	if events[0].GetEventType() != "pantry.updated" {
		t.Errorf("Expected event type 'pantry.updated', got %s", events[0].GetEventType())
	}
}

func TestPantryTransferOwnership(t *testing.T) {
	oldOwnerID := uuid.New()
	newOwnerID := uuid.New()
	pantry := NewPantry("Test Pantry", nil, oldOwnerID)
	pantry.ClearEvents() // Clear creation event

	pantry.TransferOwnership(newOwnerID)

	// Test ownership transfer
	if pantry.OwnerUserID != newOwnerID {
		t.Errorf("Expected new owner %s, got %s", newOwnerID, pantry.OwnerUserID)
	}

	// Test events
	events := pantry.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}

	if events[0].GetEventType() != "pantry.ownership_transferred" {
		t.Errorf("Expected event type 'pantry.ownership_transferred', got %s", events[0].GetEventType())
	}
}

func TestPantrySoftDelete(t *testing.T) {
	pantry := NewPantry("Test Pantry", nil, uuid.New())

	if pantry.IsDeleted() {
		t.Error("Expected pantry to not be deleted initially")
	}

	pantry.SoftDelete()

	if !pantry.IsDeleted() {
		t.Error("Expected pantry to be deleted after SoftDelete")
	}

	if pantry.DeletedAt == nil {
		t.Error("Expected DeletedAt to be set")
	}
}

func TestNewPantryMembership(t *testing.T) {
	pantryID := uuid.New()
	userID := uuid.New()
	inviterID := uuid.New()
	role := PantryRoleEditor

	membership := NewPantryMembership(pantryID, userID, role, &inviterID)

	// Test basic fields
	if membership.PantryID != pantryID {
		t.Errorf("Expected pantry ID %s, got %s", pantryID, membership.PantryID)
	}

	if membership.UserID != userID {
		t.Errorf("Expected user ID %s, got %s", userID, membership.UserID)
	}

	if membership.Role != role {
		t.Errorf("Expected role %s, got %s", role, membership.Role)
	}

	if membership.Status != PantryMembershipStatusPendingInvitation {
		t.Errorf("Expected status %s, got %s", PantryMembershipStatusPendingInvitation, membership.Status)
	}

	if membership.InvitedByUserID == nil || *membership.InvitedByUserID != inviterID {
		t.Errorf("Expected inviter ID %s, got %v", inviterID, membership.InvitedByUserID)
	}

	// Test generated fields
	if membership.ID == uuid.Nil {
		t.Error("Expected non-nil UUID")
	}

	if membership.JoinedAt.IsZero() {
		t.Error("Expected non-zero JoinedAt")
	}

	// Test status checks
	if membership.IsActive() {
		t.Error("Expected membership to not be active initially")
	}

	if !membership.IsPending() {
		t.Error("Expected membership to be pending initially")
	}
}

func TestPantryMembershipAcceptInvitation(t *testing.T) {
	membership := NewPantryMembership(uuid.New(), uuid.New(), PantryRoleEditor, nil)
	membership.ClearEvents() // Clear invitation event

	err := membership.AcceptInvitation()
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if membership.Status != PantryMembershipStatusActive {
		t.Errorf("Expected status %s, got %s", PantryMembershipStatusActive, membership.Status)
	}

	if !membership.IsActive() {
		t.Error("Expected membership to be active after acceptance")
	}

	// Test events
	events := membership.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}

	if events[0].GetEventType() != "pantry.member_joined" {
		t.Errorf("Expected event type 'pantry.member_joined', got %s", events[0].GetEventType())
	}

	// Test cannot accept again
	err = membership.AcceptInvitation()
	if err == nil {
		t.Error("Expected error when accepting already accepted invitation")
	}
}

func TestPantryMembershipRejectInvitation(t *testing.T) {
	membership := NewPantryMembership(uuid.New(), uuid.New(), PantryRoleEditor, nil)
	membership.ClearEvents() // Clear invitation event

	err := membership.RejectInvitation()
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if membership.Status != PantryMembershipStatusRemoved {
		t.Errorf("Expected status %s, got %s", PantryMembershipStatusRemoved, membership.Status)
	}

	if membership.IsActive() {
		t.Error("Expected membership to not be active after rejection")
	}

	// Test events
	events := membership.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}

	if events[0].GetEventType() != "pantry.member_removed" {
		t.Errorf("Expected event type 'pantry.member_removed', got %s", events[0].GetEventType())
	}
}

func TestPantryMembershipUpdateRole(t *testing.T) {
	membership := NewPantryMembership(uuid.New(), uuid.New(), PantryRoleEditor, nil)
	membership.ClearEvents() // Clear invitation event

	newRole := PantryRoleAdmin
	membership.UpdateRole(newRole)

	if membership.Role != newRole {
		t.Errorf("Expected role %s, got %s", newRole, membership.Role)
	}

	// Test events
	events := membership.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}

	if events[0].GetEventType() != "pantry.member_role_updated" {
		t.Errorf("Expected event type 'pantry.member_role_updated', got %s", events[0].GetEventType())
	}
}

func TestNewPantryLocation(t *testing.T) {
	pantryID := uuid.New()
	name := "Refrigerator"
	description := "Main refrigerator"

	location := NewPantryLocation(pantryID, name, &description)

	// Test basic fields
	if location.PantryID != pantryID {
		t.Errorf("Expected pantry ID %s, got %s", pantryID, location.PantryID)
	}

	if location.Name != name {
		t.Errorf("Expected name %s, got %s", name, location.Name)
	}

	if location.Description == nil || *location.Description != description {
		t.Errorf("Expected description %s, got %v", description, location.Description)
	}

	// Test generated fields
	if location.ID == uuid.Nil {
		t.Error("Expected non-nil UUID")
	}

	if location.CreatedAt.IsZero() {
		t.Error("Expected non-zero CreatedAt")
	}

	if location.UpdatedAt.IsZero() {
		t.Error("Expected non-zero UpdatedAt")
	}

	// Test events
	events := location.GetEvents()
	if len(events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(events))
	}

	if events[0].GetEventType() != "pantry.location_created" {
		t.Errorf("Expected event type 'pantry.location_created', got %s", events[0].GetEventType())
	}
}
