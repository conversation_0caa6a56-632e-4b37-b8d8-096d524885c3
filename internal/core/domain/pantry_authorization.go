package domain

import (
	"github.com/google/uuid"
)

// PantryPermission represents a permission that can be checked
type PantryPermission string

const (
	// Pantry-level permissions
	PermissionViewPantry         PantryPermission = "view_pantry"
	PermissionEditPantry         PantryPermission = "edit_pantry"
	PermissionDeletePantry       PantryPermission = "delete_pantry"
	PermissionTransferOwnership  PantryPermission = "transfer_ownership"
	
	// Member management permissions
	PermissionInviteMembers      PantryPermission = "invite_members"
	PermissionRemoveMembers      PantryPermission = "remove_members"
	PermissionUpdateMemberRoles  PantryPermission = "update_member_roles"
	PermissionViewMembers        PantryPermission = "view_members"
	
	// Location management permissions
	PermissionCreateLocations    PantryPermission = "create_locations"
	PermissionEditLocations      PantryPermission = "edit_locations"
	PermissionDeleteLocations    PantryPermission = "delete_locations"
	PermissionViewLocations      PantryPermission = "view_locations"
	
	// Inventory permissions (for future use)
	PermissionCreateItems        PantryPermission = "create_items"
	PermissionEditItems          PantryPermission = "edit_items"
	PermissionDeleteItems        PantryPermission = "delete_items"
	PermissionViewItems          PantryPermission = "view_items"
	
	// Shopping list permissions (for future use)
	PermissionCreateShoppingLists PantryPermission = "create_shopping_lists"
	PermissionEditShoppingLists   PantryPermission = "edit_shopping_lists"
	PermissionDeleteShoppingLists PantryPermission = "delete_shopping_lists"
	PermissionViewShoppingLists   PantryPermission = "view_shopping_lists"
)

// PantryAuthorizationService defines the interface for pantry authorization
type PantryAuthorizationService interface {
	// CheckPermission checks if a user has a specific permission for a pantry
	CheckPermission(userID, pantryID uuid.UUID, permission PantryPermission) (bool, error)
	
	// GetUserRole gets the user's role in a pantry
	GetUserRole(userID, pantryID uuid.UUID) (*PantryRole, error)
	
	// IsOwner checks if a user is the owner of a pantry
	IsOwner(userID, pantryID uuid.UUID) (bool, error)
	
	// IsMember checks if a user is an active member of a pantry
	IsMember(userID, pantryID uuid.UUID) (bool, error)
	
	// CanInviteWithRole checks if a user can invite someone with a specific role
	CanInviteWithRole(userID, pantryID uuid.UUID, targetRole PantryRole) (bool, error)
	
	// CanUpdateMemberRole checks if a user can update another member's role
	CanUpdateMemberRole(userID, pantryID, targetUserID uuid.UUID, newRole PantryRole) (bool, error)
	
	// CanRemoveMember checks if a user can remove another member
	CanRemoveMember(userID, pantryID, targetUserID uuid.UUID) (bool, error)
}

// GetRolePermissions returns the permissions for a given role
func GetRolePermissions(role PantryRole) []PantryPermission {
	switch role {
	case PantryRoleOwner:
		return []PantryPermission{
			// All permissions
			PermissionViewPantry,
			PermissionEditPantry,
			PermissionDeletePantry,
			PermissionTransferOwnership,
			PermissionInviteMembers,
			PermissionRemoveMembers,
			PermissionUpdateMemberRoles,
			PermissionViewMembers,
			PermissionCreateLocations,
			PermissionEditLocations,
			PermissionDeleteLocations,
			PermissionViewLocations,
			PermissionCreateItems,
			PermissionEditItems,
			PermissionDeleteItems,
			PermissionViewItems,
			PermissionCreateShoppingLists,
			PermissionEditShoppingLists,
			PermissionDeleteShoppingLists,
			PermissionViewShoppingLists,
		}
	case PantryRoleAdmin:
		return []PantryPermission{
			// Most permissions except ownership transfer and pantry deletion
			PermissionViewPantry,
			PermissionEditPantry,
			PermissionInviteMembers,
			PermissionRemoveMembers,
			PermissionUpdateMemberRoles,
			PermissionViewMembers,
			PermissionCreateLocations,
			PermissionEditLocations,
			PermissionDeleteLocations,
			PermissionViewLocations,
			PermissionCreateItems,
			PermissionEditItems,
			PermissionDeleteItems,
			PermissionViewItems,
			PermissionCreateShoppingLists,
			PermissionEditShoppingLists,
			PermissionDeleteShoppingLists,
			PermissionViewShoppingLists,
		}
	case PantryRoleEditor:
		return []PantryPermission{
			// Content management permissions
			PermissionViewPantry,
			PermissionViewMembers,
			PermissionCreateLocations,
			PermissionEditLocations,
			PermissionViewLocations,
			PermissionCreateItems,
			PermissionEditItems,
			PermissionDeleteItems,
			PermissionViewItems,
			PermissionCreateShoppingLists,
			PermissionEditShoppingLists,
			PermissionDeleteShoppingLists,
			PermissionViewShoppingLists,
		}
	case PantryRoleViewer:
		return []PantryPermission{
			// Read-only permissions
			PermissionViewPantry,
			PermissionViewMembers,
			PermissionViewLocations,
			PermissionViewItems,
			PermissionViewShoppingLists,
		}
	default:
		return []PantryPermission{}
	}
}

// HasPermission checks if a role has a specific permission
func HasPermission(role PantryRole, permission PantryPermission) bool {
	permissions := GetRolePermissions(role)
	for _, p := range permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// CanManageRole checks if a role can manage (invite/update/remove) another role
func CanManageRole(managerRole, targetRole PantryRole) bool {
	// Owner can manage all roles
	if managerRole == PantryRoleOwner {
		return true
	}
	
	// Admin can manage editor and viewer roles
	if managerRole == PantryRoleAdmin {
		return targetRole == PantryRoleEditor || targetRole == PantryRoleViewer
	}
	
	// Editor and viewer cannot manage other roles
	return false
}

// IsValidRoleTransition checks if a role transition is valid
func IsValidRoleTransition(fromRole, toRole PantryRole) bool {
	// Owner role can only be transferred, not assigned
	if toRole == PantryRoleOwner {
		return false
	}
	
	// All other transitions are valid
	return true
}

// GetRoleHierarchyLevel returns the hierarchy level of a role (higher number = more permissions)
func GetRoleHierarchyLevel(role PantryRole) int {
	switch role {
	case PantryRoleOwner:
		return 4
	case PantryRoleAdmin:
		return 3
	case PantryRoleEditor:
		return 2
	case PantryRoleViewer:
		return 1
	default:
		return 0
	}
}
