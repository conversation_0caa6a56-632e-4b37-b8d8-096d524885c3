package domain

import "github.com/google/uuid"

// CreatePantryRequest represents a request to create a new pantry
type CreatePantryRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=500"`
}

// UpdatePantryRequest represents a request to update a pantry
type UpdatePantryRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=500"`
}

// TransferOwnershipRequest represents a request to transfer pantry ownership
type TransferOwnershipRequest struct {
	NewOwnerUserID uuid.UUID `json:"new_owner_user_id" validate:"required"`
}

// InviteMemberRequest represents a request to invite a member to a pantry
type InviteMemberRequest struct {
	Email string     `json:"email" validate:"required,email"`
	Role  PantryRole `json:"role" validate:"required,oneof=admin editor viewer"`
}

// UpdateMemberRoleRequest represents a request to update a member's role
type UpdateMemberRoleRequest struct {
	Role PantryRole `json:"role" validate:"required,oneof=admin editor viewer"`
}

// AcceptInvitationRequest represents a request to accept a pantry invitation
type AcceptInvitationRequest struct {
	MembershipID uuid.UUID `json:"membership_id" validate:"required"`
}

// RejectInvitationRequest represents a request to reject a pantry invitation
type RejectInvitationRequest struct {
	MembershipID uuid.UUID `json:"membership_id" validate:"required"`
}

// CreateLocationRequest represents a request to create a pantry location
type CreateLocationRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=500"`
}

// UpdateLocationRequest represents a request to update a pantry location
type UpdateLocationRequest struct {
	Name        string  `json:"name" validate:"required,min=1,max=100"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=500"`
}

// PantryListFilters represents filters for listing pantries
type PantryListFilters struct {
	OwnerOnly bool   `json:"owner_only,omitempty"`
	Search    string `json:"search,omitempty"`
}

// MemberListFilters represents filters for listing pantry members
type MemberListFilters struct {
	Role   *PantryRole             `json:"role,omitempty"`
	Status *PantryMembershipStatus `json:"status,omitempty"`
}

// PantryResponse represents a pantry in API responses
type PantryResponse struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty"`
	OwnerUserID uuid.UUID  `json:"owner_user_id"`
	CreatedAt   string     `json:"created_at"`
	UpdatedAt   string     `json:"updated_at"`
	IsOwner     bool       `json:"is_owner"`
	UserRole    *string    `json:"user_role,omitempty"`
}

// PantryMemberResponse represents a pantry member in API responses
type PantryMemberResponse struct {
	ID              uuid.UUID              `json:"id"`
	PantryID        uuid.UUID              `json:"pantry_id"`
	UserID          uuid.UUID              `json:"user_id"`
	Role            PantryRole             `json:"role"`
	Status          PantryMembershipStatus `json:"status"`
	JoinedAt        string                 `json:"joined_at"`
	InvitedByUserID *uuid.UUID             `json:"invited_by_user_id,omitempty"`
	
	// User details (if populated)
	UserEmail     *string `json:"user_email,omitempty"`
	UserFirstName *string `json:"user_first_name,omitempty"`
	UserLastName  *string `json:"user_last_name,omitempty"`
}

// PantryLocationResponse represents a pantry location in API responses
type PantryLocationResponse struct {
	ID          uuid.UUID `json:"id"`
	PantryID    uuid.UUID `json:"pantry_id"`
	Name        string    `json:"name"`
	Description *string   `json:"description,omitempty"`
	CreatedAt   string    `json:"created_at"`
	UpdatedAt   string    `json:"updated_at"`
}

// PantryInvitationResponse represents a pantry invitation in API responses
type PantryInvitationResponse struct {
	ID              uuid.UUID              `json:"id"`
	PantryID        uuid.UUID              `json:"pantry_id"`
	Role            PantryRole             `json:"role"`
	Status          PantryMembershipStatus `json:"status"`
	JoinedAt        string                 `json:"joined_at"`
	InvitedByUserID *uuid.UUID             `json:"invited_by_user_id,omitempty"`
	
	// Pantry details
	PantryName        string    `json:"pantry_name"`
	PantryDescription *string   `json:"pantry_description,omitempty"`
	PantryOwnerUserID uuid.UUID `json:"pantry_owner_user_id"`
	
	// Inviter details (if available)
	InviterEmail     *string `json:"inviter_email,omitempty"`
	InviterFirstName *string `json:"inviter_first_name,omitempty"`
	InviterLastName  *string `json:"inviter_last_name,omitempty"`
}
