package domain_test

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// TestProductCatalogIntegration tests the complete product catalog workflow
func TestProductCatalogIntegration(t *testing.T) {
	// Test Category Creation and Hierarchy
	t.Run("Category Management", func(t *testing.T) {
		// Create root category
		rootCategory := domain.NewCategory("Food & Beverages", stringPtr("All food and beverage items"), nil)
		assert.NotEqual(t, uuid.Nil, rootCategory.ID)
		assert.Equal(t, "Food & Beverages", rootCategory.Name)
		assert.Nil(t, rootCategory.ParentCategoryID)
		assert.False(t, rootCategory.CreatedAt.IsZero())

		// Create subcategory
		subCategory := domain.NewCategory("Dairy & Eggs", stringPtr("Milk, cheese, yogurt, eggs"), &rootCategory.ID)
		assert.NotEqual(t, uuid.Nil, subCategory.ID)
		assert.Equal(t, "Dairy & Eggs", subCategory.Name)
		assert.NotNil(t, subCategory.ParentCategoryID)
		assert.Equal(t, rootCategory.ID, *subCategory.ParentCategoryID)

		// Update category
		originalUpdatedAt := rootCategory.UpdatedAt
		time.Sleep(1 * time.Millisecond) // Ensure time difference
		rootCategory.UpdateDetails("Food & Drinks", stringPtr("Updated description"))
		assert.Equal(t, "Food & Drinks", rootCategory.Name)
		assert.True(t, rootCategory.UpdatedAt.After(originalUpdatedAt))

		// Move category
		newParentID := uuid.New()
		subCategory.Move(&newParentID)
		assert.Equal(t, newParentID, *subCategory.ParentCategoryID)

		// Check events
		events := rootCategory.GetEvents()
		assert.Len(t, events, 2) // created + updated
		assert.Equal(t, "category.created", events[0].GetEventType())
		assert.Equal(t, "category.updated", events[1].GetEventType())
	})

	// Test Unit of Measure Creation and Conversion
	t.Run("Unit of Measure Management", func(t *testing.T) {
		// Create base unit
		baseUnit := domain.NewUnitOfMeasure("Kilogram", "kg", domain.UnitTypeWeight, stringPtr("Base unit for weight"))
		assert.NotEqual(t, uuid.Nil, baseUnit.ID)
		assert.Equal(t, "Kilogram", baseUnit.Name)
		assert.Equal(t, "kg", baseUnit.Symbol)
		assert.Equal(t, domain.UnitTypeWeight, baseUnit.Type)
		assert.True(t, baseUnit.IsBaseUnit)
		assert.Nil(t, baseUnit.BaseUnitID)
		assert.Nil(t, baseUnit.ConversionFactor)

		// Create derived unit
		derivedUnit := domain.NewDerivedUnitOfMeasure("Gram", "g", domain.UnitTypeWeight, stringPtr("Gram"), baseUnit.ID, 1000.0)
		assert.NotEqual(t, uuid.Nil, derivedUnit.ID)
		assert.Equal(t, "Gram", derivedUnit.Name)
		assert.Equal(t, "g", derivedUnit.Symbol)
		assert.False(t, derivedUnit.IsBaseUnit)
		assert.NotNil(t, derivedUnit.BaseUnitID)
		assert.Equal(t, baseUnit.ID, *derivedUnit.BaseUnitID)
		assert.NotNil(t, derivedUnit.ConversionFactor)
		assert.Equal(t, 1000.0, *derivedUnit.ConversionFactor)

		// Test unit conversion
		quantity := 500.0 // 500 grams
		baseQuantity, err := derivedUnit.ConvertToBaseUnit(quantity)
		require.NoError(t, err)
		assert.Equal(t, 0.5, baseQuantity) // 0.5 kg

		convertedBack, err := derivedUnit.ConvertFromBaseUnit(baseQuantity)
		require.NoError(t, err)
		assert.Equal(t, quantity, convertedBack) // Should be 500 grams again

		// Update unit details
		derivedUnit.UpdateDetails("Grams", "gm", stringPtr("Updated description"))
		assert.Equal(t, "Grams", derivedUnit.Name)
		assert.Equal(t, "gm", derivedUnit.Symbol)

		// Update conversion factor
		err = derivedUnit.UpdateConversionFactor(2000.0)
		require.NoError(t, err)
		assert.Equal(t, 2000.0, *derivedUnit.ConversionFactor)

		// Test error cases
		err = baseUnit.UpdateConversionFactor(100.0)
		assert.Error(t, err) // Cannot set conversion factor for base units

		// Check events
		events := derivedUnit.GetEvents()
		assert.Len(t, events, 3) // created + updated + conversion_updated
	})

	// Test Product Creation and Management
	t.Run("Product Management", func(t *testing.T) {
		// Create category for product
		categoryID := uuid.New()

		// Create product
		product := domain.NewProduct("Whole Milk", stringPtr("Fresh whole milk"), categoryID, stringPtr("Local Farm"))
		assert.NotEqual(t, uuid.Nil, product.ID)
		assert.Equal(t, "Whole Milk", product.Name)
		assert.Equal(t, categoryID, product.CategoryID)
		assert.NotNil(t, product.Brand)
		assert.Equal(t, "Local Farm", *product.Brand)

		// Update product
		newCategoryID := uuid.New()
		product.UpdateDetails("Organic Whole Milk", stringPtr("Organic fresh whole milk"), newCategoryID, stringPtr("Organic Farm"))
		assert.Equal(t, "Organic Whole Milk", product.Name)
		assert.Equal(t, newCategoryID, product.CategoryID)
		assert.Equal(t, "Organic Farm", *product.Brand)

		// Check events
		events := product.GetEvents()
		assert.Len(t, events, 2) // created + updated
		assert.Equal(t, "product.created", events[0].GetEventType())
		assert.Equal(t, "product.updated", events[1].GetEventType())
	})

	// Test Product Variant Creation and Management
	t.Run("Product Variant Management", func(t *testing.T) {
		// Create product for variant
		productID := uuid.New()
		unitID := uuid.New()

		// Create product variant
		variant := domain.NewProductVariant(
			productID,
			"Whole Milk 1 Gallon",
			stringPtr("1 gallon container of whole milk"),
			stringPtr("1234567890123"),
			stringPtr("https://example.com/milk.jpg"),
			domain.PackagingTypeSingle,
			&unitID,
			nil)

		assert.NotEqual(t, uuid.Nil, variant.ID)
		assert.Equal(t, productID, variant.ProductID)
		assert.Equal(t, "Whole Milk 1 Gallon", variant.Name)
		assert.NotNil(t, variant.BarcodeGTIN)
		assert.Equal(t, "1234567890123", *variant.BarcodeGTIN)
		assert.Equal(t, domain.PackagingTypeSingle, variant.PackagingType)
		assert.True(t, variant.HasBarcode())

		// Update variant
		variant.UpdateDetails(
			"Whole Milk 1 Gallon Jug",
			stringPtr("1 gallon jug of whole milk"),
			stringPtr("https://example.com/milk-jug.jpg"),
			domain.PackagingTypeBulk,
			&unitID,
		)
		assert.Equal(t, "Whole Milk 1 Gallon Jug", variant.Name)
		assert.Equal(t, domain.PackagingTypeBulk, variant.PackagingType)

		// Update barcode
		newBarcode := "9876543210987"
		variant.UpdateBarcode(&newBarcode)
		assert.Equal(t, newBarcode, *variant.BarcodeGTIN)

		// Remove barcode
		variant.UpdateBarcode(nil)
		assert.Nil(t, variant.BarcodeGTIN)
		assert.False(t, variant.HasBarcode())

		// Check events
		events := variant.GetEvents()
		assert.Len(t, events, 4) // created + updated + barcode_updated + barcode_updated
		assert.Equal(t, "product_variant.created", events[0].GetEventType())
		assert.Equal(t, "product_variant.updated", events[1].GetEventType())
		assert.Equal(t, "product_variant.barcode_updated", events[2].GetEventType())
		assert.Equal(t, "product_variant.barcode_updated", events[3].GetEventType())
	})

	// Test Barcode Validation
	t.Run("Barcode Validation", func(t *testing.T) {
		// Valid barcodes
		validBarcodes := []string{
			"12345678",       // 8 digits
			"123456789012",   // 12 digits
			"1234567890123",  // 13 digits
			"12345678901234", // 14 digits
		}

		for _, barcode := range validBarcodes {
			err := domain.ValidateBarcodeGTIN(barcode)
			assert.NoError(t, err, "Barcode %s should be valid", barcode)
		}

		// Invalid barcodes
		invalidBarcodes := []string{
			"1234567",         // Too short
			"123456789012345", // Too long
			"12345678a012",    // Contains letter
			"",                // Empty
			"12345 67890",     // Contains space
		}

		for _, barcode := range invalidBarcodes {
			err := domain.ValidateBarcodeGTIN(barcode)
			assert.Error(t, err, "Barcode %s should be invalid", barcode)
		}
	})

	// Test Complete Product Catalog Workflow
	t.Run("Complete Workflow", func(t *testing.T) {
		// 1. Create category hierarchy
		foodCategory := domain.NewCategory("Food", nil, nil)
		dairyCategory := domain.NewCategory("Dairy", nil, &foodCategory.ID)

		// 2. Create units
		literUnit := domain.NewUnitOfMeasure("Liter", "L", domain.UnitTypeVolume, nil)
		mlUnit := domain.NewDerivedUnitOfMeasure("Milliliter", "mL", domain.UnitTypeVolume, nil, literUnit.ID, 1000.0)

		// 3. Create product
		product := domain.NewProduct("Milk", stringPtr("Fresh milk"), dairyCategory.ID, stringPtr("Farm Fresh"))

		// 4. Create product variants
		variant1L := domain.NewProductVariant(
			product.ID,
			"Milk 1L",
			stringPtr("1 liter bottle of milk"),
			stringPtr("1111111111111"),
			nil,
			domain.PackagingTypeSingle,
			&literUnit.ID,
		nil)

		variant500ml := domain.NewProductVariant(
			product.ID,
			"Milk 500mL",
			stringPtr("500ml bottle of milk"),
			stringPtr("2222222222222"),
			nil,
			domain.PackagingTypeSingle,
			&mlUnit.ID,
			nil)

		// 5. Verify relationships
		assert.Equal(t, product.ID, variant1L.ProductID)
		assert.Equal(t, product.ID, variant500ml.ProductID)
		assert.Equal(t, dairyCategory.ID, product.CategoryID)
		assert.Equal(t, foodCategory.ID, *dairyCategory.ParentCategoryID)

		// 6. Test unit conversion between variants
		// Convert 1 liter to milliliters using the derived unit
		quantity1L := 1.0
		quantityInML, err := mlUnit.ConvertFromBaseUnit(quantity1L)
		require.NoError(t, err)
		assert.Equal(t, 1000.0, quantityInML) // 1L = 1000mL

		// Convert back to base unit
		quantityInBase, err := mlUnit.ConvertToBaseUnit(quantityInML)
		require.NoError(t, err)
		assert.Equal(t, quantity1L, quantityInBase)

		// All objects should have proper events
		assert.NotEmpty(t, foodCategory.GetEvents())
		assert.NotEmpty(t, dairyCategory.GetEvents())
		assert.NotEmpty(t, literUnit.GetEvents())
		assert.NotEmpty(t, mlUnit.GetEvents())
		assert.NotEmpty(t, product.GetEvents())
		assert.NotEmpty(t, variant1L.GetEvents())
		assert.NotEmpty(t, variant500ml.GetEvents())
	})
}

// Helper function
func stringPtr(s string) *string {
	return &s
}
