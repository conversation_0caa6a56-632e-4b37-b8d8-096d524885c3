package domain

import (
	"time"

	"github.com/google/uuid"
)

// AuthService defines the interface for authentication operations
type AuthService interface {
	// GenerateTokens generates access and refresh tokens for a user
	GenerateTokens(userID uuid.UUID) (*TokenPair, error)
	
	// ValidateAccessToken validates an access token and returns the user ID
	ValidateAccessToken(token string) (uuid.UUID, error)
	
	// ValidateRefreshToken validates a refresh token and returns the user ID
	ValidateRefreshToken(token string) (uuid.UUID, error)
	
	// RefreshTokens generates new tokens using a valid refresh token
	RefreshTokens(refreshToken string) (*TokenPair, error)
	
	// RevokeRefreshToken revokes a refresh token
	RevokeRefreshToken(token string) error
	
	// RevokeAllUserTokens revokes all refresh tokens for a user
	RevokeAllUserTokens(userID uuid.UUID) error
	
	// HashPassword hashes a password
	HashPassword(password string) (string, error)
	
	// VerifyPassword verifies a password against its hash
	VerifyPassword(password, hash string) error
}

// TokenPair represents an access token and refresh token pair
type TokenPair struct {
	AccessToken           string    `json:"access_token"`
	RefreshToken          string    `json:"refresh_token"`
	AccessTokenExpiresAt  time.Time `json:"access_token_expires_at"`
	RefreshTokenExpiresAt time.Time `json:"refresh_token_expires_at"`
	TokenType             string    `json:"token_type"`
}

// JWTClaims represents JWT token claims
type JWTClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Email  string    `json:"email,omitempty"`
	Type   string    `json:"type"` // "access" or "refresh"
	
	// Standard JWT claims
	Subject   string    `json:"sub"`
	ExpiresAt time.Time `json:"exp"`
	IssuedAt  time.Time `json:"iat"`
	NotBefore time.Time `json:"nbf"`
	Issuer    string    `json:"iss"`
	JTI       string    `json:"jti,omitempty"` // JWT ID, used for refresh tokens
}

// LoginCredentials represents user login credentials
type LoginCredentials struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
}

// RegisterCredentials represents user registration credentials
type RegisterCredentials struct {
	Username        string  `json:"username" validate:"required,min=3,max=50,alphanum"`
	Email           string  `json:"email" validate:"required,email"`
	Password        string  `json:"password" validate:"required,min=8"`
	ConfirmPassword string  `json:"confirm_password" validate:"required,eqfield=Password"`
	FirstName       *string `json:"first_name,omitempty" validate:"omitempty,max=100"`
	LastName        *string `json:"last_name,omitempty" validate:"omitempty,max=100"`
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=NewPassword"`
}

// UpdateProfileRequest represents a profile update request
type UpdateProfileRequest struct {
	FirstName         *string `json:"first_name,omitempty" validate:"omitempty,max=100"`
	LastName          *string `json:"last_name,omitempty" validate:"omitempty,max=100"`
	ProfilePictureURL *string `json:"profile_picture_url,omitempty" validate:"omitempty,url"`
}

// AuthenticatedUser represents an authenticated user context
type AuthenticatedUser struct {
	ID       uuid.UUID `json:"id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
}

// PasswordResetRequest represents a password reset request
type PasswordResetRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// PasswordResetConfirm represents a password reset confirmation
type PasswordResetConfirm struct {
	Token           string `json:"token" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=NewPassword"`
}

// HashService defines the interface for password hashing operations
type HashService interface {
	Hash(password string) (string, error)
	Verify(password, hash string) error
}

// TokenService defines the interface for token operations
type TokenService interface {
	GenerateAccessToken(userID uuid.UUID, email string) (string, time.Time, error)
	GenerateRefreshToken(userID uuid.UUID) (string, string, time.Time, error) // token, jti, expiresAt, error
	ValidateToken(token string) (*JWTClaims, error)
	ExtractClaims(token string) (*JWTClaims, error)
}
