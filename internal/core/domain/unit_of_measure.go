package domain

import (
	"time"

	"github.com/google/uuid"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// UnitOfMeasureType represents the type of unit
type UnitOfMeasureType string

const (
	UnitTypeVolume UnitOfMeasureType = "volume"
	UnitTypeWeight UnitOfMeasureType = "weight"
	UnitTypeCount  UnitOfMeasureType = "count"
	UnitTypeLength UnitOfMeasureType = "length"
	UnitTypeArea   UnitOfMeasureType = "area"
	UnitTypeOther  UnitOfMeasureType = "other"
)

// UnitOfMeasure represents a unit of measurement
type UnitOfMeasure struct {
	ID               uuid.UUID         `json:"id"`
	Name             string            `json:"name"`
	Symbol           string            `json:"symbol"`
	Type             UnitOfMeasureType `json:"type"`
	Description      *string           `json:"description,omitempty"`
	IsBaseUnit       bool              `json:"is_base_unit"`                // True for base units like "gram", "liter"
	BaseUnitID       *uuid.UUID        `json:"base_unit_id,omitempty"`      // Reference to base unit if this is derived
	ConversionFactor *float64          `json:"conversion_factor,omitempty"` // Factor to convert to base unit
	CreatedAt        time.Time         `json:"created_at"`
	UpdatedAt        time.Time         `json:"updated_at"`
	DeletedAt        *time.Time        `json:"deleted_at,omitempty"`

	// Aggregate root fields
	events []DomainEvent
}

// NewUnitOfMeasure creates a new unit of measure
func NewUnitOfMeasure(name, symbol string, unitType UnitOfMeasureType, description *string) *UnitOfMeasure {
	now := time.Now()
	unit := &UnitOfMeasure{
		ID:          uuid.New(),
		Name:        name,
		Symbol:      symbol,
		Type:        unitType,
		Description: description,
		IsBaseUnit:  true, // Default to base unit
		CreatedAt:   now,
		UpdatedAt:   now,
		events:      make([]DomainEvent, 0),
	}

	// Add domain event
	unit.AddEvent(NewUnitOfMeasureCreatedEvent(unit.ID, unit.Name, unit.Symbol, unit.Type))

	return unit
}

// NewDerivedUnitOfMeasure creates a new derived unit with conversion factor
func NewDerivedUnitOfMeasure(name, symbol string, unitType UnitOfMeasureType, description *string, baseUnitID uuid.UUID, conversionFactor float64) *UnitOfMeasure {
	now := time.Now()
	unit := &UnitOfMeasure{
		ID:               uuid.New(),
		Name:             name,
		Symbol:           symbol,
		Type:             unitType,
		Description:      description,
		IsBaseUnit:       false,
		BaseUnitID:       &baseUnitID,
		ConversionFactor: &conversionFactor,
		CreatedAt:        now,
		UpdatedAt:        now,
		events:           make([]DomainEvent, 0),
	}

	// Add domain event
	unit.AddEvent(NewUnitOfMeasureCreatedEvent(unit.ID, unit.Name, unit.Symbol, unit.Type))

	return unit
}

// UpdateDetails updates unit details
func (u *UnitOfMeasure) UpdateDetails(name, symbol string, description *string) {
	u.Name = name
	u.Symbol = symbol
	u.Description = description
	u.UpdatedAt = time.Now()

	// Add domain event
	u.AddEvent(NewUnitOfMeasureUpdatedEvent(u.ID, u.Name, u.Symbol))
}

// UpdateConversionFactor updates the conversion factor for derived units
func (u *UnitOfMeasure) UpdateConversionFactor(factor float64) error {
	if u.IsBaseUnit {
		return errors.NewBusinessError("INVALID_OPERATION", "Cannot set conversion factor for base units")
	}

	if factor <= 0 {
		return errors.NewBusinessError("INVALID_CONVERSION_FACTOR", "Conversion factor must be positive")
	}

	u.ConversionFactor = &factor
	u.UpdatedAt = time.Now()

	// Add domain event
	u.AddEvent(NewUnitConversionUpdatedEvent(u.ID, factor))

	return nil
}

// ConvertToBaseUnit converts a quantity to the base unit
func (u *UnitOfMeasure) ConvertToBaseUnit(quantity float64) (float64, error) {
	if u.IsBaseUnit {
		return quantity, nil
	}

	if u.ConversionFactor == nil {
		return 0, errors.NewBusinessError("MISSING_CONVERSION_FACTOR", "Conversion factor not set for derived unit")
	}

	return quantity / (*u.ConversionFactor), nil
}

// ConvertFromBaseUnit converts a quantity from the base unit to this unit
func (u *UnitOfMeasure) ConvertFromBaseUnit(baseQuantity float64) (float64, error) {
	if u.IsBaseUnit {
		return baseQuantity, nil
	}

	if u.ConversionFactor == nil {
		return 0, errors.NewBusinessError("MISSING_CONVERSION_FACTOR", "Conversion factor not set for derived unit")
	}

	return baseQuantity * (*u.ConversionFactor), nil
}

// SoftDelete marks the unit as deleted
func (u *UnitOfMeasure) SoftDelete() {
	now := time.Now()
	u.DeletedAt = &now
	u.UpdatedAt = now

	// Add domain event
	u.AddEvent(NewUnitOfMeasureDeletedEvent(u.ID, u.Name))
}

// IsDeleted checks if the unit is soft deleted
func (u *UnitOfMeasure) IsDeleted() bool {
	return u.DeletedAt != nil
}

// IsCompatibleWith checks if this unit is compatible with another unit (same type)
func (u *UnitOfMeasure) IsCompatibleWith(other *UnitOfMeasure) bool {
	return u.Type == other.Type
}

// Domain Events implementation
func (u *UnitOfMeasure) AddEvent(event DomainEvent) {
	u.events = append(u.events, event)
}

func (u *UnitOfMeasure) GetEvents() []DomainEvent {
	return u.events
}

func (u *UnitOfMeasure) ClearEvents() {
	u.events = make([]DomainEvent, 0)
}

// UnitOfMeasureRepository defines the interface for unit persistence
type UnitOfMeasureRepository interface {
	Create(unit *UnitOfMeasure) error
	GetByID(id uuid.UUID) (*UnitOfMeasure, error)
	GetByName(name string) (*UnitOfMeasure, error)
	GetBySymbol(symbol string) (*UnitOfMeasure, error)
	GetByType(unitType UnitOfMeasureType) ([]*UnitOfMeasure, error)
	GetBaseUnits() ([]*UnitOfMeasure, error)
	GetDerivedUnits(baseUnitID uuid.UUID) ([]*UnitOfMeasure, error)
	GetAllUnits() ([]*UnitOfMeasure, error)
	Update(unit *UnitOfMeasure) error
	Delete(id uuid.UUID) error
	ExistsByName(name string) (bool, error)
	ExistsBySymbol(symbol string) (bool, error)
}

// UnitConversionService provides unit conversion functionality
type UnitConversionService interface {
	ConvertQuantity(quantity float64, fromUnitID, toUnitID uuid.UUID) (float64, error)
	GetCompatibleUnits(unitID uuid.UUID) ([]*UnitOfMeasure, error)
	ValidateConversion(fromUnitID, toUnitID uuid.UUID) error
}

// Domain Events

// UnitOfMeasureCreatedEvent is raised when a unit is created
type UnitOfMeasureCreatedEvent struct {
	*BaseDomainEvent
}

// NewUnitOfMeasureCreatedEvent creates a new unit of measure created event
func NewUnitOfMeasureCreatedEvent(unitID uuid.UUID, name, symbol string, unitType UnitOfMeasureType) *UnitOfMeasureCreatedEvent {
	eventData := map[string]interface{}{
		"name":   name,
		"symbol": symbol,
		"type":   unitType,
	}

	return &UnitOfMeasureCreatedEvent{
		BaseDomainEvent: newBaseDomainEvent("unit_of_measure.created", unitID, eventData),
	}
}

// UnitOfMeasureUpdatedEvent is raised when a unit is updated
type UnitOfMeasureUpdatedEvent struct {
	*BaseDomainEvent
}

// NewUnitOfMeasureUpdatedEvent creates a new unit of measure updated event
func NewUnitOfMeasureUpdatedEvent(unitID uuid.UUID, name, symbol string) *UnitOfMeasureUpdatedEvent {
	eventData := map[string]interface{}{
		"name":   name,
		"symbol": symbol,
	}

	return &UnitOfMeasureUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("unit_of_measure.updated", unitID, eventData),
	}
}

// UnitConversionUpdatedEvent is raised when conversion factor is updated
type UnitConversionUpdatedEvent struct {
	*BaseDomainEvent
}

// NewUnitConversionUpdatedEvent creates a new unit conversion updated event
func NewUnitConversionUpdatedEvent(unitID uuid.UUID, conversionFactor float64) *UnitConversionUpdatedEvent {
	eventData := map[string]interface{}{
		"conversion_factor": conversionFactor,
	}

	return &UnitConversionUpdatedEvent{
		BaseDomainEvent: newBaseDomainEvent("unit_of_measure.conversion_updated", unitID, eventData),
	}
}

// UnitOfMeasureDeletedEvent is raised when a unit is deleted
type UnitOfMeasureDeletedEvent struct {
	*BaseDomainEvent
}

// NewUnitOfMeasureDeletedEvent creates a new unit of measure deleted event
func NewUnitOfMeasureDeletedEvent(unitID uuid.UUID, name string) *UnitOfMeasureDeletedEvent {
	eventData := map[string]interface{}{
		"name": name,
	}

	return &UnitOfMeasureDeletedEvent{
		BaseDomainEvent: newBaseDomainEvent("unit_of_measure.deleted", unitID, eventData),
	}
}
