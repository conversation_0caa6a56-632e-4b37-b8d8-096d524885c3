package services

import (
	"context"
	"fmt"
	"sync"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// NotificationService implements the notification service interface
type NotificationService struct {
	providers  map[domain.NotificationChannel]domain.NotificationProvider
	repository domain.NotificationRepository
	logger     Logger
	mu         sync.RWMutex
}

// Logger interface for notification service
type Logger interface {
	Info(msg string, fields map[string]interface{})
	Error(msg string, err error, fields map[string]interface{})
	Debug(msg string, fields map[string]interface{})
}

// NewNotificationService creates a new notification service
func NewNotificationService(repository domain.NotificationRepository, logger Logger) *NotificationService {
	return &NotificationService{
		providers:  make(map[domain.NotificationChannel]domain.NotificationProvider),
		repository: repository,
		logger:     logger,
	}
}

// RegisterProvider registers a notification provider
func (ns *NotificationService) RegisterProvider(provider domain.NotificationProvider) error {
	if provider == nil {
		return errors.New(errors.ErrCodeInvalidInput, "Provider cannot be nil")
	}

	if err := provider.ValidateConfig(); err != nil {
		return fmt.Errorf("provider validation failed: %w", err)
	}

	ns.mu.Lock()
	defer ns.mu.Unlock()

	channel := provider.GetChannel()
	ns.providers[channel] = provider

	ns.logger.Info("Notification provider registered", map[string]interface{}{
		"channel": string(channel),
		"enabled": provider.IsEnabled(),
	})

	return nil
}

// SendNotification sends a single notification
func (ns *NotificationService) SendNotification(ctx context.Context, notification *domain.Notification) error {
	// Store notification in repository first
	if err := ns.repository.Create(notification); err != nil {
		ns.logger.Error("Failed to store notification", err, map[string]interface{}{
			"notification_id": notification.ID,
			"user_id":         notification.UserID,
			"type":            string(notification.Type),
			"channel":         string(notification.Channel),
		})
		return err
	}

	// Check if notification is ready to send
	if !notification.IsReadyToSend() {
		ns.logger.Debug("Notification not ready to send", map[string]interface{}{
			"notification_id": notification.ID,
			"scheduled_at":    notification.ScheduledAt,
			"status":          string(notification.Status),
		})
		return nil
	}

	// Get provider for the channel
	ns.mu.RLock()
	provider, exists := ns.providers[notification.Channel]
	ns.mu.RUnlock()

	if !exists {
		errorMsg := fmt.Sprintf("No provider registered for channel: %s", notification.Channel)
		notification.MarkAsFailed(errorMsg)
		ns.repository.Update(notification)
		return errors.New(errors.ErrCodeNotFound, errorMsg)
	}

	if !provider.IsEnabled() {
		notification.MarkAsSkipped()
		ns.repository.Update(notification)
		ns.logger.Debug("Provider disabled, skipping notification", map[string]interface{}{
			"notification_id": notification.ID,
			"channel":         string(notification.Channel),
		})
		return nil
	}

	// Send notification
	if err := provider.Send(ctx, notification); err != nil {
		errorMsg := fmt.Sprintf("Failed to send notification: %v", err)
		notification.MarkAsFailed(errorMsg)
		ns.repository.Update(notification)
		
		ns.logger.Error("Failed to send notification", err, map[string]interface{}{
			"notification_id": notification.ID,
			"channel":         string(notification.Channel),
			"user_id":         notification.UserID,
		})
		return err
	}

	// Mark as sent
	notification.MarkAsSent()
	if err := ns.repository.Update(notification); err != nil {
		ns.logger.Error("Failed to update notification status", err, map[string]interface{}{
			"notification_id": notification.ID,
		})
		// Don't return error as notification was actually sent
	}

	ns.logger.Info("Notification sent successfully", map[string]interface{}{
		"notification_id": notification.ID,
		"channel":         string(notification.Channel),
		"user_id":         notification.UserID,
		"type":            string(notification.Type),
	})

	return nil
}

// SendBulkNotifications sends multiple notifications
func (ns *NotificationService) SendBulkNotifications(ctx context.Context, notifications []*domain.Notification) error {
	var errors []error
	successCount := 0

	for _, notification := range notifications {
		if err := ns.SendNotification(ctx, notification); err != nil {
			errors = append(errors, err)
		} else {
			successCount++
		}
	}

	ns.logger.Info("Bulk notifications processed", map[string]interface{}{
		"total_notifications": len(notifications),
		"success_count":       successCount,
		"error_count":         len(errors),
	})

	if len(errors) > 0 {
		return fmt.Errorf("failed to send %d out of %d notifications", len(errors), len(notifications))
	}

	return nil
}

// GetSupportedChannels returns the list of supported notification channels
func (ns *NotificationService) GetSupportedChannels() []domain.NotificationChannel {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	channels := make([]domain.NotificationChannel, 0, len(ns.providers))
	for channel, provider := range ns.providers {
		if provider.IsEnabled() {
			channels = append(channels, channel)
		}
	}

	return channels
}

// ProcessPendingNotifications processes pending notifications from the repository
func (ns *NotificationService) ProcessPendingNotifications(ctx context.Context, limit int) error {
	notifications, err := ns.repository.GetPendingNotifications(limit)
	if err != nil {
		return err
	}

	if len(notifications) == 0 {
		return nil
	}

	ns.logger.Info("Processing pending notifications", map[string]interface{}{
		"count": len(notifications),
	})

	var processErrors []error
	for _, notification := range notifications {
		// Skip the repository storage since these are already stored
		if !notification.IsReadyToSend() {
			continue
		}

		// Get provider and send
		ns.mu.RLock()
		provider, exists := ns.providers[notification.Channel]
		ns.mu.RUnlock()

		if !exists {
			errorMsg := fmt.Sprintf("No provider registered for channel: %s", notification.Channel)
			notification.MarkAsFailed(errorMsg)
			ns.repository.Update(notification)
			continue
		}

		if !provider.IsEnabled() {
			notification.MarkAsSkipped()
			ns.repository.Update(notification)
			continue
		}

		if err := provider.Send(ctx, notification); err != nil {
			errorMsg := fmt.Sprintf("Failed to send notification: %v", err)
			notification.MarkAsFailed(errorMsg)
			ns.repository.Update(notification)
			processErrors = append(processErrors, err)
			continue
		}

		notification.MarkAsSent()
		ns.repository.Update(notification)
	}

	if len(processErrors) > 0 {
		return fmt.Errorf("failed to process %d notifications", len(processErrors))
	}

	return nil
}

// GetProviderStatus returns the status of all registered providers
func (ns *NotificationService) GetProviderStatus() map[domain.NotificationChannel]bool {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	status := make(map[domain.NotificationChannel]bool)
	for channel, provider := range ns.providers {
		status[channel] = provider.IsEnabled()
	}

	return status
}
