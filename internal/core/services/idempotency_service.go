package services

import (
	"crypto/sha256"
	"fmt"
	"strings"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// IdempotencyService implements the idempotency service interface
type IdempotencyService struct {
	repository domain.IdempotencyRepository
	config     *domain.IdempotencyConfig
	logger     Logger
}

// NewIdempotencyService creates a new idempotency service
func NewIdempotencyService(
	repository domain.IdempotencyRepository,
	config *domain.IdempotencyConfig,
	logger Logger,
) *IdempotencyService {
	if config == nil {
		config = domain.DefaultIdempotencyConfig()
	}

	return &IdempotencyService{
		repository: repository,
		config:     config,
		logger:     logger,
	}
}

// CheckIdempotency checks if a request is idempotent and returns cached response if available
func (s *IdempotencyService) CheckIdempotency(key string, requestHash string, method string, path string, userID *uuid.UUID) (*domain.IdempotencyKey, error) {
	if !s.config.Enabled {
		return nil, nil
	}

	// Try to get existing key
	existingKey, err := s.repository.Get(key)
	if err != nil {
		// If not found, try to set as processing
		if appErr, ok := err.(*errors.AppError); ok && appErr.Code == errors.ErrCodeNotFound {
			return s.setProcessing(key, requestHash, method, path, userID)
		}
		return nil, err
	}

	// Validate request hash matches
	if existingKey.RequestHash != requestHash {
		s.logger.Error("Idempotency key hash mismatch", nil, map[string]interface{}{
			"key":           key,
			"existing_hash": existingKey.RequestHash,
			"request_hash":  requestHash,
			"method":        method,
			"path":          path,
		})
		return nil, errors.New(errors.ErrCodeConflict, "Idempotency key exists with different request data")
	}

	return existingKey, nil
}

// StartProcessing marks an idempotent request as being processed
func (s *IdempotencyService) StartProcessing(key string) error {
	if !s.config.Enabled {
		return nil
	}

	existingKey, err := s.repository.Get(key)
	if err != nil {
		return err
	}

	if !existingKey.IsPending() {
		return errors.New(errors.ErrCodeConflict, "Request is not in pending status")
	}

	existingKey.MarkProcessingStarted()
	return s.repository.Update(existingKey)
}

// CompleteRequest stores the response for an idempotent request
func (s *IdempotencyService) CompleteRequest(key string, statusCode int, responseBody string, responseHeaders map[string]string) error {
	if !s.config.Enabled {
		return nil
	}

	existingKey, err := s.repository.Get(key)
	if err != nil {
		return err
	}

	// Truncate response body if too large
	if int64(len(responseBody)) > s.config.MaxResponseSize {
		responseBody = responseBody[:s.config.MaxResponseSize] + "...[truncated]"
	}

	existingKey.MarkCompleted(statusCode, responseBody, responseHeaders)

	err = s.repository.Update(existingKey)
	if err != nil {
		s.logger.Error("Failed to update completed idempotency key", err, map[string]interface{}{
			"key":         key,
			"status_code": statusCode,
		})
		return err
	}

	s.logger.Info("Idempotent request completed", map[string]interface{}{
		"key":                 key,
		"status_code":         statusCode,
		"processing_duration": existingKey.GetProcessingDuration().String(),
	})

	return nil
}

// FailRequest marks an idempotent request as failed
func (s *IdempotencyService) FailRequest(key string, errorMessage string) error {
	if !s.config.Enabled {
		return nil
	}

	existingKey, err := s.repository.Get(key)
	if err != nil {
		return err
	}

	existingKey.MarkFailed()

	err = s.repository.Update(existingKey)
	if err != nil {
		s.logger.Error("Failed to update failed idempotency key", err, map[string]interface{}{
			"key":   key,
			"error": errorMessage,
		})
		return err
	}

	s.logger.Info("Idempotent request failed", map[string]interface{}{
		"key":                 key,
		"error":               errorMessage,
		"processing_duration": existingKey.GetProcessingDuration().String(),
	})

	return nil
}

// GenerateKey generates an idempotency key from request data
func (s *IdempotencyService) GenerateKey(method string, path string, userID *uuid.UUID, requestBody string) string {
	var keyParts []string

	keyParts = append(keyParts, method)
	keyParts = append(keyParts, path)

	if s.config.IncludeUserID && userID != nil {
		keyParts = append(keyParts, userID.String())
	}

	if s.config.IncludeRequestBody && requestBody != "" {
		// Create hash of request body to keep key size manageable
		bodyHash := s.hashString(requestBody)
		keyParts = append(keyParts, bodyHash)
	}

	combined := strings.Join(keyParts, "|")
	return s.hashString(combined)
}

// CreateRequestHash creates a hash of the request for comparison
func (s *IdempotencyService) CreateRequestHash(method string, path string, userID *uuid.UUID, requestBody string) string {
	var hashParts []string

	hashParts = append(hashParts, method)
	hashParts = append(hashParts, path)

	if userID != nil {
		hashParts = append(hashParts, userID.String())
	}

	if requestBody != "" {
		hashParts = append(hashParts, requestBody)
	}

	combined := strings.Join(hashParts, "|")
	return s.hashString(combined)
}

// IsIdempotentMethod checks if an HTTP method should be treated as idempotent
func (s *IdempotencyService) IsIdempotentMethod(method string) bool {
	// Check if method is excluded
	for _, excludedMethod := range s.config.ExcludedMethods {
		if method == excludedMethod {
			return false
		}
	}

	// POST, PUT, PATCH, DELETE should be idempotent
	switch method {
	case "POST", "PUT", "PATCH", "DELETE":
		return true
	default:
		return false
	}
}

// ShouldSkipPath checks if a path should be excluded from idempotency
func (s *IdempotencyService) ShouldSkipPath(path string) bool {
	for _, excludedPath := range s.config.ExcludedPaths {
		if strings.HasPrefix(path, excludedPath) {
			return true
		}
	}
	return false
}

// CleanupExpired removes expired idempotency keys
func (s *IdempotencyService) CleanupExpired() error {
	if !s.config.Enabled {
		return nil
	}

	// Redis automatically handles expiration, but we can implement manual cleanup if needed
	expiredKeys, err := s.repository.GetExpiredKeys(1000)
	if err != nil {
		return err
	}

	if len(expiredKeys) > 0 {
		err = s.repository.DeleteBatch(expiredKeys)
		if err != nil {
			s.logger.Error("Failed to cleanup expired idempotency keys", err, map[string]interface{}{
				"expired_count": len(expiredKeys),
			})
			return err
		}

		s.logger.Info("Cleaned up expired idempotency keys", map[string]interface{}{
			"expired_count": len(expiredKeys),
		})
	}

	return nil
}

// GetConfig returns the current configuration
func (s *IdempotencyService) GetConfig() *domain.IdempotencyConfig {
	return s.config
}

// UpdateConfig updates the service configuration
func (s *IdempotencyService) UpdateConfig(config *domain.IdempotencyConfig) {
	s.config = config
	s.logger.Info("Idempotency service configuration updated", map[string]interface{}{
		"enabled":              config.Enabled,
		"default_ttl":          config.DefaultTTL.String(),
		"max_ttl":              config.MaxTTL.String(),
		"include_user_id":      config.IncludeUserID,
		"include_request_body": config.IncludeRequestBody,
	})
}

// GetStats returns statistics about idempotency usage
func (s *IdempotencyService) GetStats() (map[string]interface{}, error) {
	if !s.config.Enabled {
		return map[string]interface{}{
			"enabled": false,
		}, nil
	}

	// Get repository stats if available
	if statsRepo, ok := s.repository.(interface {
		GetStats() (map[string]interface{}, error)
	}); ok {
		repoStats, err := statsRepo.GetStats()
		if err != nil {
			return nil, err
		}

		stats := map[string]interface{}{
			"enabled":    true,
			"config":     s.config,
			"repository": repoStats,
		}

		return stats, nil
	}

	return map[string]interface{}{
		"enabled": true,
		"config":  s.config,
	}, nil
}

// Private helper methods

func (s *IdempotencyService) setProcessing(key string, requestHash string, method string, path string, userID *uuid.UUID) (*domain.IdempotencyKey, error) {
	idempotencyKey, created, err := s.repository.SetProcessing(key, requestHash, method, path, userID, s.config.DefaultTTL)
	if err != nil {
		return nil, err
	}

	if created {
		s.logger.Info("New idempotent request started", map[string]interface{}{
			"key":    key,
			"method": method,
			"path":   path,
		})
	} else {
		s.logger.Info("Existing idempotent request found", map[string]interface{}{
			"key":    key,
			"method": method,
			"path":   path,
			"status": string(idempotencyKey.Status),
		})
	}

	return idempotencyKey, nil
}

func (s *IdempotencyService) hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return fmt.Sprintf("%x", hash)
}
