package usecases

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// TestShoppingListDomain tests the basic domain logic for shopping lists
func TestShoppingListDomain(t *testing.T) {
	// Test creating a shopping list request
	userID := uuid.New()
	pantryID := uuid.New()

	req := domain.CreateShoppingListRequest{
		UserID:   userID,
		PantryID: pantryID,
		Name:     "Test Shopping List",
	}

	// Validate the request
	assert.Equal(t, userID, req.UserID)
	assert.Equal(t, pantryID, req.PantryID)
	assert.Equal(t, "Test Shopping List", req.Name)
}

// TestShoppingListItem tests the basic domain logic for shopping list items
func TestShoppingListItem(t *testing.T) {
	// Test creating a shopping list item
	shoppingListID := uuid.New()
	freeTextName := "Test Item"

	item := domain.NewShoppingListItem(
		shoppingListID,
		nil, // ProductVariantID
		&freeTextName,
		2.5, // QuantityDesired
		nil, // UnitOfMeasureID
		nil, // Notes
	)

	// Validate the item
	assert.NotNil(t, item.ID)
	assert.Equal(t, shoppingListID, item.ShoppingListID)
	assert.Equal(t, &freeTextName, item.FreeTextName)
	assert.Equal(t, 2.5, item.QuantityDesired)
	assert.False(t, item.IsPurchased)
}
