package usecases

import (
	"context"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// Logger provides a simplified logging interface for usecases
type Logger interface {
	Info(msg string, fields ...map[string]interface{})
	Error(msg string, err error, fields ...map[string]interface{})
	Debug(msg string, fields ...map[string]interface{})
	LogBusinessEvent(eventType, entityID string, data map[string]interface{})
}

// EventDispatcher defines an interface for dispatching domain events
type EventDispatcher interface {
	DispatchEvents(ctx context.Context, events []domain.DomainEvent)
}
