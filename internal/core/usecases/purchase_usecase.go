package usecases

import (
	"context"
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// PurchaseUsecase handles purchase-related business logic
type PurchaseUsecase struct {
	purchaseRepo         domain.PurchaseRepository
	storeRepo           domain.StoreRepository
	inventoryRepo       domain.InventoryItemRepository
	pantryAuthzService  domain.PantryAuthorizationService
	productVariantRepo  domain.ProductVariantRepository
}

// NewPurchaseUsecase creates a new PurchaseUsecase
func NewPurchaseUsecase(
	purchaseRepo domain.PurchaseRepository,
	storeRepo domain.StoreRepository,
	inventoryRepo domain.InventoryItemRepository,
	pantryAuthzService domain.PantryAuthorizationService,
	productVariantRepo domain.ProductVariantRepository,
) *PurchaseUsecase {
	return &PurchaseUsecase{
		purchaseRepo:        purchaseRepo,
		storeRepo:          storeRepo,
		inventoryRepo:      inventoryRepo,
		pantryAuthzService: pantryAuthzService,
		productVariantRepo: productVariantRepo,
	}
}

// CreatePurchaseRequest represents a request to create a purchase
type CreatePurchaseRequest struct {
	PantryID          uuid.UUID
	PurchaseDate      time.Time
	TotalAmount       float64
	Currency          string
	StoreID           *uuid.UUID
	StoreName         *string
	ReceiptImageURL   *string
	Notes             *string
	Items             []CreatePurchaseItemRequest
}

// CreatePurchaseItemRequest represents a request to create a purchase item
type CreatePurchaseItemRequest struct {
	ProductVariantID uuid.UUID
	QuantityBought   float64
	UnitOfMeasureID  uuid.UUID
	PricePerUnit     float64
	Notes            *string
}

// CreatePurchase creates a new purchase record
func (uc *PurchaseUsecase) CreatePurchase(ctx context.Context, userID uuid.UUID, req *CreatePurchaseRequest) (*domain.Purchase, error) {
	// Check create items permission
	hasPermission, err := uc.pantryAuthzService.CheckPermission(userID, req.PantryID, domain.PermissionCreateItems)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New(errors.ErrCodeForbidden, "No permission to create purchases")
	}

	// Validate store if provided
	if req.StoreID != nil {
		store, err := uc.storeRepo.GetByID(*req.StoreID)
		if err != nil {
			return nil, err
		}
		if store == nil {
			return nil, errors.New(errors.ErrCodeNotFound, "Store not found")
		}
	}

	// Create purchase
	purchase, err := domain.NewPurchase(
		req.PantryID,
		req.PurchaseDate,
		req.TotalAmount,
		req.Currency,
		req.StoreID,
		req.StoreName,
		req.ReceiptImageURL,
		userID,
		req.Notes,
	)
	if err != nil {
		return nil, err
	}

	// Add purchase items
	for _, itemReq := range req.Items {
		item, err := domain.NewPurchaseItem(
			purchase.ID,
			itemReq.ProductVariantID,
			itemReq.QuantityBought,
			itemReq.UnitOfMeasureID,
			itemReq.PricePerUnit,
			itemReq.Notes,
		)
		if err != nil {
			return nil, err
		}
		purchase.AddPurchaseItem(item)
	}

	// Calculate total amount
	purchase.UpdateTotalAmount()

	// Save to repository
	if err := uc.purchaseRepo.Create(purchase); err != nil {
		return nil, err
	}

	return purchase, nil
}

// LinkPurchaseToInventory links purchase items to inventory
func (uc *PurchaseUsecase) LinkPurchaseToInventory(ctx context.Context, userID, purchaseID uuid.UUID, locationID *uuid.UUID) error {
	// Get purchase
	purchase, err := uc.purchaseRepo.GetByID(purchaseID)
	if err != nil {
		return err
	}

	// Check create items permission
	hasPermission, err := uc.pantryAuthzService.CheckPermission(userID, purchase.PantryID, domain.PermissionCreateItems)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New(errors.ErrCodeForbidden, "No permission to add inventory items")
	}

	// Create inventory items for each purchase item
	for _, item := range purchase.Items {
		// Get product variant to check shelf life
		variant, err := uc.productVariantRepo.GetByID(item.ProductVariantID)
		if err != nil {
			return err
		}

		var expirationDate *time.Time
		if variant.ShelfLifeDays != nil {
			// Calculate expiration date: purchase date + shelf life days
			expDate := purchase.PurchaseDate.AddDate(0, 0, *variant.ShelfLifeDays)
			expirationDate = &expDate
		}

		inventoryItem := domain.NewInventoryItem(
			purchase.PantryID,
			locationID,
			item.ProductVariantID,
			item.QuantityBought,
			item.UnitOfMeasureID,
			&purchase.PurchaseDate,
			expirationDate,
			&item.PricePerUnit,
			item.Notes,
		)

		if err := uc.inventoryRepo.Create(inventoryItem); err != nil {
			return err
		}
	}

	return nil
}

// GetPurchasesByPantry retrieves purchases for a pantry with pagination
func (uc *PurchaseUsecase) GetPurchasesByPantry(ctx context.Context, userID, pantryID uuid.UUID, page, limit int) ([]*domain.Purchase, int64, error) {
	// Check view items permission
	hasPermission, err := uc.pantryAuthzService.CheckPermission(userID, pantryID, domain.PermissionViewItems)
	if err != nil {
		return nil, 0, err
	}
	if !hasPermission {
		return nil, 0, errors.New(errors.ErrCodeForbidden, "No permission to view purchases")
	}

	return uc.purchaseRepo.GetByPantryID(pantryID, page, limit)
}

// GetPurchase retrieves a specific purchase by ID
func (uc *PurchaseUsecase) GetPurchase(ctx context.Context, userID, purchaseID uuid.UUID) (*domain.Purchase, error) {
	purchase, err := uc.purchaseRepo.GetByID(purchaseID)
	if err != nil {
		return nil, err
	}

	// Check view items permission
	hasPermission, err := uc.pantryAuthzService.CheckPermission(userID, purchase.PantryID, domain.PermissionViewItems)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New(errors.ErrCodeForbidden, "No permission to view purchase")
	}

	return purchase, nil
}

// DeletePurchase deletes a purchase
func (uc *PurchaseUsecase) DeletePurchase(ctx context.Context, userID, purchaseID uuid.UUID) error {
	// Get purchase first to check authorization
	purchase, err := uc.purchaseRepo.GetByID(purchaseID)
	if err != nil {
		return err
	}

	// Check delete items permission
	hasPermission, err := uc.pantryAuthzService.CheckPermission(userID, purchase.PantryID, domain.PermissionDeleteItems)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New(errors.ErrCodeForbidden, "No permission to delete purchase")
	}

	return uc.purchaseRepo.Delete(purchaseID)
}