package usecases

import (
	"context"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// StoreUsecase handles store-related business logic
type StoreUsecase struct {
	storeRepo domain.StoreRepository
	logger    Logger
}

// NewStoreUsecase creates a new store usecase
func NewStoreUsecase(storeRepo domain.StoreRepository, logger Logger) *StoreUsecase {
	return &StoreUsecase{
		storeRepo: storeRepo,
		logger:    logger,
	}
}

// CreateStoreRequest represents the data needed to create a store
type CreateStoreRequest struct {
	Name        string  `json:"name" validate:"required"`
	Address     *string `json:"address"`
	City        *string `json:"city"`
	Country     *string `json:"country"`
	PhoneNumber *string `json:"phone_number"`
	Website     *string `json:"website"`
}

// CreateStore creates a new store
func (u *StoreUsecase) CreateStore(ctx context.Context, req *CreateStoreRequest) (*domain.Store, error) {
	store := domain.NewStore(
		req.Name,
		req.Address,
		req.City,
		req.Country,
		req.PhoneNumber,
		req.Website,
	)

	if err := u.storeRepo.Create(store); err != nil {
		u.logger.Error("Failed to create store", err)
		return nil, err
	}

	u.logger.Info("Store created successfully", map[string]interface{}{
		"store_id": store.ID,
		"name":     store.Name,
	})

	return store, nil
}

// GetStore retrieves a store by ID
func (u *StoreUsecase) GetStore(ctx context.Context, id uuid.UUID) (*domain.Store, error) {
	store, err := u.storeRepo.GetByID(id)
	if err != nil {
		u.logger.Error("Failed to get store", err, map[string]interface{}{
			"store_id": id,
		})
		return nil, err
	}

	return store, nil
}

// UpdateStoreRequest represents the data needed to update a store
type UpdateStoreRequest struct {
	Name        string  `json:"name" validate:"required"`
	Address     *string `json:"address"`
	City        *string `json:"city"`
	Country     *string `json:"country"`
	PhoneNumber *string `json:"phone_number"`
	Website     *string `json:"website"`
}

// UpdateStore updates an existing store
func (u *StoreUsecase) UpdateStore(ctx context.Context, id uuid.UUID, req *UpdateStoreRequest) (*domain.Store, error) {
	store, err := u.storeRepo.GetByID(id)
	if err != nil {
		u.logger.Error("Failed to get store for update", err, map[string]interface{}{
			"store_id": id,
		})
		return nil, err
	}

	store.Name = req.Name
	store.Address = req.Address
	store.City = req.City
	store.Country = req.Country
	store.PhoneNumber = req.PhoneNumber
	store.Website = req.Website

	if err := u.storeRepo.Update(store); err != nil {
		u.logger.Error("Failed to update store", err, map[string]interface{}{
			"store_id": id,
		})
		return nil, err
	}

	u.logger.Info("Store updated successfully", map[string]interface{}{
		"store_id": store.ID,
		"name":     store.Name,
	})

	return store, nil
}

// ListStores retrieves a paginated list of stores
func (u *StoreUsecase) ListStores(ctx context.Context, page, limit int) ([]*domain.Store, int64, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}

	stores, total, err := u.storeRepo.GetAll(page, limit)
	if err != nil {
		u.logger.Error("Failed to list stores", err, map[string]interface{}{
			"page":  page,
			"limit": limit,
		})
		return nil, 0, err
	}

	return stores, total, nil
}

// DeleteStore deletes a store by ID
func (u *StoreUsecase) DeleteStore(ctx context.Context, id uuid.UUID) error {
	// Check if store exists
	if _, err := u.storeRepo.GetByID(id); err != nil {
		return err
	}

	if err := u.storeRepo.Delete(id); err != nil {
		u.logger.Error("Failed to delete store", err, map[string]interface{}{
			"store_id": id,
		})
		return err
	}

	u.logger.Info("Store deleted successfully", map[string]interface{}{
		"store_id": id,
	})

	return nil
}

// SearchStores searches for stores by name
func (u *StoreUsecase) SearchStores(ctx context.Context, query string) ([]*domain.Store, error) {
	if query == "" {
		return nil, errors.NewValidationError("Search query is required", nil)
	}

	stores, err := u.storeRepo.SearchByName(query)
	if err != nil {
		u.logger.Error("Failed to search stores", err, map[string]interface{}{
			"query": query,
		})
		return nil, err
	}

	return stores, nil
}
