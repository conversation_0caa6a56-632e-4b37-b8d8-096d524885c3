# PantryPal

A smart pantry management system that helps you track inventory, manage recipes, and optimize shopping.

## Features

- User and Pantry Management
- Product Catalog and Categories  
- Inventory Tracking
- Recipe Management
- Shopping List Management
- Purchase History
- Expiration Tracking

## Getting Started

1. Clone the repository
2. Copy `.env.example` to `.env` and configure
3. Run `docker-compose up` to start services
4. Run `make migrate` to set up the database

## Development

- Run tests: `make test`
- Generate API docs: `make swagger`
- Start API server: `make run`

## Documentation

See the `frontend_documentation/` folder for API integration guides and feature documentation.
