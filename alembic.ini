[alembic]
sqlalchemy.url = sqlite:///./context_portal/context.db

[loggers]
keys = root,alembic,sqlalchemy

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = INFO
handlers = console
qualname =

[logger_alembic]
level = INFO
handlers = console
qualname = alembic

[logger_sqlalchemy]
level = WARN
handlers = console
qualname = sqlalchemy.engine

[handler_console]
class = StreamHandler
args = (sys.stderr,)
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S