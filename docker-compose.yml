version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: pantrypal_postgres
    environment:
      POSTGRES_DB: pantrypal
      POSTGRES_USER: pantrypal
      POSTGRES_PASSWORD: pantrypal_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pantrypal -d pantrypal"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: pantrypal_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Uncomment this section when you want to run the app in Docker
  # app:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: pantrypal_app
  #   ports:
  #     - "8080:8080"
  #   environment:
  #     APP_DATABASE_HOST: postgres
  #     APP_DATABASE_PASSWORD: pantrypal_dev_password
  #     APP_REDIS_HOST: redis
  #     APP_AUTH_JWT_SECRET: your_super_secret_jwt_key_for_development_only
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #   volumes:
  #     - ./config.yaml:/app/config.yaml

volumes:
  postgres_data:
  redis_data:
