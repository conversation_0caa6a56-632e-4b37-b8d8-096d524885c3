package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

// Test data structures
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type LoginResponse struct {
	Success bool `json:"success"`
	Data    struct {
		AccessToken string `json:"access_token"`
		User        struct {
			ID    string `json:"id"`
			Email string `json:"email"`
		} `json:"user"`
	} `json:"data"`
}

type CategoryRequest struct {
	Name        string  `json:"name"`
	Description *string `json:"description,omitempty"`
}

type UnitRequest struct {
	Name        string  `json:"name"`
	Symbol      string  `json:"symbol"`
	Type        string  `json:"type"`
	Description *string `json:"description,omitempty"`
}

type ProductRequest struct {
	Name        string  `json:"name"`
	Description *string `json:"description,omitempty"`
	CategoryID  string  `json:"category_id"`
	Brand       *string `json:"brand,omitempty"`
}

type VariantRequest struct {
	Name                   string  `json:"name"`
	Description            *string `json:"description,omitempty"`
	BarcodeGTIN            *string `json:"barcode_gtin,omitempty"`
	ImageURL               *string `json:"image_url,omitempty"`
	PackagingType          string  `json:"packaging_type"`
	DefaultUnitOfMeasureID *string `json:"default_unit_of_measure_id,omitempty"`
}

type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data"`
	Message string      `json:"message"`
}

const baseURL = "http://localhost:8080/api/v1"

func main() {
	fmt.Println("🧪 Testing Product Catalog API...")
	
	// Test authentication first
	token, err := testLogin()
	if err != nil {
		log.Fatalf("❌ Login failed: %v", err)
	}
	fmt.Println("✅ Authentication successful")
	
	// Test category management
	categoryID, err := testCategoryManagement(token)
	if err != nil {
		log.Fatalf("❌ Category management failed: %v", err)
	}
	fmt.Println("✅ Category management successful")
	
	// Test unit of measure management
	unitID, err := testUnitManagement(token)
	if err != nil {
		log.Fatalf("❌ Unit management failed: %v", err)
	}
	fmt.Println("✅ Unit management successful")
	
	// Test product management
	productID, err := testProductManagement(token, categoryID)
	if err != nil {
		log.Fatalf("❌ Product management failed: %v", err)
	}
	fmt.Println("✅ Product management successful")
	
	// Test product variant management
	err = testVariantManagement(token, productID, unitID)
	if err != nil {
		log.Fatalf("❌ Variant management failed: %v", err)
	}
	fmt.Println("✅ Variant management successful")
	
	fmt.Println("🎉 All Product Catalog API tests passed!")
}

func testLogin() (string, error) {
	loginReq := LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}
	
	body, _ := json.Marshal(loginReq)
	resp, err := http.Post(baseURL+"/auth/login", "application/json", bytes.NewBuffer(body))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	var loginResp LoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&loginResp); err != nil {
		return "", err
	}
	
	if !loginResp.Success {
		return "", fmt.Errorf("login failed")
	}
	
	return loginResp.Data.AccessToken, nil
}

func testCategoryManagement(token string) (string, error) {
	// Create category
	desc := "Test category for API testing"
	categoryReq := CategoryRequest{
		Name:        "Test Category",
		Description: &desc,
	}
	
	categoryID, err := createCategory(token, categoryReq)
	if err != nil {
		return "", fmt.Errorf("create category: %v", err)
	}
	
	// Get categories
	if err := getCategories(token); err != nil {
		return "", fmt.Errorf("get categories: %v", err)
	}
	
	// Get specific category
	if err := getCategory(token, categoryID); err != nil {
		return "", fmt.Errorf("get category: %v", err)
	}
	
	return categoryID, nil
}

func testUnitManagement(token string) (string, error) {
	// Create base unit
	desc := "Test unit for API testing"
	unitReq := UnitRequest{
		Name:        "Test Kilogram",
		Symbol:      "tkg",
		Type:        "weight",
		Description: &desc,
	}
	
	unitID, err := createUnit(token, unitReq)
	if err != nil {
		return "", fmt.Errorf("create unit: %v", err)
	}
	
	// Get units
	if err := getUnits(token); err != nil {
		return "", fmt.Errorf("get units: %v", err)
	}
	
	// Get specific unit
	if err := getUnit(token, unitID); err != nil {
		return "", fmt.Errorf("get unit: %v", err)
	}
	
	return unitID, nil
}

func testProductManagement(token, categoryID string) (string, error) {
	// Create product
	desc := "Test product for API testing"
	brand := "Test Brand"
	productReq := ProductRequest{
		Name:        "Test Product",
		Description: &desc,
		CategoryID:  categoryID,
		Brand:       &brand,
	}
	
	productID, err := createProduct(token, productReq)
	if err != nil {
		return "", fmt.Errorf("create product: %v", err)
	}
	
	// Get products
	if err := getProducts(token); err != nil {
		return "", fmt.Errorf("get products: %v", err)
	}
	
	// Get specific product
	if err := getProduct(token, productID); err != nil {
		return "", fmt.Errorf("get product: %v", err)
	}
	
	return productID, nil
}

func testVariantManagement(token, productID, unitID string) error {
	// Create variant
	desc := "Test variant for API testing"
	barcode := "1234567890123"
	imageURL := "https://example.com/test.jpg"
	variantReq := VariantRequest{
		Name:                   "Test Variant",
		Description:            &desc,
		BarcodeGTIN:            &barcode,
		ImageURL:               &imageURL,
		PackagingType:          "single",
		DefaultUnitOfMeasureID: &unitID,
	}
	
	variantID, err := createVariant(token, productID, variantReq)
	if err != nil {
		return fmt.Errorf("create variant: %v", err)
	}
	
	// Get variants for product
	if err := getProductVariants(token, productID); err != nil {
		return fmt.Errorf("get product variants: %v", err)
	}
	
	// Get specific variant
	if err := getVariant(token, variantID); err != nil {
		return fmt.Errorf("get variant: %v", err)
	}
	
	// Search variants
	if err := searchVariants(token); err != nil {
		return fmt.Errorf("search variants: %v", err)
	}
	
	// Get variant by barcode
	if err := getVariantByBarcode(token, barcode); err != nil {
		return fmt.Errorf("get variant by barcode: %v", err)
	}
	
	return nil
}

// Helper functions for API calls

func makeRequest(method, url, token string, body interface{}) (*http.Response, error) {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer(nil)
	}
	
	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	
	client := &http.Client{Timeout: 10 * time.Second}
	return client.Do(req)
}

func createCategory(token string, req CategoryRequest) (string, error) {
	resp, err := makeRequest("POST", baseURL+"/catalog/categories", token, req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return "", err
	}
	
	if !apiResp.Success {
		return "", fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	return data["id"].(string), nil
}

func getCategories(token string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/categories", token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func getCategory(token, categoryID string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/categories/"+categoryID, token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func createUnit(token string, req UnitRequest) (string, error) {
	resp, err := makeRequest("POST", baseURL+"/catalog/units", token, req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return "", err
	}
	
	if !apiResp.Success {
		return "", fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	return data["id"].(string), nil
}

func getUnits(token string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/units", token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func getUnit(token, unitID string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/units/"+unitID, token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func createProduct(token string, req ProductRequest) (string, error) {
	resp, err := makeRequest("POST", baseURL+"/catalog/products", token, req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return "", err
	}
	
	if !apiResp.Success {
		return "", fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	return data["id"].(string), nil
}

func getProducts(token string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/products", token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func getProduct(token, productID string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/products/"+productID, token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func createVariant(token, productID string, req VariantRequest) (string, error) {
	resp, err := makeRequest("POST", baseURL+"/catalog/products/"+productID+"/variants", token, req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return "", err
	}
	
	if !apiResp.Success {
		return "", fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	return data["id"].(string), nil
}

func getProductVariants(token, productID string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/products/"+productID+"/variants", token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func getVariant(token, variantID string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/variants/"+variantID, token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func searchVariants(token string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/variants?query=test", token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}

func getVariantByBarcode(token, barcode string) error {
	resp, err := makeRequest("GET", baseURL+"/catalog/barcode/"+barcode, token, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	return json.NewDecoder(resp.Body).Decode(&apiResp)
}
