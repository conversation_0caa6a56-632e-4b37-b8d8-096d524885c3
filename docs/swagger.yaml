basePath: /api/v1
definitions:
  domain.AddShoppingListItemRequest:
    properties:
      free_text_name:
        maxLength: 255
        minLength: 1
        type: string
      notes:
        maxLength: 500
        type: string
      product_variant_id:
        type: string
      quantity_desired:
        type: number
      unit_of_measure_id:
        type: string
    required:
    - quantity_desired
    type: object
  domain.AlertConfigurationRequest:
    properties:
      alert_days:
        maximum: 7
        minimum: 0
        type: integer
      category_filters:
        items:
          type: string
        type: array
      channels:
        items:
          $ref: '#/definitions/domain.NotificationChannel'
        minItems: 1
        type: array
      critical_days:
        maximum: 1
        minimum: 0
        type: integer
      enabled:
        type: boolean
      min_value:
        minimum: 0
        type: number
      quiet_hours:
        $ref: '#/definitions/domain.QuietHours'
      warning_days:
        maximum: 30
        minimum: 1
        type: integer
    required:
    - channels
    type: object
  domain.BulkMarkItemsNotPurchasedRequest:
    properties:
      item_ids:
        items:
          type: string
        minItems: 1
        type: array
    required:
    - item_ids
    type: object
  domain.BulkMarkItemsPurchasedRequest:
    properties:
      item_ids:
        items:
          type: string
        minItems: 1
        type: array
    required:
    - item_ids
    type: object
  domain.ChangePasswordRequest:
    properties:
      confirm_password:
        type: string
      current_password:
        type: string
      new_password:
        minLength: 8
        type: string
    required:
    - confirm_password
    - current_password
    - new_password
    type: object
  domain.ChangeShoppingListStatusRequest:
    properties:
      status:
        allOf:
        - $ref: '#/definitions/domain.ShoppingListStatus'
        enum:
        - active
        - completed
        - archived
    required:
    - status
    type: object
  domain.ConsumeInventoryRequest:
    properties:
      consumed_quantity:
        type: number
      notes:
        maxLength: 500
        type: string
    required:
    - consumed_quantity
    type: object
  domain.CreateInventoryItemRequest:
    properties:
      expiration_date:
        type: string
      location_id:
        type: string
      notes:
        maxLength: 1000
        type: string
      product_variant_id:
        type: string
      purchase_date:
        type: string
      purchase_price:
        minimum: 0
        type: number
      quantity:
        type: number
      unit_of_measure_id:
        type: string
    required:
    - product_variant_id
    - quantity
    - unit_of_measure_id
    type: object
  domain.CreatePantryRequest:
    properties:
      description:
        maxLength: 500
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
    required:
    - name
    type: object
  domain.CreateRecipeIngredientRequest:
    properties:
      is_garnish:
        type: boolean
      is_optional:
        type: boolean
      name:
        maxLength: 200
        minLength: 1
        type: string
      notes:
        maxLength: 500
        type: string
      preparation:
        maxLength: 200
        type: string
      product_variant_id:
        type: string
      quantity:
        type: number
      unit:
        maxLength: 50
        type: string
      unit_of_measure_id:
        type: string
    required:
    - name
    - quantity
    type: object
  domain.CreateRecipeInstructionRequest:
    properties:
      duration:
        maximum: 1440
        minimum: 0
        type: integer
      instruction:
        maxLength: 2000
        minLength: 10
        type: string
      temperature:
        maximum: 500
        minimum: -50
        type: integer
      tips:
        maxLength: 1000
        type: string
      title:
        maxLength: 200
        type: string
    required:
    - instruction
    type: object
  domain.CreateRecipeNutritionRequest:
    properties:
      calories:
        maximum: 10000
        minimum: 0
        type: integer
      carbohydrates:
        maximum: 1000
        minimum: 0
        type: number
      fat:
        maximum: 1000
        minimum: 0
        type: number
      fiber:
        maximum: 1000
        minimum: 0
        type: number
      protein:
        maximum: 1000
        minimum: 0
        type: number
      serving_size:
        maxLength: 100
        type: string
      sodium:
        maximum: 10000
        minimum: 0
        type: number
      sugar:
        maximum: 1000
        minimum: 0
        type: number
    type: object
  domain.CreateRecipeRequest:
    properties:
      category:
        maxLength: 100
        type: string
      cook_time:
        maximum: 1440
        minimum: 0
        type: integer
      cuisine:
        maxLength: 100
        type: string
      description:
        maxLength: 1000
        type: string
      difficulty:
        allOf:
        - $ref: '#/definitions/domain.DifficultyLevel'
        enum:
        - easy
        - medium
        - hard
        - expert
      ingredients:
        items:
          $ref: '#/definitions/domain.CreateRecipeIngredientRequest'
        minItems: 1
        type: array
      instructions:
        items:
          $ref: '#/definitions/domain.CreateRecipeInstructionRequest'
        minItems: 1
        type: array
      is_public:
        type: boolean
      notes:
        maxLength: 2000
        type: string
      nutrition:
        $ref: '#/definitions/domain.CreateRecipeNutritionRequest'
      prep_time:
        maximum: 1440
        minimum: 0
        type: integer
      servings:
        maximum: 100
        minimum: 1
        type: integer
      source:
        maxLength: 500
        type: string
      tags:
        items:
          type: string
        type: array
      title:
        maxLength: 200
        minLength: 3
        type: string
    required:
    - ingredients
    - instructions
    - servings
    - title
    type: object
  domain.CreateShoppingListRequest:
    properties:
      description:
        maxLength: 1000
        type: string
      name:
        maxLength: 255
        minLength: 1
        type: string
      pantry_id:
        type: string
    required:
    - name
    - pantry_id
    type: object
  domain.DifficultyLevel:
    enum:
    - easy
    - medium
    - hard
    - expert
    type: string
    x-enum-varnames:
    - DifficultyEasy
    - DifficultyMedium
    - DifficultyHard
    - DifficultyExpert
  domain.ExpirationTrackingRequest:
    properties:
      alert_days:
        maximum: 7
        minimum: 0
        type: integer
      category_ids:
        items:
          type: string
        type: array
      channels:
        items:
          $ref: '#/definitions/domain.NotificationChannel'
        type: array
      critical_days:
        maximum: 1
        minimum: 0
        type: integer
      send_alerts:
        type: boolean
      warning_days:
        maximum: 30
        minimum: 1
        type: integer
    type: object
  domain.InventoryCheckRequest:
    properties:
      pantry_id:
        type: string
    type: object
  domain.LoginCredentials:
    properties:
      email:
        type: string
      password:
        minLength: 8
        type: string
    required:
    - email
    - password
    type: object
  domain.NotificationChannel:
    enum:
    - email
    - telegram
    - supabase
    - webhook
    - in_app
    type: string
    x-enum-varnames:
    - NotificationChannelEmail
    - NotificationChannelTelegram
    - NotificationChannelSupabase
    - NotificationChannelWebhook
    - NotificationChannelInApp
  domain.PantryShoppingListStats:
    properties:
      active_shopping_lists:
        type: integer
      archived_shopping_lists:
        type: integer
      completed_shopping_lists:
        type: integer
      overall_completion_rate:
        type: number
      pantry_id:
        type: string
      total_items:
        type: integer
      total_purchased_items:
        type: integer
      total_shopping_lists:
        type: integer
    type: object
  domain.Purchase:
    properties:
      created_at:
        type: string
      currency:
        type: string
      deleted_at:
        type: string
      id:
        type: string
      items:
        items:
          $ref: '#/definitions/domain.PurchaseItem'
        type: array
      notes:
        type: string
      pantry_id:
        type: string
      purchase_date:
        type: string
      purchased_by_user_id:
        type: string
      receipt_image_url:
        type: string
      store_id:
        type: string
      store_name:
        type: string
      total_amount:
        type: number
      updated_at:
        type: string
    type: object
  domain.PurchaseItem:
    properties:
      created_at:
        type: string
      deleted_at:
        type: string
      id:
        type: string
      notes:
        type: string
      price_per_unit:
        type: number
      product_variant_id:
        type: string
      purchase_id:
        type: string
      quantity_bought:
        type: number
      total_price:
        type: number
      unit_of_measure_id:
        type: string
      updated_at:
        type: string
    type: object
  domain.QuietHours:
    properties:
      enabled:
        type: boolean
      end_time:
        type: string
      start_time:
        type: string
      timezone:
        type: string
    type: object
  domain.RegisterCredentials:
    properties:
      confirm_password:
        type: string
      email:
        type: string
      first_name:
        maxLength: 100
        type: string
      last_name:
        maxLength: 100
        type: string
      password:
        minLength: 8
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - confirm_password
    - email
    - password
    - username
    type: object
  domain.ScaleRecipeRequest:
    properties:
      servings:
        maximum: 100
        minimum: 1
        type: integer
    required:
    - servings
    type: object
  domain.ShoppingList:
    properties:
      created_at:
        type: string
      created_by:
        type: string
      description:
        type: string
      id:
        type: string
      items:
        description: Relationships
        items:
          $ref: '#/definitions/domain.ShoppingListItemEntity'
        type: array
      name:
        type: string
      pantry_id:
        type: string
      status:
        $ref: '#/definitions/domain.ShoppingListStatus'
      updated_at:
        type: string
    type: object
  domain.ShoppingListItemEntity:
    properties:
      created_at:
        type: string
      free_text_name:
        type: string
      id:
        type: string
      is_purchased:
        type: boolean
      notes:
        type: string
      product_variant_id:
        type: string
      purchased_at:
        type: string
      quantity_desired:
        type: number
      shopping_list_id:
        type: string
      unit_of_measure_id:
        type: string
      updated_at:
        type: string
    type: object
  domain.ShoppingListStats:
    properties:
      completion_percentage:
        type: number
      purchased_items:
        type: integer
      remaining_items:
        type: integer
      shopping_list_id:
        type: string
      total_items:
        type: integer
    type: object
  domain.ShoppingListStatus:
    enum:
    - active
    - completed
    - archived
    type: string
    x-enum-varnames:
    - ShoppingListStatusActive
    - ShoppingListStatusCompleted
    - ShoppingListStatusArchived
  domain.Store:
    properties:
      address:
        type: string
      city:
        type: string
      country:
        type: string
      created_at:
        type: string
      deleted_at:
        type: string
      id:
        type: string
      name:
        type: string
      phone_number:
        type: string
      updated_at:
        type: string
      website:
        type: string
    type: object
  domain.UpdateInventoryItemRequest:
    properties:
      expiration_date:
        type: string
      location_id:
        type: string
      notes:
        maxLength: 1000
        type: string
      purchase_date:
        type: string
      purchase_price:
        minimum: 0
        type: number
      quantity:
        type: number
    required:
    - quantity
    type: object
  domain.UpdatePantryRequest:
    properties:
      description:
        maxLength: 500
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
    required:
    - name
    type: object
  domain.UpdateProfileRequest:
    properties:
      first_name:
        maxLength: 100
        type: string
      last_name:
        maxLength: 100
        type: string
      profile_picture_url:
        type: string
    type: object
  domain.UpdateRecipeRequest:
    properties:
      category:
        maxLength: 100
        type: string
      cook_time:
        maximum: 1440
        minimum: 0
        type: integer
      cuisine:
        maxLength: 100
        type: string
      description:
        maxLength: 1000
        type: string
      difficulty:
        allOf:
        - $ref: '#/definitions/domain.DifficultyLevel'
        enum:
        - easy
        - medium
        - hard
        - expert
      ingredients:
        items:
          $ref: '#/definitions/domain.CreateRecipeIngredientRequest'
        type: array
      instructions:
        items:
          $ref: '#/definitions/domain.CreateRecipeInstructionRequest'
        type: array
      is_favorite:
        type: boolean
      is_public:
        type: boolean
      notes:
        maxLength: 2000
        type: string
      nutrition:
        $ref: '#/definitions/domain.CreateRecipeNutritionRequest'
      prep_time:
        maximum: 1440
        minimum: 0
        type: integer
      servings:
        maximum: 100
        minimum: 1
        type: integer
      source:
        maxLength: 500
        type: string
      tags:
        items:
          type: string
        type: array
      title:
        maxLength: 200
        minLength: 3
        type: string
    type: object
  domain.UpdateShoppingListItemRequest:
    properties:
      free_text_name:
        maxLength: 255
        minLength: 1
        type: string
      notes:
        maxLength: 500
        type: string
      product_variant_id:
        type: string
      quantity_desired:
        type: number
      unit_of_measure_id:
        type: string
    required:
    - quantity_desired
    type: object
  domain.UpdateShoppingListRequest:
    properties:
      description:
        maxLength: 1000
        type: string
      name:
        maxLength: 255
        minLength: 1
        type: string
    required:
    - name
    type: object
  handler.APIResponse:
    properties:
      data: {}
      error:
        $ref: '#/definitions/handler.ErrorInfo'
      message:
        type: string
      metadata:
        additionalProperties: true
        type: object
      request_id:
        type: string
      success:
        type: boolean
      timestamp:
        type: string
    type: object
  handler.CategoryResponse:
    properties:
      created_at:
        example: "2024-01-15T10:30:00Z"
        type: string
      description:
        example: Dairy products
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-446655440007
        type: string
      name:
        example: Dairy
        type: string
      parent_id:
        example: 550e8400-e29b-41d4-a716-446655440008
        type: string
      updated_at:
        example: "2024-01-15T10:30:00Z"
        type: string
    type: object
  handler.CreatePurchaseItemRequest:
    properties:
      notes:
        type: string
      price_per_unit:
        minimum: 0
        type: number
      product_variant_id:
        type: string
      quantity_bought:
        minimum: 0
        type: number
      unit_of_measure_id:
        type: string
    required:
    - price_per_unit
    - product_variant_id
    - quantity_bought
    - unit_of_measure_id
    type: object
  handler.CreatePurchaseRequest:
    properties:
      currency:
        type: string
      items:
        items:
          $ref: '#/definitions/handler.CreatePurchaseItemRequest'
        minItems: 1
        type: array
      notes:
        type: string
      pantry_id:
        type: string
      purchase_date:
        type: string
      receipt_image_url:
        type: string
      store_id:
        type: string
      store_name:
        type: string
      total_amount:
        minimum: 0
        type: number
    required:
    - currency
    - items
    - pantry_id
    - purchase_date
    - total_amount
    type: object
  handler.ErrorInfo:
    properties:
      code:
        type: string
      details:
        additionalProperties: true
        type: object
      message:
        type: string
    type: object
  handler.InventoryItemResponse:
    properties:
      category_name:
        example: Dairy
        type: string
      created_at:
        example: "2024-01-15T10:30:00Z"
        type: string
      expiration_date:
        example: "2024-02-15"
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-446655440002
        type: string
      location:
        $ref: '#/definitions/handler.PantryLocationResponse'
      location_id:
        example: 550e8400-e29b-41d4-a716-446655440005
        type: string
      location_name:
        example: Main Fridge
        type: string
      notes:
        example: Organic brand
        type: string
      pantry_id:
        example: 550e8400-e29b-41d4-a716-446655440001
        type: string
      pantry_name:
        example: Main Kitchen
        type: string
      product_brand:
        example: Organic Valley
        type: string
      product_name:
        example: Organic Milk
        type: string
      product_variant:
        allOf:
        - $ref: '#/definitions/handler.ProductVariantResponse'
        description: Optional nested data (for backward compatibility)
      product_variant_id:
        example: 550e8400-e29b-41d4-a716-446655440003
        type: string
      purchase_date:
        example: "2024-01-15"
        type: string
      purchase_price:
        example: 12.99
        type: number
      quantity:
        example: 5
        type: number
      status:
        example: fresh
        type: string
      unit_name:
        example: pieces
        type: string
      unit_of_measure:
        $ref: '#/definitions/handler.UnitOfMeasureResponse'
      unit_of_measure_id:
        example: 550e8400-e29b-41d4-a716-446655440004
        type: string
      unit_symbol:
        example: pcs
        type: string
      updated_at:
        example: "2024-01-15T10:30:00Z"
        type: string
      variant_image_url:
        example: https://example.com/images/milk-1l.jpg
        type: string
      variant_name:
        example: 1 Liter
        type: string
    type: object
  handler.PaginatedResponse:
    properties:
      data: {}
      error:
        $ref: '#/definitions/handler.ErrorInfo'
      message:
        type: string
      metadata:
        additionalProperties: true
        type: object
      pagination:
        $ref: '#/definitions/handler.PaginationMeta'
      request_id:
        type: string
      success:
        type: boolean
      timestamp:
        type: string
    type: object
  handler.PaginationMeta:
    properties:
      has_next:
        type: boolean
      has_prev:
        type: boolean
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  handler.PantryLocationResponse:
    properties:
      created_at:
        example: "2024-01-15T10:30:00Z"
        type: string
      description:
        example: Main refrigerator
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-446655440005
        type: string
      name:
        example: Main Fridge
        type: string
      pantry_id:
        example: 550e8400-e29b-41d4-a716-446655440001
        type: string
      updated_at:
        example: "2024-01-15T10:30:00Z"
        type: string
    type: object
  handler.ProductResponse:
    properties:
      brand:
        example: Organic Valley
        type: string
      category:
        $ref: '#/definitions/handler.CategoryResponse'
      category_id:
        example: 550e8400-e29b-41d4-a716-446655440007
        type: string
      created_at:
        example: "2024-01-15T10:30:00Z"
        type: string
      description:
        example: Fresh organic whole milk
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-446655440006
        type: string
      name:
        example: Organic Milk
        type: string
      updated_at:
        example: "2024-01-15T10:30:00Z"
        type: string
    type: object
  handler.ProductVariantResponse:
    properties:
      barcode:
        example: "1234567890123"
        type: string
      created_at:
        example: "2024-01-15T10:30:00Z"
        type: string
      description:
        example: 1 liter bottle
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-446655440003
        type: string
      name:
        example: 1 Liter
        type: string
      product:
        $ref: '#/definitions/handler.ProductResponse'
      product_id:
        example: 550e8400-e29b-41d4-a716-446655440006
        type: string
      size:
        example: 1
        type: number
      size_unit:
        example: L
        type: string
      sku:
        example: MILK-1L-001
        type: string
      updated_at:
        example: "2024-01-15T10:30:00Z"
        type: string
    type: object
  handler.UnitOfMeasureResponse:
    properties:
      base_unit_id:
        example: 550e8400-e29b-41d4-a716-446655440009
        type: string
      conversion_factor:
        example: 1
        type: number
      created_at:
        example: "2024-01-15T10:30:00Z"
        type: string
      id:
        example: 550e8400-e29b-41d4-a716-446655440004
        type: string
      name:
        example: pieces
        type: string
      symbol:
        example: pcs
        type: string
      type:
        example: count
        type: string
      updated_at:
        example: "2024-01-15T10:30:00Z"
        type: string
    type: object
  usecases.CreateStoreRequest:
    properties:
      address:
        type: string
      city:
        type: string
      country:
        type: string
      name:
        type: string
      phone_number:
        type: string
      website:
        type: string
    required:
    - name
    type: object
  usecases.UpdateStoreRequest:
    properties:
      address:
        type: string
      city:
        type: string
      country:
        type: string
      name:
        type: string
      phone_number:
        type: string
      website:
        type: string
    required:
    - name
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: Pantry Pal API Support
    url: http://www.pantrypal.com/support
  description: A comprehensive multi-tenant pantry management system for tracking
    inventory, managing shopping lists, and reducing food waste.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Pantry Pal API
  version: "2.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user with email and password
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.LoginCredentials'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Invalid credentials
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: User login
      tags:
      - Authentication
  /auth/logout:
    post:
      consumes:
      - application/json
      description: Logout user and revoke refresh token
      produces:
      - application/json
      responses:
        "200":
          description: Logout successful
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: User logout
      tags:
      - Authentication
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Generate new access token using refresh token from cookie
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Refresh token not found or invalid
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Refresh access token
      tags:
      - Authentication
  /auth/register:
    post:
      consumes:
      - application/json
      description: Create a new user account with email, username, and password
      parameters:
      - description: Registration credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.RegisterCredentials'
      produces:
      - application/json
      responses:
        "201":
          description: User registered successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "409":
          description: Email or username already exists
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Register a new user
      tags:
      - Authentication
  /expiration/alerts/global:
    get:
      consumes:
      - application/json
      description: Retrieve expiration alert configuration for a pantry or globally
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get alert configuration
      tags:
      - Expiration Tracking
    post:
      consumes:
      - application/json
      description: Configure expiration alert settings for a pantry or globally
      parameters:
      - description: Alert configuration
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.AlertConfigurationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration saved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Configure expiration alerts
      tags:
      - Expiration Tracking
  /inventory/{itemId}:
    get:
      consumes:
      - application/json
      description: Retrieve a specific inventory item by ID. Returns enhanced inventory
        item with human-readable names for all related entities.
      parameters:
      - description: Inventory item ID
        in: path
        name: itemId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Inventory item retrieved successfully with enhanced response
            including names
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/handler.InventoryItemResponse'
              type: object
        "400":
          description: Invalid item ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to item
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Inventory item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get inventory item
      tags:
      - Inventory
    put:
      consumes:
      - application/json
      description: Update an existing inventory item. Returns enhanced inventory item
        with human-readable names for all related entities.
      parameters:
      - description: Inventory item ID
        in: path
        name: itemId
        required: true
        type: string
      - description: Updated inventory item data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateInventoryItemRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Inventory item updated successfully with enhanced response
            including names
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/handler.InventoryItemResponse'
              type: object
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to item
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Inventory item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update inventory item
      tags:
      - Inventory
  /inventory/{itemId}/consume:
    post:
      consumes:
      - application/json
      description: Consume a specified quantity from an inventory item. Returns enhanced
        inventory item with human-readable names for all related entities.
      parameters:
      - description: Inventory item ID
        in: path
        name: itemId
        required: true
        type: string
      - description: Consumption data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ConsumeInventoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Inventory item consumed successfully with enhanced response
            including names
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/handler.InventoryItemResponse'
              type: object
        "400":
          description: Invalid input or insufficient quantity
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to item
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Inventory item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Consume inventory item
      tags:
      - Inventory
  /pantries:
    get:
      consumes:
      - application/json
      description: Retrieve all pantries accessible to the authenticated user with
        pagination
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      - description: 'Only return owned pantries (default: false)'
        in: query
        name: owner_only
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Pantries retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user's pantries
      tags:
      - Pantries
    post:
      consumes:
      - application/json
      description: Create a new pantry for the authenticated user
      parameters:
      - description: Pantry creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreatePantryRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Pantry created successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "409":
          description: Pantry name already exists
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Create a new pantry
      tags:
      - Pantries
  /pantries/{pantryId}:
    delete:
      consumes:
      - application/json
      description: Delete a pantry and all its associated data
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Pantry deleted successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no delete permission
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete pantry
      tags:
      - Pantries
    get:
      consumes:
      - application/json
      description: Retrieve a specific pantry by its ID
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Pantry retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to this pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get pantry by ID
      tags:
      - Pantries
    put:
      consumes:
      - application/json
      description: Update pantry name and description
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Pantry update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdatePantryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Pantry updated successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no edit permission
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "409":
          description: Pantry name already exists
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update pantry
      tags:
      - Pantries
  /pantries/{pantryId}/expiration/alerts:
    get:
      consumes:
      - application/json
      description: Retrieve expiration alert configuration for a pantry or globally
      parameters:
      - description: Pantry ID (optional for global config)
        in: path
        name: pantryId
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid pantry ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get alert configuration
      tags:
      - Expiration Tracking
    post:
      consumes:
      - application/json
      description: Configure expiration alert settings for a pantry or globally
      parameters:
      - description: Pantry ID (optional for global config)
        in: path
        name: pantryId
        type: string
      - description: Alert configuration
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.AlertConfigurationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Alert configuration saved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Configure expiration alerts
      tags:
      - Expiration Tracking
  /pantries/{pantryId}/expiration/track:
    post:
      consumes:
      - application/json
      description: Track and analyze expiring items in a pantry with configurable
        thresholds
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Expiration tracking configuration
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ExpirationTrackingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Expiring items tracked successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Track expiring items
      tags:
      - Expiration Tracking
  /pantries/{pantryId}/inventory:
    get:
      consumes:
      - application/json
      description: Retrieve all inventory items in the specified pantry with pagination.
        Returns enhanced inventory items with human-readable names for all related
        entities.
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Inventory retrieved successfully with enhanced response including
            names
          schema:
            allOf:
            - $ref: '#/definitions/handler.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/handler.InventoryItemResponse'
                  type: array
              type: object
        "400":
          description: Invalid pantry ID or query parameters
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get pantry inventory
      tags:
      - Inventory
    post:
      consumes:
      - application/json
      description: Create a new inventory item in the specified pantry. Returns enhanced
        inventory item with human-readable names for all related entities.
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Inventory item data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateInventoryItemRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Inventory item created successfully with enhanced response
            including names
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/handler.InventoryItemResponse'
              type: object
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry or product variant not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Create inventory item
      tags:
      - Inventory
  /pantries/{pantryId}/shopping-lists:
    get:
      consumes:
      - application/json
      description: Get all shopping lists for a pantry with optional filtering
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Filter by status (active, completed, archived)
        in: query
        name: status
        type: string
      - description: Filter by creator user ID
        in: query
        name: created_by
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 20, max: 100)'
        in: query
        name: limit
        type: integer
      - description: Sort field (created_at, updated_at, name)
        in: query
        name: sort_by
        type: string
      - description: Sort direction (asc, desc)
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Shopping lists retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.ShoppingList'
                  type: array
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get shopping lists
      tags:
      - Shopping Lists
    post:
      consumes:
      - application/json
      description: Create a new shopping list in a pantry
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateShoppingListRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Shopping list created successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingList'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Create shopping list
      tags:
      - Shopping Lists
  /pantries/{pantryId}/shopping-lists/{listId}:
    delete:
      consumes:
      - application/json
      description: Delete a shopping list and all its items
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Shopping list deleted successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Shopping list not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete shopping list
      tags:
      - Shopping Lists
    get:
      consumes:
      - application/json
      description: Get a specific shopping list by ID
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Shopping list retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingList'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Shopping list not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get shopping list
      tags:
      - Shopping Lists
    put:
      consumes:
      - application/json
      description: Update shopping list details
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Updated shopping list data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateShoppingListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Shopping list updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingList'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Shopping list not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update shopping list
      tags:
      - Shopping Lists
  /pantries/{pantryId}/shopping-lists/{listId}/items:
    post:
      consumes:
      - application/json
      description: Add an item to a shopping list
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Item data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.AddShoppingListItemRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Item added successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingListItemEntity'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Shopping list not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Add item to shopping list
      tags:
      - Shopping List Items
  /pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}:
    delete:
      consumes:
      - application/json
      description: Remove an item from a shopping list
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Item ID
        in: path
        name: itemId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: Item removed successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Remove item from shopping list
      tags:
      - Shopping List Items
    put:
      consumes:
      - application/json
      description: Update a shopping list item
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Item ID
        in: path
        name: itemId
        required: true
        type: string
      - description: Updated item data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateShoppingListItemRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Item updated successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingListItemEntity'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update shopping list item
      tags:
      - Shopping List Items
  /pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}/purchased:
    delete:
      consumes:
      - application/json
      description: Mark a shopping list item as not purchased
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Item ID
        in: path
        name: itemId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Item marked as not purchased
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingListItemEntity'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Mark item as not purchased
      tags:
      - Shopping List Items
    patch:
      consumes:
      - application/json
      description: Mark a shopping list item as purchased
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Item ID
        in: path
        name: itemId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Item marked as purchased
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingListItemEntity'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Item not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Mark item as purchased
      tags:
      - Shopping List Items
  /pantries/{pantryId}/shopping-lists/{listId}/items/bulk-purchased:
    delete:
      consumes:
      - application/json
      description: Mark multiple shopping list items as not purchased
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Item IDs to mark as not purchased
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.BulkMarkItemsNotPurchasedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Items marked as not purchased successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Items not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Bulk mark items as not purchased
      tags:
      - Shopping List Items
    patch:
      consumes:
      - application/json
      description: Mark multiple shopping list items as purchased
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Item IDs to mark as purchased
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.BulkMarkItemsPurchasedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Items marked as purchased successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Items not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Bulk mark items as purchased
      tags:
      - Shopping List Items
  /pantries/{pantryId}/shopping-lists/{listId}/stats:
    get:
      consumes:
      - application/json
      description: Get completion statistics for a specific shopping list
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Statistics retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingListStats'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Shopping list not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get shopping list statistics
      tags:
      - Shopping List Statistics
  /pantries/{pantryId}/shopping-lists/{listId}/status:
    patch:
      consumes:
      - application/json
      description: Change the status of a shopping list (active, completed, archived)
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      - description: Shopping list ID
        in: path
        name: listId
        required: true
        type: string
      - description: Status change data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ChangeShoppingListStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Shopping list status changed successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.ShoppingList'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Shopping list not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Change shopping list status
      tags:
      - Shopping Lists
  /pantries/{pantryId}/shopping-lists/stats:
    get:
      consumes:
      - application/json
      description: Get aggregated statistics for all shopping lists in a pantry
      parameters:
      - description: Pantry ID
        in: path
        name: pantryId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Statistics retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.PantryShoppingListStats'
              type: object
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Pantry not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get pantry shopping list statistics
      tags:
      - Shopping List Statistics
  /purchases:
    post:
      consumes:
      - application/json
      description: Creates a new purchase with items
      parameters:
      - description: Purchase details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.CreatePurchaseRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.Purchase'
              type: object
        "400":
          description: Bad Request
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
        "403":
          description: Forbidden
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
      summary: Create a new purchase
      tags:
      - Purchases
  /purchases/{id}:
    delete:
      description: Deletes a purchase by ID
      parameters:
      - description: Purchase ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "403":
          description: Forbidden
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
        "404":
          description: Not Found
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
      summary: Delete purchase
      tags:
      - Purchases
    get:
      description: Retrieves a specific purchase by ID
      parameters:
      - description: Purchase ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.Purchase'
              type: object
        "403":
          description: Forbidden
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
        "404":
          description: Not Found
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
      summary: Get purchase by ID
      tags:
      - Purchases
  /purchases/{id}/inventory:
    post:
      description: Creates inventory items from a purchase
      parameters:
      - description: Purchase ID
        in: path
        name: id
        required: true
        type: string
      - description: Location ID
        in: query
        name: location_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
        "404":
          description: Not Found
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
      summary: Link purchase to inventory
      tags:
      - Purchases
  /purchases/pantry/{pantry_id}:
    get:
      description: Retrieves purchases for a specific pantry with pagination
      parameters:
      - description: Pantry ID
        in: path
        name: pantry_id
        required: true
        type: string
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handler.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.Purchase'
                  type: array
              type: object
        "403":
          description: Forbidden
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                error:
                  $ref: '#/definitions/handler.ErrorInfo'
              type: object
      summary: Get pantry purchases
      tags:
      - Purchases
  /recipes:
    get:
      consumes:
      - application/json
      description: Retrieve all recipes created by the authenticated user with pagination
        and filtering
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      - description: Search in recipe title and description
        in: query
        name: search
        type: string
      - description: Filter by cuisine
        in: query
        name: cuisine
        type: string
      - description: Filter by category
        in: query
        name: category
        type: string
      - description: Filter by difficulty (easy, medium, hard, expert)
        in: query
        name: difficulty
        type: string
      - description: Maximum preparation time in minutes
        in: query
        name: max_prep_time
        type: integer
      - description: Maximum cooking time in minutes
        in: query
        name: max_cook_time
        type: integer
      - description: Maximum calories per serving
        in: query
        name: max_calories
        type: integer
      - description: Filter favorite recipes only
        in: query
        name: is_favorite
        type: boolean
      - description: Sort by field (created_at, updated_at, title, rating, cook_count)
        in: query
        name: sort_by
        type: string
      - description: Sort order (asc, desc)
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Recipes retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user's recipes
      tags:
      - Recipes
    post:
      consumes:
      - application/json
      description: Create a new recipe with ingredients, instructions, and optional
        media
      parameters:
      - description: Recipe creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateRecipeRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Recipe created successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Create a new recipe
      tags:
      - Recipes
  /recipes/{recipeId}:
    delete:
      consumes:
      - application/json
      description: Delete a recipe and all its associated data
      parameters:
      - description: Recipe ID
        in: path
        name: recipeId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Recipe deleted successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid recipe ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - not recipe owner
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Recipe not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete recipe
      tags:
      - Recipes
    get:
      consumes:
      - application/json
      description: Retrieve a specific recipe by its ID
      parameters:
      - description: Recipe ID
        in: path
        name: recipeId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Recipe retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid recipe ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to this recipe
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Recipe not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get recipe by ID
      tags:
      - Recipes
    put:
      consumes:
      - application/json
      description: Update an existing recipe
      parameters:
      - description: Recipe ID
        in: path
        name: recipeId
        required: true
        type: string
      - description: Recipe update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateRecipeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Recipe updated successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - not recipe owner
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Recipe not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update recipe
      tags:
      - Recipes
  /recipes/{recipeId}/check-inventory:
    post:
      consumes:
      - application/json
      description: Check if recipe ingredients are available in pantry inventory
      parameters:
      - description: Recipe ID
        in: path
        name: recipeId
        required: true
        type: string
      - description: Inventory check data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.InventoryCheckRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Inventory availability checked successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to recipe or pantry
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Recipe not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Check ingredient availability
      tags:
      - Recipes
  /recipes/{recipeId}/cook:
    post:
      consumes:
      - application/json
      description: Mark a recipe as cooked to track cooking frequency
      parameters:
      - description: Recipe ID
        in: path
        name: recipeId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Recipe marked as cooked successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid recipe ID
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to recipe
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Recipe not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Mark recipe as cooked
      tags:
      - Recipes
  /recipes/{recipeId}/scale:
    post:
      consumes:
      - application/json
      description: Scale a recipe to a different number of servings
      parameters:
      - description: Recipe ID
        in: path
        name: recipeId
        required: true
        type: string
      - description: Scaling data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ScaleRecipeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Recipe scaled successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "403":
          description: Forbidden - no access to recipe
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Recipe not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Scale recipe
      tags:
      - Recipes
  /recipes/public:
    get:
      consumes:
      - application/json
      description: Retrieve all public recipes with pagination and filtering
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      - description: Search in recipe title and description
        in: query
        name: search
        type: string
      - description: Filter by cuisine
        in: query
        name: cuisine
        type: string
      - description: Filter by category
        in: query
        name: category
        type: string
      - description: Filter by difficulty (easy, medium, hard, expert)
        in: query
        name: difficulty
        type: string
      - description: Maximum preparation time in minutes
        in: query
        name: max_prep_time
        type: integer
      - description: Maximum cooking time in minutes
        in: query
        name: max_cook_time
        type: integer
      - description: Maximum calories per serving
        in: query
        name: max_calories
        type: integer
      - description: Sort by field (created_at, updated_at, title, rating, cook_count)
        in: query
        name: sort_by
        type: string
      - description: Sort order (asc, desc)
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Public recipes retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Get public recipes
      tags:
      - Recipes
  /recipes/search:
    get:
      consumes:
      - application/json
      description: Search for recipes by title, description, ingredients, or tags
      parameters:
      - description: Search query
        in: query
        name: q
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10, max: 100)'
        in: query
        name: limit
        type: integer
      - description: Filter by cuisine
        in: query
        name: cuisine
        type: string
      - description: Filter by category
        in: query
        name: category
        type: string
      - description: Filter by difficulty (easy, medium, hard, expert)
        in: query
        name: difficulty
        type: string
      - description: Maximum preparation time in minutes
        in: query
        name: max_prep_time
        type: integer
      - description: Maximum cooking time in minutes
        in: query
        name: max_cook_time
        type: integer
      - description: Maximum calories per serving
        in: query
        name: max_calories
        type: integer
      - description: Sort by field (created_at, updated_at, title, rating, cook_count)
        in: query
        name: sort_by
        type: string
      - description: Sort order (asc, desc)
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Recipes found successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Search recipes
      tags:
      - Recipes
  /stores:
    get:
      description: Get a paginated list of stores
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Results per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handler.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.Store'
                  type: array
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Get all stores
      tags:
      - stores
    post:
      consumes:
      - application/json
      description: Creates a new store with the given details
      parameters:
      - description: Store details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/usecases.CreateStoreRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.Store'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Create a new store
      tags:
      - stores
  /stores/{id}:
    delete:
      description: Soft delete a store by its ID
      parameters:
      - description: Store ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Delete a store
      tags:
      - stores
    get:
      description: Get a store's details by its ID
      parameters:
      - description: Store ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.Store'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Get a store by ID
      tags:
      - stores
    put:
      consumes:
      - application/json
      description: Update a store's details
      parameters:
      - description: Store ID
        in: path
        name: id
        required: true
        type: string
      - description: Store details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/usecases.UpdateStoreRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/domain.Store'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Update a store
      tags:
      - stores
  /stores/search:
    get:
      description: Search for stores by name
      parameters:
      - description: Search query
        in: query
        name: query
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handler.APIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/domain.Store'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      summary: Search stores
      tags:
      - stores
  /users/change-password:
    post:
      consumes:
      - application/json
      description: Change the current authenticated user's password
      parameters:
      - description: Password change data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Password changed successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized or invalid current password
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Change user password
      tags:
      - Users
  /users/profile:
    get:
      consumes:
      - application/json
      description: Retrieve the current authenticated user's profile information
      produces:
      - application/json
      responses:
        "200":
          description: Profile retrieved successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - Users
    put:
      consumes:
      - application/json
      description: Update the current authenticated user's profile information
      parameters:
      - description: Profile update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Profile updated successfully
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "400":
          description: Invalid input or validation error
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/handler.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/handler.APIResponse'
      security:
      - BearerAuth: []
      summary: Update user profile
      tags:
      - Users
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
