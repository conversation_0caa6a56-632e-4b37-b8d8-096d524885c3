{"swagger": "2.0", "info": {"description": "A comprehensive multi-tenant pantry management system for tracking inventory, managing shopping lists, and reducing food waste.", "title": "Pantry Pal API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "Pantry Pal API Support", "url": "http://www.pantrypal.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "2.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/auth/login": {"post": {"description": "Authenticate user with email and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User login", "parameters": [{"description": "Login credentials", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.LoginCredentials"}}], "responses": {"200": {"description": "Login successful", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Invalid credentials", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/auth/logout": {"post": {"description": "Logout user and revoke refresh token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User logout", "responses": {"200": {"description": "Logout successful", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/auth/refresh": {"post": {"description": "Generate new access token using refresh token from cookie", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Refresh access token", "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Refresh token not found or invalid", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/auth/register": {"post": {"description": "Create a new user account with email, username, and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Register a new user", "parameters": [{"description": "Registration credentials", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.RegisterCredentials"}}], "responses": {"201": {"description": "User registered successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "409": {"description": "Email or username already exists", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/expiration/alerts/global": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve expiration alert configuration for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Get alert configuration", "responses": {"200": {"description": "Alert configuration retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Configure expiration alert settings for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Configure expiration alerts", "parameters": [{"description": "Alert configuration", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.AlertConfigurationRequest"}}], "responses": {"200": {"description": "Alert configuration saved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/inventory/{itemId}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a specific inventory item by ID. Returns enhanced inventory item with human-readable names for all related entities.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Get inventory item", "parameters": [{"type": "string", "description": "Inventory item ID", "name": "itemId", "in": "path", "required": true}], "responses": {"200": {"description": "Inventory item retrieved successfully with enhanced response including names", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/handler.InventoryItemResponse"}}}]}}, "400": {"description": "Invalid item ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to item", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Inventory item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing inventory item. Returns enhanced inventory item with human-readable names for all related entities.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Update inventory item", "parameters": [{"type": "string", "description": "Inventory item ID", "name": "itemId", "in": "path", "required": true}, {"description": "Updated inventory item data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateInventoryItemRequest"}}], "responses": {"200": {"description": "Inventory item updated successfully with enhanced response including names", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/handler.InventoryItemResponse"}}}]}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to item", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Inventory item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/inventory/{itemId}/consume": {"post": {"security": [{"BearerAuth": []}], "description": "Consume a specified quantity from an inventory item. Returns enhanced inventory item with human-readable names for all related entities.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Consume inventory item", "parameters": [{"type": "string", "description": "Inventory item ID", "name": "itemId", "in": "path", "required": true}, {"description": "Consumption data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ConsumeInventoryRequest"}}], "responses": {"200": {"description": "Inventory item consumed successfully with enhanced response including names", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/handler.InventoryItemResponse"}}}]}}, "400": {"description": "Invalid input or insufficient quantity", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to item", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Inventory item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all pantries accessible to the authenticated user with pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Get user's pantries", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "boolean", "description": "Only return owned pantries (default: false)", "name": "owner_only", "in": "query"}], "responses": {"200": {"description": "Pantries retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new pantry for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Create a new pantry", "parameters": [{"description": "Pantry creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreatePantryRequest"}}], "responses": {"201": {"description": "Pantry created successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "409": {"description": "Pantry name already exists", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a specific pantry by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Get pantry by ID", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}], "responses": {"200": {"description": "Pantry retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to this pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update pantry name and description", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Update pantry", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"description": "Pantry update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdatePantryRequest"}}], "responses": {"200": {"description": "Pantry updated successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no edit permission", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "409": {"description": "Pantry name already exists", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a pantry and all its associated data", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Pantries"], "summary": "Delete pantry", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}], "responses": {"200": {"description": "Pantry deleted successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no delete permission", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/expiration/alerts": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve expiration alert configuration for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Get alert configuration", "parameters": [{"type": "string", "description": "Pantry ID (optional for global config)", "name": "pantryId", "in": "path"}], "responses": {"200": {"description": "Alert configuration retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid pantry ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Configure expiration alert settings for a pantry or globally", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Configure expiration alerts", "parameters": [{"type": "string", "description": "Pantry ID (optional for global config)", "name": "pantryId", "in": "path"}, {"description": "Alert configuration", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.AlertConfigurationRequest"}}], "responses": {"200": {"description": "Alert configuration saved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/expiration/track": {"post": {"security": [{"BearerAuth": []}], "description": "Track and analyze expiring items in a pantry with configurable thresholds", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Expiration Tracking"], "summary": "Track expiring items", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"description": "Expiration tracking configuration", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ExpirationTrackingRequest"}}], "responses": {"200": {"description": "Expiring items tracked successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/inventory": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all inventory items in the specified pantry with pagination. Returns enhanced inventory items with human-readable names for all related entities.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Get pantry inventory", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Inventory retrieved successfully with enhanced response including names", "schema": {"allOf": [{"$ref": "#/definitions/handler.PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/handler.InventoryItemResponse"}}}}]}}, "400": {"description": "Invalid pantry ID or query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new inventory item in the specified pantry. Returns enhanced inventory item with human-readable names for all related entities.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Inventory"], "summary": "Create inventory item", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"description": "Inventory item data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateInventoryItemRequest"}}], "responses": {"201": {"description": "Inventory item created successfully with enhanced response including names", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/handler.InventoryItemResponse"}}}]}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry or product variant not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists": {"get": {"security": [{"BearerAuth": []}], "description": "Get all shopping lists for a pantry with optional filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping Lists"], "summary": "Get shopping lists", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Filter by status (active, completed, archived)", "name": "status", "in": "query"}, {"type": "string", "description": "Filter by creator user ID", "name": "created_by", "in": "query"}, {"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 20, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "description": "Sort field (created_at, updated_at, name)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort direction (asc, desc)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "Shopping lists retrieved successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.ShoppingList"}}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new shopping list in a pantry", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping Lists"], "summary": "Create shopping list", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"description": "Shopping list data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateShoppingListRequest"}}], "responses": {"201": {"description": "Shopping list created successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingList"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/stats": {"get": {"security": [{"BearerAuth": []}], "description": "Get aggregated statistics for all shopping lists in a pantry", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Statistics"], "summary": "Get pantry shopping list statistics", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}], "responses": {"200": {"description": "Statistics retrieved successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.PantryShoppingListStats"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Pantry not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/{listId}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a specific shopping list by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping Lists"], "summary": "Get shopping list", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}], "responses": {"200": {"description": "Shopping list retrieved successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingList"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Shopping list not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update shopping list details", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping Lists"], "summary": "Update shopping list", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"description": "Updated shopping list data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateShoppingListRequest"}}], "responses": {"200": {"description": "Shopping list updated successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingList"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Shopping list not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a shopping list and all its items", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping Lists"], "summary": "Delete shopping list", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}], "responses": {"204": {"description": "Shopping list deleted successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Shopping list not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/{listId}/items": {"post": {"security": [{"BearerAuth": []}], "description": "Add an item to a shopping list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Items"], "summary": "Add item to shopping list", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"description": "Item data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.AddShoppingListItemRequest"}}], "responses": {"201": {"description": "Item added successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingListItemEntity"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Shopping list not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/{listId}/items/bulk-purchased": {"delete": {"security": [{"BearerAuth": []}], "description": "Mark multiple shopping list items as not purchased", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Items"], "summary": "Bulk mark items as not purchased", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"description": "Item IDs to mark as not purchased", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.BulkMarkItemsNotPurchasedRequest"}}], "responses": {"200": {"description": "Items marked as not purchased successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Items not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "patch": {"security": [{"BearerAuth": []}], "description": "Mark multiple shopping list items as purchased", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Items"], "summary": "Bulk mark items as purchased", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"description": "Item IDs to mark as purchased", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.BulkMarkItemsPurchasedRequest"}}], "responses": {"200": {"description": "Items marked as purchased successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Items not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}": {"put": {"security": [{"BearerAuth": []}], "description": "Update a shopping list item", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Items"], "summary": "Update shopping list item", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"type": "string", "description": "Item ID", "name": "itemId", "in": "path", "required": true}, {"description": "Updated item data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateShoppingListItemRequest"}}], "responses": {"200": {"description": "Item updated successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingListItemEntity"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Remove an item from a shopping list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Items"], "summary": "Remove item from shopping list", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"type": "string", "description": "Item ID", "name": "itemId", "in": "path", "required": true}], "responses": {"204": {"description": "<PERSON><PERSON> removed successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/{listId}/items/{itemId}/purchased": {"delete": {"security": [{"BearerAuth": []}], "description": "Mark a shopping list item as not purchased", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Items"], "summary": "Mark item as not purchased", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"type": "string", "description": "Item ID", "name": "itemId", "in": "path", "required": true}], "responses": {"200": {"description": "Item marked as not purchased", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingListItemEntity"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "patch": {"security": [{"BearerAuth": []}], "description": "Mark a shopping list item as purchased", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Items"], "summary": "Mark item as purchased", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"type": "string", "description": "Item ID", "name": "itemId", "in": "path", "required": true}], "responses": {"200": {"description": "Item marked as purchased", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingListItemEntity"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Item not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/{listId}/stats": {"get": {"security": [{"BearerAuth": []}], "description": "Get completion statistics for a specific shopping list", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping List Statistics"], "summary": "Get shopping list statistics", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}], "responses": {"200": {"description": "Statistics retrieved successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingListStats"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Shopping list not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/pantries/{pantryId}/shopping-lists/{listId}/status": {"patch": {"security": [{"BearerAuth": []}], "description": "Change the status of a shopping list (active, completed, archived)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Shopping Lists"], "summary": "Change shopping list status", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantryId", "in": "path", "required": true}, {"type": "string", "description": "Shopping list ID", "name": "listId", "in": "path", "required": true}, {"description": "Status change data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ChangeShoppingListStatusRequest"}}], "responses": {"200": {"description": "Shopping list status changed successfully", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.ShoppingList"}}}]}}, "400": {"description": "Invalid request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Shopping list not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/purchases": {"post": {"description": "Creates a new purchase with items", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Purchases"], "summary": "Create a new purchase", "parameters": [{"description": "Purchase details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handler.CreatePurchaseRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.Purchase"}}}]}}, "400": {"description": "Bad Request", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}, "403": {"description": "Forbidden", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}}}}, "/purchases/pantry/{pantry_id}": {"get": {"description": "Retrieves purchases for a specific pantry with pagination", "produces": ["application/json"], "tags": ["Purchases"], "summary": "Get pantry purchases", "parameters": [{"type": "string", "description": "Pantry ID", "name": "pantry_id", "in": "path", "required": true}, {"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/handler.PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.Purchase"}}}}]}}, "403": {"description": "Forbidden", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}}}}, "/purchases/{id}": {"get": {"description": "Retrieves a specific purchase by ID", "produces": ["application/json"], "tags": ["Purchases"], "summary": "Get purchase by ID", "parameters": [{"type": "string", "description": "Purchase ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.Purchase"}}}]}}, "403": {"description": "Forbidden", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}, "404": {"description": "Not Found", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}}}, "delete": {"description": "Deletes a purchase by ID", "tags": ["Purchases"], "summary": "Delete purchase", "parameters": [{"type": "string", "description": "Purchase ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "403": {"description": "Forbidden", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}, "404": {"description": "Not Found", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}}}}, "/purchases/{id}/inventory": {"post": {"description": "Creates inventory items from a purchase", "produces": ["application/json"], "tags": ["Purchases"], "summary": "Link purchase to inventory", "parameters": [{"type": "string", "description": "Purchase ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Location ID", "name": "location_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}, "404": {"description": "Not Found", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"error": {"$ref": "#/definitions/handler.ErrorInfo"}}}]}}}}}, "/recipes": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all recipes created by the authenticated user with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Get user's recipes", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "description": "Search in recipe title and description", "name": "search", "in": "query"}, {"type": "string", "description": "Filter by cuisine", "name": "cuisine", "in": "query"}, {"type": "string", "description": "Filter by category", "name": "category", "in": "query"}, {"type": "string", "description": "Filter by difficulty (easy, medium, hard, expert)", "name": "difficulty", "in": "query"}, {"type": "integer", "description": "Maximum preparation time in minutes", "name": "max_prep_time", "in": "query"}, {"type": "integer", "description": "Maximum cooking time in minutes", "name": "max_cook_time", "in": "query"}, {"type": "integer", "description": "Maximum calories per serving", "name": "max_calories", "in": "query"}, {"type": "boolean", "description": "Filter favorite recipes only", "name": "is_favorite", "in": "query"}, {"type": "string", "description": "Sort by field (created_at, updated_at, title, rating, cook_count)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort order (asc, desc)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "Recipes retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new recipe with ingredients, instructions, and optional media", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Create a new recipe", "parameters": [{"description": "Recipe creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateRecipeRequest"}}], "responses": {"201": {"description": "Recipe created successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/public": {"get": {"description": "Retrieve all public recipes with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Get public recipes", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "description": "Search in recipe title and description", "name": "search", "in": "query"}, {"type": "string", "description": "Filter by cuisine", "name": "cuisine", "in": "query"}, {"type": "string", "description": "Filter by category", "name": "category", "in": "query"}, {"type": "string", "description": "Filter by difficulty (easy, medium, hard, expert)", "name": "difficulty", "in": "query"}, {"type": "integer", "description": "Maximum preparation time in minutes", "name": "max_prep_time", "in": "query"}, {"type": "integer", "description": "Maximum cooking time in minutes", "name": "max_cook_time", "in": "query"}, {"type": "integer", "description": "Maximum calories per serving", "name": "max_calories", "in": "query"}, {"type": "string", "description": "Sort by field (created_at, updated_at, title, rating, cook_count)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort order (asc, desc)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "Public recipes retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/search": {"get": {"security": [{"BearerAuth": []}], "description": "Search for recipes by title, description, ingredients, or tags", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Search recipes", "parameters": [{"type": "string", "description": "Search query", "name": "q", "in": "query", "required": true}, {"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Items per page (default: 10, max: 100)", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by cuisine", "name": "cuisine", "in": "query"}, {"type": "string", "description": "Filter by category", "name": "category", "in": "query"}, {"type": "string", "description": "Filter by difficulty (easy, medium, hard, expert)", "name": "difficulty", "in": "query"}, {"type": "integer", "description": "Maximum preparation time in minutes", "name": "max_prep_time", "in": "query"}, {"type": "integer", "description": "Maximum cooking time in minutes", "name": "max_cook_time", "in": "query"}, {"type": "integer", "description": "Maximum calories per serving", "name": "max_calories", "in": "query"}, {"type": "string", "description": "Sort by field (created_at, updated_at, title, rating, cook_count)", "name": "sort_by", "in": "query"}, {"type": "string", "description": "Sort order (asc, desc)", "name": "sort_order", "in": "query"}], "responses": {"200": {"description": "Recipes found successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a specific recipe by its ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Get recipe by ID", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}], "responses": {"200": {"description": "Recipe retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid recipe ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to this recipe", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing recipe", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Update recipe", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}, {"description": "Recipe update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateRecipeRequest"}}], "responses": {"200": {"description": "Recipe updated successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - not recipe owner", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a recipe and all its associated data", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Delete recipe", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}], "responses": {"200": {"description": "Recipe deleted successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid recipe ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - not recipe owner", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}/check-inventory": {"post": {"security": [{"BearerAuth": []}], "description": "Check if recipe ingredients are available in pantry inventory", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Check ingredient availability", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}, {"description": "Inventory check data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.InventoryCheckRequest"}}], "responses": {"200": {"description": "Inventory availability checked successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to recipe or pantry", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}/cook": {"post": {"security": [{"BearerAuth": []}], "description": "Mark a recipe as cooked to track cooking frequency", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Mark recipe as cooked", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> marked as cooked successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid recipe ID", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to recipe", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/recipes/{recipeId}/scale": {"post": {"security": [{"BearerAuth": []}], "description": "Scale a recipe to a different number of servings", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Recipes"], "summary": "Scale recipe", "parameters": [{"type": "string", "description": "Recipe ID", "name": "recipeId", "in": "path", "required": true}, {"description": "Scaling data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ScaleRecipeRequest"}}], "responses": {"200": {"description": "Recipe scaled successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "403": {"description": "Forbidden - no access to recipe", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Recipe not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/stores": {"get": {"description": "Get a paginated list of stores", "produces": ["application/json"], "tags": ["stores"], "summary": "Get all stores", "parameters": [{"type": "integer", "description": "Page number (default: 1)", "name": "page", "in": "query"}, {"type": "integer", "description": "Results per page (default: 10)", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/handler.PaginatedResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.Store"}}}}]}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "post": {"description": "Creates a new store with the given details", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stores"], "summary": "Create a new store", "parameters": [{"description": "Store details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/usecases.CreateStoreRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.Store"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/stores/search": {"get": {"description": "Search for stores by name", "produces": ["application/json"], "tags": ["stores"], "summary": "Search stores", "parameters": [{"type": "string", "description": "Search query", "name": "query", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/domain.Store"}}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/stores/{id}": {"get": {"description": "Get a store's details by its ID", "produces": ["application/json"], "tags": ["stores"], "summary": "Get a store by ID", "parameters": [{"type": "string", "description": "Store ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.Store"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"description": "Update a store's details", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["stores"], "summary": "Update a store", "parameters": [{"type": "string", "description": "Store ID", "name": "id", "in": "path", "required": true}, {"description": "Store details", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/usecases.UpdateStoreRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/handler.APIResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/domain.Store"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "delete": {"description": "Soft delete a store by its ID", "tags": ["stores"], "summary": "Delete a store", "parameters": [{"type": "string", "description": "Store ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/users/change-password": {"post": {"security": [{"BearerAuth": []}], "description": "Change the current authenticated user's password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Change user password", "parameters": [{"description": "Password change data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.ChangePasswordRequest"}}], "responses": {"200": {"description": "Password changed successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized or invalid current password", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}, "/users/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve the current authenticated user's profile information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user profile", "responses": {"200": {"description": "Profile retrieved successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update the current authenticated user's profile information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Update user profile", "parameters": [{"description": "Profile update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.UpdateProfileRequest"}}], "responses": {"200": {"description": "Profile updated successfully", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "400": {"description": "Invalid input or validation error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "404": {"description": "User not found", "schema": {"$ref": "#/definitions/handler.APIResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/handler.APIResponse"}}}}}}, "definitions": {"domain.AddShoppingListItemRequest": {"type": "object", "required": ["quantity_desired"], "properties": {"free_text_name": {"type": "string", "maxLength": 255, "minLength": 1}, "notes": {"type": "string", "maxLength": 500}, "product_variant_id": {"type": "string"}, "quantity_desired": {"type": "number"}, "unit_of_measure_id": {"type": "string"}}}, "domain.AlertConfigurationRequest": {"type": "object", "required": ["channels"], "properties": {"alert_days": {"type": "integer", "maximum": 7, "minimum": 0}, "category_filters": {"type": "array", "items": {"type": "string"}}, "channels": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/domain.NotificationChannel"}}, "critical_days": {"type": "integer", "maximum": 1, "minimum": 0}, "enabled": {"type": "boolean"}, "min_value": {"type": "number", "minimum": 0}, "quiet_hours": {"$ref": "#/definitions/domain.QuietHours"}, "warning_days": {"type": "integer", "maximum": 30, "minimum": 1}}}, "domain.BulkMarkItemsNotPurchasedRequest": {"type": "object", "required": ["item_ids"], "properties": {"item_ids": {"type": "array", "minItems": 1, "items": {"type": "string"}}}}, "domain.BulkMarkItemsPurchasedRequest": {"type": "object", "required": ["item_ids"], "properties": {"item_ids": {"type": "array", "minItems": 1, "items": {"type": "string"}}}}, "domain.ChangePasswordRequest": {"type": "object", "required": ["confirm_password", "current_password", "new_password"], "properties": {"confirm_password": {"type": "string"}, "current_password": {"type": "string"}, "new_password": {"type": "string", "minLength": 8}}}, "domain.ChangeShoppingListStatusRequest": {"type": "object", "required": ["status"], "properties": {"status": {"enum": ["active", "completed", "archived"], "allOf": [{"$ref": "#/definitions/domain.ShoppingListStatus"}]}}}, "domain.ConsumeInventoryRequest": {"type": "object", "required": ["consumed_quantity"], "properties": {"consumed_quantity": {"type": "number"}, "notes": {"type": "string", "maxLength": 500}}}, "domain.CreateInventoryItemRequest": {"type": "object", "required": ["product_variant_id", "quantity", "unit_of_measure_id"], "properties": {"expiration_date": {"type": "string"}, "location_id": {"type": "string"}, "notes": {"type": "string", "maxLength": 1000}, "product_variant_id": {"type": "string"}, "purchase_date": {"type": "string"}, "purchase_price": {"type": "number", "minimum": 0}, "quantity": {"type": "number"}, "unit_of_measure_id": {"type": "string"}}}, "domain.CreatePantryRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 500}, "name": {"type": "string", "maxLength": 100, "minLength": 1}}}, "domain.CreateRecipeIngredientRequest": {"type": "object", "required": ["name", "quantity"], "properties": {"is_garnish": {"type": "boolean"}, "is_optional": {"type": "boolean"}, "name": {"type": "string", "maxLength": 200, "minLength": 1}, "notes": {"type": "string", "maxLength": 500}, "preparation": {"type": "string", "maxLength": 200}, "product_variant_id": {"type": "string"}, "quantity": {"type": "number"}, "unit": {"type": "string", "maxLength": 50}, "unit_of_measure_id": {"type": "string"}}}, "domain.CreateRecipeInstructionRequest": {"type": "object", "required": ["instruction"], "properties": {"duration": {"type": "integer", "maximum": 1440, "minimum": 0}, "instruction": {"type": "string", "maxLength": 2000, "minLength": 10}, "temperature": {"type": "integer", "maximum": 500, "minimum": -50}, "tips": {"type": "string", "maxLength": 1000}, "title": {"type": "string", "maxLength": 200}}}, "domain.CreateRecipeNutritionRequest": {"type": "object", "properties": {"calories": {"type": "integer", "maximum": 10000, "minimum": 0}, "carbohydrates": {"type": "number", "maximum": 1000, "minimum": 0}, "fat": {"type": "number", "maximum": 1000, "minimum": 0}, "fiber": {"type": "number", "maximum": 1000, "minimum": 0}, "protein": {"type": "number", "maximum": 1000, "minimum": 0}, "serving_size": {"type": "string", "maxLength": 100}, "sodium": {"type": "number", "maximum": 10000, "minimum": 0}, "sugar": {"type": "number", "maximum": 1000, "minimum": 0}}}, "domain.CreateRecipeRequest": {"type": "object", "required": ["ingredients", "instructions", "servings", "title"], "properties": {"category": {"type": "string", "maxLength": 100}, "cook_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "cuisine": {"type": "string", "maxLength": 100}, "description": {"type": "string", "maxLength": 1000}, "difficulty": {"enum": ["easy", "medium", "hard", "expert"], "allOf": [{"$ref": "#/definitions/domain.DifficultyLevel"}]}, "ingredients": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/domain.CreateRecipeIngredientRequest"}}, "instructions": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/domain.CreateRecipeInstructionRequest"}}, "is_public": {"type": "boolean"}, "notes": {"type": "string", "maxLength": 2000}, "nutrition": {"$ref": "#/definitions/domain.CreateRecipeNutritionRequest"}, "prep_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "servings": {"type": "integer", "maximum": 100, "minimum": 1}, "source": {"type": "string", "maxLength": 500}, "tags": {"type": "array", "items": {"type": "string"}}, "title": {"type": "string", "maxLength": 200, "minLength": 3}}}, "domain.CreateShoppingListRequest": {"type": "object", "required": ["name", "pantry_id"], "properties": {"description": {"type": "string", "maxLength": 1000}, "name": {"type": "string", "maxLength": 255, "minLength": 1}, "pantry_id": {"type": "string"}}}, "domain.DifficultyLevel": {"type": "string", "enum": ["easy", "medium", "hard", "expert"], "x-enum-varnames": ["DifficultyEasy", "DifficultyMedium", "DifficultyHard", "Diff<PERSON><PERSON>yExpert"]}, "domain.ExpirationTrackingRequest": {"type": "object", "properties": {"alert_days": {"type": "integer", "maximum": 7, "minimum": 0}, "category_ids": {"type": "array", "items": {"type": "string"}}, "channels": {"type": "array", "items": {"$ref": "#/definitions/domain.NotificationChannel"}}, "critical_days": {"type": "integer", "maximum": 1, "minimum": 0}, "send_alerts": {"type": "boolean"}, "warning_days": {"type": "integer", "maximum": 30, "minimum": 1}}}, "domain.InventoryCheckRequest": {"type": "object", "properties": {"pantry_id": {"type": "string"}}}, "domain.LoginCredentials": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 8}}}, "domain.NotificationChannel": {"type": "string", "enum": ["email", "telegram", "supabase", "webhook", "in_app"], "x-enum-varnames": ["NotificationChannelEmail", "NotificationChannelTelegram", "NotificationChannelSupabase", "NotificationChannelWebhook", "NotificationChannelInApp"]}, "domain.PantryShoppingListStats": {"type": "object", "properties": {"active_shopping_lists": {"type": "integer"}, "archived_shopping_lists": {"type": "integer"}, "completed_shopping_lists": {"type": "integer"}, "overall_completion_rate": {"type": "number"}, "pantry_id": {"type": "string"}, "total_items": {"type": "integer"}, "total_purchased_items": {"type": "integer"}, "total_shopping_lists": {"type": "integer"}}}, "domain.Purchase": {"type": "object", "properties": {"created_at": {"type": "string"}, "currency": {"type": "string"}, "deleted_at": {"type": "string"}, "id": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/definitions/domain.PurchaseItem"}}, "notes": {"type": "string"}, "pantry_id": {"type": "string"}, "purchase_date": {"type": "string"}, "purchased_by_user_id": {"type": "string"}, "receipt_image_url": {"type": "string"}, "store_id": {"type": "string"}, "store_name": {"type": "string"}, "total_amount": {"type": "number"}, "updated_at": {"type": "string"}}}, "domain.PurchaseItem": {"type": "object", "properties": {"created_at": {"type": "string"}, "deleted_at": {"type": "string"}, "id": {"type": "string"}, "notes": {"type": "string"}, "price_per_unit": {"type": "number"}, "product_variant_id": {"type": "string"}, "purchase_id": {"type": "string"}, "quantity_bought": {"type": "number"}, "total_price": {"type": "number"}, "unit_of_measure_id": {"type": "string"}, "updated_at": {"type": "string"}}}, "domain.QuietHours": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "end_time": {"type": "string"}, "start_time": {"type": "string"}, "timezone": {"type": "string"}}}, "domain.RegisterCredentials": {"type": "object", "required": ["confirm_password", "email", "password", "username"], "properties": {"confirm_password": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string", "maxLength": 100}, "last_name": {"type": "string", "maxLength": 100}, "password": {"type": "string", "minLength": 8}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "domain.ScaleRecipeRequest": {"type": "object", "required": ["servings"], "properties": {"servings": {"type": "integer", "maximum": 100, "minimum": 1}}}, "domain.ShoppingList": {"type": "object", "properties": {"created_at": {"type": "string"}, "created_by": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "items": {"description": "Relationships", "type": "array", "items": {"$ref": "#/definitions/domain.ShoppingListItemEntity"}}, "name": {"type": "string"}, "pantry_id": {"type": "string"}, "status": {"$ref": "#/definitions/domain.ShoppingListStatus"}, "updated_at": {"type": "string"}}}, "domain.ShoppingListItemEntity": {"type": "object", "properties": {"created_at": {"type": "string"}, "free_text_name": {"type": "string"}, "id": {"type": "string"}, "is_purchased": {"type": "boolean"}, "notes": {"type": "string"}, "product_variant_id": {"type": "string"}, "purchased_at": {"type": "string"}, "quantity_desired": {"type": "number"}, "shopping_list_id": {"type": "string"}, "unit_of_measure_id": {"type": "string"}, "updated_at": {"type": "string"}}}, "domain.ShoppingListStats": {"type": "object", "properties": {"completion_percentage": {"type": "number"}, "purchased_items": {"type": "integer"}, "remaining_items": {"type": "integer"}, "shopping_list_id": {"type": "string"}, "total_items": {"type": "integer"}}}, "domain.ShoppingListStatus": {"type": "string", "enum": ["active", "completed", "archived"], "x-enum-varnames": ["ShoppingListStatusActive", "ShoppingListStatusCompleted", "ShoppingListStatusArchived"]}, "domain.Store": {"type": "object", "properties": {"address": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "created_at": {"type": "string"}, "deleted_at": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "phone_number": {"type": "string"}, "updated_at": {"type": "string"}, "website": {"type": "string"}}}, "domain.UpdateInventoryItemRequest": {"type": "object", "required": ["quantity"], "properties": {"expiration_date": {"type": "string"}, "location_id": {"type": "string"}, "notes": {"type": "string", "maxLength": 1000}, "purchase_date": {"type": "string"}, "purchase_price": {"type": "number", "minimum": 0}, "quantity": {"type": "number"}}}, "domain.UpdatePantryRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 500}, "name": {"type": "string", "maxLength": 100, "minLength": 1}}}, "domain.UpdateProfileRequest": {"type": "object", "properties": {"first_name": {"type": "string", "maxLength": 100}, "last_name": {"type": "string", "maxLength": 100}, "profile_picture_url": {"type": "string"}}}, "domain.UpdateRecipeRequest": {"type": "object", "properties": {"category": {"type": "string", "maxLength": 100}, "cook_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "cuisine": {"type": "string", "maxLength": 100}, "description": {"type": "string", "maxLength": 1000}, "difficulty": {"enum": ["easy", "medium", "hard", "expert"], "allOf": [{"$ref": "#/definitions/domain.DifficultyLevel"}]}, "ingredients": {"type": "array", "items": {"$ref": "#/definitions/domain.CreateRecipeIngredientRequest"}}, "instructions": {"type": "array", "items": {"$ref": "#/definitions/domain.CreateRecipeInstructionRequest"}}, "is_favorite": {"type": "boolean"}, "is_public": {"type": "boolean"}, "notes": {"type": "string", "maxLength": 2000}, "nutrition": {"$ref": "#/definitions/domain.CreateRecipeNutritionRequest"}, "prep_time": {"type": "integer", "maximum": 1440, "minimum": 0}, "servings": {"type": "integer", "maximum": 100, "minimum": 1}, "source": {"type": "string", "maxLength": 500}, "tags": {"type": "array", "items": {"type": "string"}}, "title": {"type": "string", "maxLength": 200, "minLength": 3}}}, "domain.UpdateShoppingListItemRequest": {"type": "object", "required": ["quantity_desired"], "properties": {"free_text_name": {"type": "string", "maxLength": 255, "minLength": 1}, "notes": {"type": "string", "maxLength": 500}, "product_variant_id": {"type": "string"}, "quantity_desired": {"type": "number"}, "unit_of_measure_id": {"type": "string"}}}, "domain.UpdateShoppingListRequest": {"type": "object", "required": ["name"], "properties": {"description": {"type": "string", "maxLength": 1000}, "name": {"type": "string", "maxLength": 255, "minLength": 1}}}, "handler.APIResponse": {"type": "object", "properties": {"data": {}, "error": {"$ref": "#/definitions/handler.ErrorInfo"}, "message": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": true}, "request_id": {"type": "string"}, "success": {"type": "boolean"}, "timestamp": {"type": "string"}}}, "handler.CategoryResponse": {"type": "object", "properties": {"created_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}, "description": {"type": "string", "example": "Dairy products"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440007"}, "name": {"type": "string", "example": "Dairy"}, "parent_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440008"}, "updated_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}}}, "handler.CreatePurchaseItemRequest": {"type": "object", "required": ["price_per_unit", "product_variant_id", "quantity_bought", "unit_of_measure_id"], "properties": {"notes": {"type": "string"}, "price_per_unit": {"type": "number", "minimum": 0}, "product_variant_id": {"type": "string"}, "quantity_bought": {"type": "number", "minimum": 0}, "unit_of_measure_id": {"type": "string"}}}, "handler.CreatePurchaseRequest": {"type": "object", "required": ["currency", "items", "pantry_id", "purchase_date", "total_amount"], "properties": {"currency": {"type": "string"}, "items": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/handler.CreatePurchaseItemRequest"}}, "notes": {"type": "string"}, "pantry_id": {"type": "string"}, "purchase_date": {"type": "string"}, "receipt_image_url": {"type": "string"}, "store_id": {"type": "string"}, "store_name": {"type": "string"}, "total_amount": {"type": "number", "minimum": 0}}}, "handler.ErrorInfo": {"type": "object", "properties": {"code": {"type": "string"}, "details": {"type": "object", "additionalProperties": true}, "message": {"type": "string"}}}, "handler.InventoryItemResponse": {"type": "object", "properties": {"category_name": {"type": "string", "example": "Dairy"}, "created_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}, "expiration_date": {"type": "string", "example": "2024-02-15"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440002"}, "location": {"$ref": "#/definitions/handler.PantryLocationResponse"}, "location_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440005"}, "location_name": {"type": "string", "example": "<PERSON>"}, "notes": {"type": "string", "example": "Organic brand"}, "pantry_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440001"}, "pantry_name": {"type": "string", "example": "Main Kitchen"}, "product_brand": {"type": "string", "example": "Organic Valley"}, "product_name": {"type": "string", "example": "Organic Milk"}, "product_variant": {"description": "Optional nested data (for backward compatibility)", "allOf": [{"$ref": "#/definitions/handler.ProductVariantResponse"}]}, "product_variant_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440003"}, "purchase_date": {"type": "string", "example": "2024-01-15"}, "purchase_price": {"type": "number", "example": 12.99}, "quantity": {"type": "number", "example": 5}, "status": {"type": "string", "example": "fresh"}, "unit_name": {"type": "string", "example": "pieces"}, "unit_of_measure": {"$ref": "#/definitions/handler.UnitOfMeasureResponse"}, "unit_of_measure_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440004"}, "unit_symbol": {"type": "string", "example": "pcs"}, "updated_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}, "variant_image_url": {"type": "string", "example": "https://example.com/images/milk-1l.jpg"}, "variant_name": {"type": "string", "example": "1 Liter"}}}, "handler.PaginatedResponse": {"type": "object", "properties": {"data": {}, "error": {"$ref": "#/definitions/handler.ErrorInfo"}, "message": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": true}, "pagination": {"$ref": "#/definitions/handler.PaginationMeta"}, "request_id": {"type": "string"}, "success": {"type": "boolean"}, "timestamp": {"type": "string"}}}, "handler.PaginationMeta": {"type": "object", "properties": {"has_next": {"type": "boolean"}, "has_prev": {"type": "boolean"}, "limit": {"type": "integer"}, "page": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "handler.PantryLocationResponse": {"type": "object", "properties": {"created_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}, "description": {"type": "string", "example": "Main refrigerator"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440005"}, "name": {"type": "string", "example": "<PERSON>"}, "pantry_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440001"}, "updated_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}}}, "handler.ProductResponse": {"type": "object", "properties": {"brand": {"type": "string", "example": "Organic Valley"}, "category": {"$ref": "#/definitions/handler.CategoryResponse"}, "category_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440007"}, "created_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}, "description": {"type": "string", "example": "Fresh organic whole milk"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440006"}, "name": {"type": "string", "example": "Organic Milk"}, "updated_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}}}, "handler.ProductVariantResponse": {"type": "object", "properties": {"barcode": {"type": "string", "example": "1234567890123"}, "created_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}, "description": {"type": "string", "example": "1 liter bottle"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440003"}, "name": {"type": "string", "example": "1 Liter"}, "product": {"$ref": "#/definitions/handler.ProductResponse"}, "product_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440006"}, "size": {"type": "number", "example": 1}, "size_unit": {"type": "string", "example": "L"}, "sku": {"type": "string", "example": "MILK-1L-001"}, "updated_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}}}, "handler.UnitOfMeasureResponse": {"type": "object", "properties": {"base_unit_id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440009"}, "conversion_factor": {"type": "number", "example": 1}, "created_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}, "id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440004"}, "name": {"type": "string", "example": "pieces"}, "symbol": {"type": "string", "example": "pcs"}, "type": {"type": "string", "example": "count"}, "updated_at": {"type": "string", "example": "2024-01-15T10:30:00Z"}}}, "usecases.CreateStoreRequest": {"type": "object", "required": ["name"], "properties": {"address": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "name": {"type": "string"}, "phone_number": {"type": "string"}, "website": {"type": "string"}}}, "usecases.UpdateStoreRequest": {"type": "object", "required": ["name"], "properties": {"address": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "name": {"type": "string"}, "phone_number": {"type": "string"}, "website": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}