-- Add new products
INSERT INTO products (name, description, category_id, brand)
SELECT 'Bread', 'Fresh bread', category_id, 'Wonder Bakery'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (name, description, category_id, brand)
SELECT 'Eggs', 'Fresh eggs', category_id, 'Happy Farm'
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (name, description, category_id, brand)
SELECT 'Chicken', 'Fresh chicken', category_id, 'Fresh Poultry'
FROM categories WHERE name = 'Meat & Seafood';

-- Add new product variants
INSERT INTO product_variants (product_id, name, description, packaging_type, default_unit_of_measure_id)
SELECT 
    p.product_id,
    'Wonder Bakery Whole Wheat Bread',
    'Nutritious whole wheat bread',
    'single',
    u.unit_id
FROM products p
CROSS JOIN units_of_measure u
WHERE p.name = 'Bread' AND u.symbol = 'pc';

INSERT INTO product_variants (product_id, name, description, packaging_type, default_unit_of_measure_id)
SELECT 
    p.product_id,
    'Happy Farm Free Range Eggs (12pcs)',
    'Farm fresh free range eggs',
    'multi-pack',
    u.unit_id
FROM products p
CROSS JOIN units_of_measure u
WHERE p.name = 'Eggs' AND u.symbol = 'dz';

INSERT INTO product_variants (product_id, name, description, packaging_type, default_unit_of_measure_id)
SELECT 
    p.product_id,
    'Fresh Poultry Whole Chicken',
    'Fresh whole chicken',
    'single',
    u.unit_id
FROM products p
CROSS JOIN units_of_measure u
WHERE p.name = 'Chicken' AND u.symbol = 'kg';

-- Add inventory items for new products
INSERT INTO inventory_items (
    pantry_id,
    location_id,
    product_variant_id,
    quantity,
    unit_of_measure_id,
    purchase_date,
    expiration_date,
    purchase_price
)
SELECT
    p.pantry_id,
    pl.pantry_location_id,
    pv.variant_id,
    2.0,
    u.unit_id,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '5 days',
    25000
FROM pantries p
JOIN pantry_locations pl ON pl.name = 'Kitchen Cabinet'
JOIN product_variants pv ON pv.name LIKE '%Bread%'
JOIN units_of_measure u ON u.symbol = 'pc'
WHERE p.name = 'Home Pantry';

INSERT INTO inventory_items (
    pantry_id,
    location_id,
    product_variant_id,
    quantity,
    unit_of_measure_id,
    purchase_date,
    expiration_date,
    purchase_price
)
SELECT
    p.pantry_id,
    pl.pantry_location_id,
    pv.variant_id,
    2.0,
    u.unit_id,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '14 days',
    35000
FROM pantries p
JOIN pantry_locations pl ON pl.name = 'Refrigerator'
JOIN product_variants pv ON pv.name LIKE '%Eggs%'
JOIN units_of_measure u ON u.symbol = 'dz'
WHERE p.name = 'Home Pantry';

INSERT INTO inventory_items (
    pantry_id,
    location_id,
    product_variant_id,
    quantity,
    unit_of_measure_id,
    purchase_date,
    expiration_date,
    purchase_price
)
SELECT
    p.pantry_id,
    pl.pantry_location_id,
    pv.variant_id,
    1.5,
    u.unit_id,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '3 days',
    45000
FROM pantries p
JOIN pantry_locations pl ON pl.name = 'Freezer'
JOIN product_variants pv ON pv.name LIKE '%Chicken%'
JOIN units_of_measure u ON u.symbol = 'kg'
WHERE p.name = 'Home Pantry';