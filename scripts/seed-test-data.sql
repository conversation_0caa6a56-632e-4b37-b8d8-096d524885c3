-- Create a pantry for the test user
INSERT INTO pantries (name, description, owner_user_id)
SELECT 'Home Pantry', 'My home pantry', id FROM users WHERE username = 'sugeng';

-- Add pantry membership for the owner
INSERT INTO pantry_memberships (pantry_id, user_id, role, status)
SELECT p.pantry_id, u.id, 'owner', 'active'
FROM pantries p
JOIN users u ON u.username = 'sugeng'
WHERE p.name = 'Home Pantry';

-- Add pantry locations
INSERT INTO pantry_locations (pantry_id, name, description)
SELECT p.pantry_id, 'Kitchen Cabinet', 'Main kitchen storage'
FROM pantries p WHERE p.name = 'Home Pantry';

INSERT INTO pantry_locations (pantry_id, name, description)
SELECT p.pantry_id, 'Refrigerator', 'Cold storage'
FROM pantries p WHERE p.name = 'Home Pantry';

INSERT INTO pantry_locations (pantry_id, name, description)
SELECT p.pantry_id, 'Freezer', 'Frozen storage'
FROM pantries p WHERE p.name = 'Home Pantry';

-- Add some sample products
INSERT INTO products (name, description, category_id, brand)
SELECT 'Rice', 'White rice', category_id, 'Golden Farm'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (name, description, category_id, brand)
SELECT 'Milk', 'Fresh milk', category_id, 'Farm Fresh'
FROM categories WHERE name = 'Dairy & Eggs';

-- Add product variants
INSERT INTO product_variants (product_id, name, description, packaging_type, default_unit_of_measure_id)
SELECT 
    p.product_id,
    'Golden Farm Premium Rice 5kg',
    'Premium quality white rice',
    'single',
    u.unit_id
FROM products p
CROSS JOIN units_of_measure u
WHERE p.name = 'Rice' AND u.symbol = 'kg';

INSERT INTO product_variants (product_id, name, description, packaging_type, default_unit_of_measure_id)
SELECT 
    p.product_id,
    'Farm Fresh Full Cream Milk 1L',
    'Fresh full cream milk',
    'single',
    u.unit_id
FROM products p
CROSS JOIN units_of_measure u
WHERE p.name = 'Milk' AND u.symbol = 'L';

-- Add inventory items
INSERT INTO inventory_items (
    pantry_id,
    location_id,
    product_variant_id,
    quantity,
    unit_of_measure_id,
    purchase_date,
    expiration_date,
    purchase_price
)
SELECT
    p.pantry_id,
    pl.pantry_location_id,
    pv.variant_id,
    5.0,
    u.unit_id,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '1 year',
    50000
FROM pantries p
JOIN pantry_locations pl ON pl.name = 'Kitchen Cabinet'
JOIN product_variants pv ON pv.name LIKE '%Rice%'
JOIN units_of_measure u ON u.symbol = 'kg'
WHERE p.name = 'Home Pantry';

INSERT INTO inventory_items (
    pantry_id,
    location_id,
    product_variant_id,
    quantity,
    unit_of_measure_id,
    purchase_date,
    expiration_date,
    purchase_price
)
SELECT
    p.pantry_id,
    pl.pantry_location_id,
    pv.variant_id,
    2.0,
    u.unit_id,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '7 days',
    15000
FROM pantries p
JOIN pantry_locations pl ON pl.name = 'Refrigerator'
JOIN product_variants pv ON pv.name LIKE '%Milk%'
JOIN units_of_measure u ON u.symbol = 'L'
WHERE p.name = 'Home Pantry';

-- Add a usage log for rice
INSERT INTO usage_logs (
    inventory_item_id,
    pantry_id,
    user_id,
    quantity_used,
    unit_of_measure_id,
    notes
)
SELECT
    i.item_id,
    i.pantry_id,
    u.id,
    0.5,
    uom.unit_id,
    'Used for lunch'
FROM inventory_items i
JOIN users u ON u.username = 'sugeng'
JOIN units_of_measure uom ON uom.symbol = 'kg'
JOIN product_variants pv ON pv.variant_id = i.product_variant_id
WHERE pv.name LIKE '%Rice%';