#!/bin/bash

# Pantry Pal API Testing Script
# This script demonstrates the implemented API endpoints

BASE_URL="http://localhost:8080"
API_URL="$BASE_URL/api/v1"

echo "🧪 Testing Pantry Pal API"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Test 1: Health Check
echo
print_info "Testing health endpoint..."
response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$BASE_URL/health")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Health check passed"
    echo "Response: $(cat /tmp/health_response.json | jq .)"
else
    print_status 1 "Health check failed (HTTP $http_code)"
    exit 1
fi

# Test 2: User Registration
echo
print_info "Testing user registration..."
registration_data='{
    "username": "testuser123",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "confirm_password": "SecurePassword123!",
    "first_name": "Test",
    "last_name": "User"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/register_response.json \
    -H "Content-Type: application/json" \
    -d "$registration_data" \
    "$API_URL/auth/register")
http_code="${response: -3}"

if [ "$http_code" = "201" ]; then
    print_status 0 "User registration successful"
    # Extract access token for subsequent requests
    access_token=$(cat /tmp/register_response.json | jq -r '.data.access_token')
    echo "Access token: ${access_token:0:20}..."
else
    print_status 1 "User registration failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/register_response.json | jq .)"
fi

# Test 3: User Login
echo
print_info "Testing user login..."
login_data='{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/login_response.json \
    -H "Content-Type: application/json" \
    -d "$login_data" \
    "$API_URL/auth/login")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "User login successful"
    # Update access token
    access_token=$(cat /tmp/login_response.json | jq -r '.data.access_token')
    echo "New access token: ${access_token:0:20}..."
else
    print_status 1 "User login failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/login_response.json | jq .)"
fi

# Test 4: Get User Profile (Protected Route)
echo
print_info "Testing get user profile (protected route)..."
response=$(curl -s -w "%{http_code}" -o /tmp/profile_response.json \
    -H "Authorization: Bearer $access_token" \
    "$API_URL/users/profile")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Get user profile successful"
    echo "User profile: $(cat /tmp/profile_response.json | jq '.data')"
else
    print_status 1 "Get user profile failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/profile_response.json | jq .)"
fi

# Test 5: Update User Profile
echo
print_info "Testing update user profile..."
update_data='{
    "first_name": "Updated",
    "last_name": "Name",
    "profile_picture_url": "https://example.com/avatar.jpg"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/update_response.json \
    -X PUT \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $access_token" \
    -d "$update_data" \
    "$API_URL/users/profile")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Update user profile successful"
    echo "Updated profile: $(cat /tmp/update_response.json | jq '.data')"
else
    print_status 1 "Update user profile failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/update_response.json | jq .)"
fi

# Test 6: Test Authentication Required
echo
print_info "Testing authentication requirement..."
response=$(curl -s -w "%{http_code}" -o /tmp/unauth_response.json \
    "$API_URL/users/profile")
http_code="${response: -3}"

if [ "$http_code" = "401" ]; then
    print_status 0 "Authentication requirement working correctly"
else
    print_status 1 "Authentication requirement not working (expected 401, got $http_code)"
fi

# Test 7: Test Invalid Credentials
echo
print_info "Testing invalid credentials..."
invalid_login='{
    "email": "<EMAIL>",
    "password": "WrongPassword"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/invalid_login_response.json \
    -H "Content-Type: application/json" \
    -d "$invalid_login" \
    "$API_URL/auth/login")
http_code="${response: -3}"

if [ "$http_code" = "401" ]; then
    print_status 0 "Invalid credentials handling working correctly"
else
    print_status 1 "Invalid credentials handling not working (expected 401, got $http_code)"
fi

# Test 8: Test Validation Errors
echo
print_info "Testing validation errors..."
invalid_registration='{
    "username": "ab",
    "email": "invalid-email",
    "password": "123",
    "confirm_password": "456"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/validation_response.json \
    -H "Content-Type: application/json" \
    -d "$invalid_registration" \
    "$API_URL/auth/register")
http_code="${response: -3}"

if [ "$http_code" = "400" ]; then
    print_status 0 "Validation error handling working correctly"
    echo "Validation errors: $(cat /tmp/validation_response.json | jq '.error.details')"
else
    print_status 1 "Validation error handling not working (expected 400, got $http_code)"
fi

# Test 9: Logout
echo
print_info "Testing user logout..."
response=$(curl -s -w "%{http_code}" -o /tmp/logout_response.json \
    -X POST \
    -H "Authorization: Bearer $access_token" \
    "$API_URL/auth/logout")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "User logout successful"
else
    print_status 1 "User logout failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/logout_response.json | jq .)"
fi

# Test 10: Create Pantry (Protected Route)
echo
print_info "Testing create pantry..."
pantry_data='{
    "name": "My Kitchen Pantry",
    "description": "Main kitchen storage area"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/create_pantry_response.json \
    -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $access_token" \
    -d "$pantry_data" \
    "$API_URL/pantries")
http_code="${response: -3}"

if [ "$http_code" = "201" ]; then
    print_status 0 "Create pantry successful"
    pantry_id=$(cat /tmp/create_pantry_response.json | jq -r '.data.id')
    echo "Pantry ID: $pantry_id"
else
    print_status 1 "Create pantry failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/create_pantry_response.json | jq .)"
fi

# Test 11: Get User Pantries
echo
print_info "Testing get user pantries..."
response=$(curl -s -w "%{http_code}" -o /tmp/pantries_response.json \
    -H "Authorization: Bearer $access_token" \
    "$API_URL/pantries")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Get pantries successful"
    echo "Pantries: $(cat /tmp/pantries_response.json | jq '.data | length') found"
else
    print_status 1 "Get pantries failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/pantries_response.json | jq .)"
fi

# Test 12: Get Specific Pantry
echo
print_info "Testing get specific pantry..."
response=$(curl -s -w "%{http_code}" -o /tmp/pantry_response.json \
    -H "Authorization: Bearer $access_token" \
    "$API_URL/pantries/$pantry_id")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Get pantry successful"
    echo "Pantry: $(cat /tmp/pantry_response.json | jq '.data.name')"
else
    print_status 1 "Get pantry failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/pantry_response.json | jq .)"
fi

# Test 13: Update Pantry
echo
print_info "Testing update pantry..."
update_pantry_data='{
    "name": "Updated Kitchen Pantry",
    "description": "Updated main kitchen storage area"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/update_pantry_response.json \
    -X PUT \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $access_token" \
    -d "$update_pantry_data" \
    "$API_URL/pantries/$pantry_id")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status 0 "Update pantry successful"
    echo "Updated name: $(cat /tmp/update_pantry_response.json | jq '.data.name')"
else
    print_status 1 "Update pantry failed (HTTP $http_code)"
    echo "Response: $(cat /tmp/update_pantry_response.json | jq .)"
fi

# Test 14: Test Pantry Authorization (Access without permission)
echo
print_info "Testing pantry authorization..."
# Create a second user to test authorization
second_user_data='{
    "username": "testuser456",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "confirm_password": "SecurePassword123!"
}'

response=$(curl -s -w "%{http_code}" -o /tmp/second_user_response.json \
    -H "Content-Type: application/json" \
    -d "$second_user_data" \
    "$API_URL/auth/register")
http_code="${response: -3}"

if [ "$http_code" = "201" ]; then
    second_user_token=$(cat /tmp/second_user_response.json | jq -r '.data.access_token')

    # Try to access first user's pantry with second user's token
    response=$(curl -s -w "%{http_code}" -o /tmp/unauthorized_pantry_response.json \
        -H "Authorization: Bearer $second_user_token" \
        "$API_URL/pantries/$pantry_id")
    http_code="${response: -3}"

    if [ "$http_code" = "403" ]; then
        print_status 0 "Pantry authorization working correctly"
    else
        print_status 1 "Pantry authorization not working (expected 403, got $http_code)"
    fi
else
    print_status 1 "Failed to create second user for authorization test"
fi

echo
echo "🎉 API testing completed!"
echo "=========================="
echo
echo "📋 Summary:"
echo "- Health check endpoint working"
echo "- User registration with validation"
echo "- User login with credential verification"
echo "- Protected routes with JWT authentication"
echo "- User profile management"
echo "- Multi-tenant pantry management"
echo "- Role-based authorization working"
echo "- Proper error handling and validation"
echo "- Standardized response format"
echo "- Request tracing with request IDs"

# Cleanup
rm -f /tmp/*_response.json

echo
echo "💡 Next steps:"
echo "- Add pantry member invitation system"
echo "- Implement pantry location management"
echo "- Add product catalog and inventory"
echo "- Create shopping lists functionality"
echo "- Add email notifications for invitations"
