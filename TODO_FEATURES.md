# Pantry Pal - TODO Features & Implementation Tasks

## 📊 **Implementation Progress Overview**

* **✅ Completed Features:** ~75% (Core business logic, inventory, recipes, shopping lists, notifications)
* **🚧 In Progress:** ~5% (Architecture ready, partial implementation)
* **❌ Not Started:** ~20% (Supporting systems, enhancements)

---

## 🔴 **HIGH PRIORITY - Core MVP Features**

### 1. Purchase History Management ❌

**Status:** Not Started  
**Priority:** Critical  
**Estimated Effort:** 3-4 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `Purchase` entity with transaction details
  + [ ] `PurchaseItem` entity for line items
  + [ ] `Store` entity for store management
  + [ ] Purchase-to-inventory linking logic

* [ ] **Repository Layer**
  + [ ] `PurchaseRepository` implementation
  + [ ] `PurchaseItemRepository` implementation  
  + [ ] `StoreRepository` implementation
  + [ ] Database migrations for purchase tables

* [ ] **Use Cases**
  + [ ] `CreatePurchase` use case
  + [ ] `AddPurchaseItems` use case
  + [ ] `LinkPurchaseToInventory` use case
  + [ ] `GetPurchaseHistory` use case
  + [ ] `ManageStores` use case

* [ ] **HTTP API**
  + [ ] Purchase creation endpoints
  + [ ] Purchase history retrieval
  + [ ] Store management endpoints
  + [ ] Purchase-to-inventory linking

#### **Requirements Reference:**

```
- Record purchase transactions (date, total, store, items bought)
- Link purchase items to inventory
- Manage frequently visited stores
```

### 2. Pantry-Specific Settings ❌

**Status:** Not Started  
**Priority:** High  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `PantrySettings` entity
  + [ ] Settings validation logic
  + [ ] Default settings initialization

* [ ] **Repository Layer**
  + [ ] `PantrySettingsRepository` implementation
  + [ ] Database migration for pantry_settings table

* [ ] **Use Cases**
  + [ ] `ConfigurePantrySettings` use case
  + [ ] `GetPantrySettings` use case
  + [ ] `UpdateLowStockThresholds` use case
  + [ ] `SetDefaultLocation` use case

* [ ] **HTTP API**
  + [ ] Settings CRUD endpoints
  + [ ] Threshold configuration endpoints

#### **Settings to Implement:**

* [ ] Low stock thresholds per product/category
* [ ] Default storage locations
* [ ] Preferred currency settings
* [ ] Preferred units of measure
* [ ] Notification preferences

### 3. Password Change Functionality ❌

**Status:** Placeholder Exists  
**Priority:** High  
**Estimated Effort:** 1 day

#### **Missing Components:**

* [ ] **Use Cases**
  + [ ] `ChangePassword` use case with current password verification
  + [ ] Password strength validation
  + [ ] Security event logging

* [ ] **HTTP API**
  + [ ] Complete password change handler implementation
  + [ ] Current password verification
  + [ ] New password validation

#### **Current Status:**

```go
// internal/infra/web/handler/user_handler.go:201
return ErrorResponse(c, errors.New(errors.ErrCodeInternalError, "Password change not yet implemented"))
```

### 4. Usage Tracking & Analytics ❌

**Status:** Partial (consumption exists, no detailed logs)  
**Priority:** High  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `UsageLog` entity for detailed consumption tracking
  + [ ] Usage analytics aggregation
  + [ ] Consumption pattern analysis

* [ ] **Repository Layer**
  + [ ] `UsageLogRepository` implementation
  + [ ] Database migration for usage_logs table
  + [ ] Analytics query methods

* [ ] **Use Cases**
  + [ ] `LogItemUsage` use case
  + [ ] `GetUsageHistory` use case
  + [ ] `AnalyzeConsumptionPatterns` use case
  + [ ] `PredictUsage` use case

* [ ] **HTTP API**
  + [ ] Usage logging endpoints
  + [ ] Usage history retrieval
  + [ ] Analytics endpoints

---

## 🟡 **MEDIUM PRIORITY - Enhanced Features**

### 5. Inventory Adjustments ❌

**Status:** Not Started  
**Priority:** Medium  
**Estimated Effort:** 2 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `InventoryAdjustment` entity
  + [ ] Adjustment types (spoilage, loss, manual_correction, transfer)
  + [ ] Adjustment validation logic

* [ ] **Repository Layer**
  + [ ] `InventoryAdjustmentRepository` implementation
  + [ ] Database migration for inventory_adjustments table

* [ ] **Use Cases**
  + [ ] `RecordAdjustment` use case
  + [ ] `GetAdjustmentHistory` use case
  + [ ] `RecordSpoilage` use case

* [ ] **HTTP API**
  + [ ] Adjustment recording endpoints
  + [ ] Adjustment history endpoints

### 6. Account Recovery System ❌

**Status:** Not Started  
**Priority:** Medium  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `PasswordResetToken` entity
  + [ ] Token generation and validation logic

* [ ] **Repository Layer**
  + [ ] `PasswordResetTokenRepository` implementation
  + [ ] Database migration for password reset tokens

* [ ] **Use Cases**
  + [ ] `RequestPasswordReset` use case
  + [ ] `ValidateResetToken` use case
  + [ ] `ResetPassword` use case

* [ ] **HTTP API**
  + [ ] Password reset request endpoint
  + [ ] Password reset confirmation endpoint

* [ ] **Email Integration**
  + [ ] Email service for reset links
  + [ ] Email templates

### 7. Idempotency Middleware Integration ❌

**Status:** Service Exists, Middleware Missing  
**Priority:** Medium  
**Estimated Effort:** 1 day

#### **Missing Components:**

* [ ] **Middleware Implementation**
  + [ ] Fiber middleware for idempotency
  + [ ] Integration with existing `IdempotencyService`
  + [ ] Header validation and processing

* [ ] **Configuration**
  + [ ] Idempotency configuration settings
  + [ ] TTL and cache settings

#### **Current Status:**

```go
// Service exists: internal/infra/redis/idempotency_service.go
// Missing: Middleware integration in HTTP layer
```

---

## 🟢 **LOW PRIORITY - Future Enhancements**

### 8. Advanced Notification Channels ❌

**Status:** Architecture Ready, Providers Not Implemented  
**Priority:** Low  
**Estimated Effort:** 3-5 days

#### **Missing Components:**

* [ ] **Email Provider**
  + [ ] SMTP configuration
  + [ ] Email templates
  + [ ] HTML/text email generation

* [ ] **SMS Provider** (Future)
  + [ ] SMS service integration
  + [ ] SMS templates

* [ ] **Push Notification Provider** (Future)
  + [ ] Push notification service
  + [ ] Device token management

#### **Current Status:**

```go
// Architecture exists: internal/infra/notification/providers/
// All providers have TODO placeholders
```

### 9. Casbin Authorization Migration ❌

**Status:** Custom System Works, Migration Optional  
**Priority:** Low  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Casbin Integration**
  + [ ] Casbin enforcer setup
  + [ ] Policy management
  + [ ] Migration from current authorization system

* [ ] **Policy Management**
  + [ ] Dynamic policy updates
  + [ ] Role-based policy definitions

#### **Current Status:**

```go
// Working alternative: internal/infra/auth/pantry_authorization_service.go
// Requirements specify Casbin but current system is functional
```

---

## 📋 **Implementation Checklist by Component**

### **Domain Layer Completeness**

* [x] User management
* [x] Pantry management  
* [x] Product catalog
* [x] Inventory tracking
* [x] Shopping lists
* [x] Recipe management
* [x] Notifications
* [ ] Purchase history
* [ ] Usage tracking
* [ ] Inventory adjustments
* [ ] Pantry settings

### **Repository Layer Completeness**

* [x] User repositories
* [x] Pantry repositories
* [x] Product repositories
* [x] Inventory repositories
* [x] Shopping list repositories
* [x] Recipe repositories
* [x] Notification repositories
* [ ] Purchase repositories
* [ ] Usage log repositories
* [ ] Adjustment repositories
* [ ] Settings repositories

### **Use Case Layer Completeness**

* [x] Authentication use cases
* [x] Pantry management use cases
* [x] Inventory use cases
* [x] Shopping list use cases
* [x] Recipe use cases
* [x] Expiration tracking use cases
* [ ] Purchase management use cases
* [ ] Usage analytics use cases
* [ ] Settings management use cases

### **HTTP API Completeness**

* [x] Authentication endpoints
* [x] User management endpoints
* [x] Pantry management endpoints
* [x] Product catalog endpoints
* [x] Inventory endpoints
* [x] Shopping list endpoints
* [x] Recipe endpoints
* [x] Expiration tracking endpoints
* [ ] Purchase history endpoints
* [ ] Usage tracking endpoints
* [ ] Settings management endpoints

---

## 🎯 **Next Sprint Recommendations**

### **Sprint 1: Core MVP Completion (1-2 weeks)**

1. Purchase History Management (3-4 days)
2. Pantry-Specific Settings (2-3 days)
3. Password Change Functionality (1 day)

### **Sprint 2: Enhanced Features (1 week)**

1. Usage Tracking & Analytics (2-3 days)
2. Inventory Adjustments (2 days)
3. Idempotency Middleware (1 day)

### **Sprint 3: Security & Recovery (1 week)**

1. Account Recovery System (2-3 days)
2. Advanced notification channels (3-5 days)

---

## 📈 **Progress Tracking**

**Last Updated:** December 19, 2024  
**Total Features:** 20  
**Completed:** 15 ✅  
**In Progress:** 1 🚧  
**Not Started:** 4 ❌  

**Completion Rate:** 75% ✅

---

## 🛠️ **Detailed Implementation Guides**

### **Purchase History Implementation Guide**

#### **Step 1: Domain Models**

```go
// internal/core/domain/purchase.go
type Purchase struct {
    ID              uuid.UUID
    PantryID        uuid.UUID
    PurchaseDate    time.Time
    TotalAmount     *float64
    Currency        string
    StoreID         *uuid.UUID
    StoreName       *string
    ReceiptImageURL *string
    PurchasedByUserID uuid.UUID
    Notes           *string
    Items           []PurchaseItem
    CreatedAt       time.Time
    UpdatedAt       time.Time
}

type PurchaseItem struct {
    ID                uuid.UUID
    PurchaseID        uuid.UUID
    ProductVariantID  uuid.UUID
    QuantityBought    float64
    UnitOfMeasureID   uuid.UUID
    PricePerUnit      float64
    TotalPrice        float64
    Notes             *string
}

type Store struct {
    ID          uuid.UUID
    Name        string
    Address     *string
    City        *string
    Country     *string
    PhoneNumber *string
    Website     *string
}
```

#### **Step 2: Repository Interfaces**

```go
// internal/core/domain/repositories.go
type PurchaseRepository interface {
    Create(ctx context.Context, purchase *Purchase) error
    GetByID(ctx context.Context, id uuid.UUID) (*Purchase, error)
    GetByPantryID(ctx context.Context, pantryID uuid.UUID, opts ListOptions) ([]*Purchase, int64, error)
    Update(ctx context.Context, purchase *Purchase) error
    Delete(ctx context.Context, id uuid.UUID) error
}

type StoreRepository interface {
    Create(ctx context.Context, store *Store) error
    GetByID(ctx context.Context, id uuid.UUID) (*Store, error)
    SearchByName(ctx context.Context, name string) ([]*Store, error)
    GetUserStores(ctx context.Context, userID uuid.UUID) ([]*Store, error)
}
```

#### **Step 3: Use Cases**

```go
// internal/core/usecases/purchase_usecase.go
type PurchaseUsecase struct {
    purchaseRepo    domain.PurchaseRepository
    storeRepo       domain.StoreRepository
    inventoryRepo   domain.InventoryItemRepository
    pantryAuthzService domain.PantryAuthorizationService
    logger          Logger
}

func (uc *PurchaseUsecase) CreatePurchase(ctx context.Context, userID uuid.UUID, req *CreatePurchaseRequest) (*domain.Purchase, error) {
    // 1. Validate pantry access
    // 2. Create purchase entity
    // 3. Add purchase items
    // 4. Optionally link to inventory
    // 5. Save to repository
}
```

### **Pantry Settings Implementation Guide**

#### **Step 1: Domain Model**

```go
// internal/core/domain/pantry_settings.go
type PantrySettings struct {
    ID                    uuid.UUID
    PantryID              uuid.UUID
    LowStockThresholds    map[uuid.UUID]float64 // ProductVariantID -> Threshold
    DefaultLocationID     *uuid.UUID
    PreferredCurrency     string
    PreferredUnits        map[string]uuid.UUID // UnitType -> UnitID
    NotificationSettings  NotificationSettings
    UpdatedByUserID       uuid.UUID
    CreatedAt            time.Time
    UpdatedAt            time.Time
}

type NotificationSettings struct {
    LowStockEnabled     bool
    ExpirationEnabled   bool
    EmailNotifications  bool
    InAppNotifications  bool
}
```

#### **Step 2: Use Cases**

```go
// internal/core/usecases/pantry_settings_usecase.go
func (uc *PantrySettingsUsecase) UpdateSettings(ctx context.Context, userID, pantryID uuid.UUID, req *UpdateSettingsRequest) (*domain.PantrySettings, error) {
    // 1. Check edit pantry permission
    // 2. Get or create settings
    // 3. Update settings
    // 4. Validate settings
    // 5. Save to repository
}
```

### **Password Change Implementation Guide**

#### **Step 1: Use Case**

```go
// internal/core/usecases/auth_usecase.go
func (uc *AuthUsecase) ChangePassword(ctx context.Context, userID uuid.UUID, req *ChangePasswordRequest) error {
    // 1. Get user by ID
    // 2. Verify current password
    // 3. Validate new password strength
    // 4. Hash new password
    // 5. Update user password
    // 6. Revoke all refresh tokens (force re-login)
    // 7. Log security event
}
```

#### **Step 2: HTTP Handler**

```go
// internal/infra/web/handler/user_handler.go
func (h *UserHandler) ChangePassword(c *fiber.Ctx) error {
    // Replace the TODO implementation
    // 1. Parse request
    // 2. Validate input
    // 3. Call use case
    // 4. Return success response
}
```

---

## 📝 **Database Migration Scripts Needed**

### **Purchase Tables**

```sql
-- migrations/000015_create_purchases_table.up.sql
CREATE TABLE purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(id) ON DELETE CASCADE,
    purchase_date TIMESTAMPTZ NOT NULL,
    total_amount DECIMAL(10, 2),
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    store_id UUID REFERENCES stores(id) ON DELETE SET NULL,
    store_name VARCHAR(255),
    receipt_image_url VARCHAR(500),
    purchased_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE purchase_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    purchase_id UUID NOT NULL REFERENCES purchases(id) ON DELETE CASCADE,
    product_variant_id UUID NOT NULL REFERENCES product_variants(id) ON DELETE RESTRICT,
    quantity_bought DECIMAL(10, 3) NOT NULL,
    unit_of_measure_id UUID NOT NULL REFERENCES unit_of_measures(id) ON DELETE RESTRICT,
    price_per_unit DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE stores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    address VARCHAR(500),
    city VARCHAR(100),
    country VARCHAR(100),
    phone_number VARCHAR(20),
    website VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### **Settings Table**

```sql
-- migrations/000016_create_pantry_settings_table.up.sql
CREATE TABLE pantry_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value JSONB,
    data_type VARCHAR(20) NOT NULL,
    updated_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (pantry_id, setting_key)
);
```

### **Usage Logs Table**

```sql
-- migrations/000017_create_usage_logs_table.up.sql
CREATE TABLE usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_item_id UUID NOT NULL REFERENCES inventory_items(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    quantity_used DECIMAL(10, 3) NOT NULL,
    unit_of_measure_id UUID NOT NULL REFERENCES unit_of_measures(id) ON DELETE RESTRICT,
    usage_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔄 **Integration Points**

### **Purchase → Inventory Integration**

* Link `PurchaseItem` to `InventoryItem` creation
* Auto-populate purchase price and date in inventory
* Track purchase history per inventory item

### **Settings → Inventory Integration**

* Use low stock thresholds in shopping list generation
* Apply default locations when adding inventory
* Use preferred units in quantity conversions

### **Usage Logs → Analytics Integration**

* Feed consumption data to shopping list algorithms
* Provide usage patterns for expiration predictions
* Support inventory forecasting features

---

## 🎯 **Success Criteria**

### **Purchase History**

* [ ] Can record complete purchase transactions
* [ ] Can link purchases to inventory items
* [ ] Can track spending per pantry
* [ ] Can manage store information

### **Pantry Settings**

* [ ] Can configure low stock thresholds
* [ ] Can set default locations and units
* [ ] Settings are applied throughout the system
* [ ] Settings are pantry-specific

### **Password Change**

* [ ] Secure current password verification
* [ ] Strong password validation
* [ ] Automatic token revocation
* [ ] Security event logging

### **Usage Tracking**

* [ ] Detailed consumption logging
* [ ] Usage history retrieval
* [ ] Consumption pattern analysis
* [ ] Integration with shopping list generation
