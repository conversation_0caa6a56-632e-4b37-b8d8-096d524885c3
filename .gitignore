# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/
dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage.html

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.tmp/

# Docker volumes
postgres_data/
redis_data/

# Build artifacts
*.tar.gz
*.zip

# Air live reload
.air.toml
tmp/

# Migration files (keep the directory structure but ignore generated files)
# internal/infra/persistence/migrations/*.sql

# Test artifacts
*.test
*.prof

# Local development files
local/
dev/

# Backup files
*.bak
*.backup

# Editor backup files
*~
.#*
\#*#

# Node modules (if any frontend tools are added later)
node_modules/

# Python cache (if any Python tools are used)
__pycache__/
*.py[cod]
*$py.class

# Local configuration overrides
config.local.yaml
config.dev.yaml
config.prod.yaml
